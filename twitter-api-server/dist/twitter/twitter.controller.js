"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TwitterController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwitterController = void 0;
const common_1 = require("@nestjs/common");
const twitter_service_1 = require("./twitter.service");
const database_service_1 = require("../database/database.service");
const post_tweet_dto_1 = require("./dto/post-tweet.dto");
let TwitterController = TwitterController_1 = class TwitterController {
    twitterService;
    databaseService;
    logger = new common_1.Logger(TwitterController_1.name);
    constructor(twitterService, databaseService) {
        this.twitterService = twitterService;
        this.databaseService = databaseService;
    }
    async postTweet(postTweetDto) {
        try {
            this.logger.log(`Posting tweet: ${postTweetDto.content.substring(0, 50)}...`);
            const tweet = await this.twitterService.postTweet(postTweetDto.content);
            return {
                success: true,
                tweet: {
                    id: tweet.id,
                    text: tweet.text,
                    created_at: tweet.created_at
                }
            };
        }
        catch (error) {
            this.logger.error('Error posting tweet:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to post tweet'
            };
        }
    }
    async getTwitterStats() {
        try {
            const userData = await this.twitterService.getCurrentUser();
            const engagementMetrics = await this.twitterService.calculateEngagementMetrics();
            return {
                followers_count: userData.followers_count,
                following_count: userData.following_count,
                total_tweets: userData.tweet_count,
                total_impressions: engagementMetrics.totalImpressions,
                total_engagements: engagementMetrics.totalEngagements,
                engagement_rate: engagementMetrics.engagementRate,
                username: userData.username,
                name: userData.name,
                verified: userData.verified,
                profile_image_url: userData.profile_image_url
            };
        }
        catch (error) {
            this.logger.error('Error fetching Twitter stats:', error);
            throw new common_1.HttpException('Failed to fetch Twitter stats', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async refreshTwitterStats() {
        try {
            const userData = await this.twitterService.getCurrentUser();
            const totalImpressions = await this.twitterService.getTotalImpressions();
            const engagementMetrics = await this.twitterService.calculateEngagementMetrics();
            await this.databaseService.addAnalytics({
                followers_count: userData.followers_count,
                following_count: userData.following_count,
                total_tweets: userData.tweet_count,
                total_impressions: totalImpressions,
                total_engagements: engagementMetrics.totalEngagements
            });
            return { success: true };
        }
        catch (error) {
            this.logger.error('Error refreshing Twitter stats:', error);
            throw new common_1.HttpException('Failed to refresh Twitter stats', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAuthStatus() {
        try {
            return await this.twitterService.getTokenStatus();
        }
        catch (error) {
            this.logger.error('Error getting auth status:', error);
            throw new common_1.HttpException('Failed to get authentication status', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async healthCheck() {
        try {
            const databaseHealth = await this.databaseService.healthCheck();
            const hasTokens = await this.twitterService.hasValidTokens();
            return {
                status: databaseHealth && hasTokens ? 'healthy' : 'degraded',
                database: databaseHealth,
                tokens: hasTokens
            };
        }
        catch (error) {
            this.logger.error('Health check failed:', error);
            return {
                status: 'unhealthy',
                database: false,
                tokens: false
            };
        }
    }
};
exports.TwitterController = TwitterController;
__decorate([
    (0, common_1.Post)('post'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [post_tweet_dto_1.PostTweetDto]),
    __metadata("design:returntype", Promise)
], TwitterController.prototype, "postTweet", null);
__decorate([
    (0, common_1.Get)('stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TwitterController.prototype, "getTwitterStats", null);
__decorate([
    (0, common_1.Post)('stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TwitterController.prototype, "refreshTwitterStats", null);
__decorate([
    (0, common_1.Get)('auth/status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TwitterController.prototype, "getAuthStatus", null);
__decorate([
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TwitterController.prototype, "healthCheck", null);
exports.TwitterController = TwitterController = TwitterController_1 = __decorate([
    (0, common_1.Controller)('twitter'),
    __metadata("design:paramtypes", [twitter_service_1.TwitterService,
        database_service_1.DatabaseService])
], TwitterController);
//# sourceMappingURL=twitter.controller.js.map