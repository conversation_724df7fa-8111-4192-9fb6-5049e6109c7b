"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TwitterService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwitterService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const twitter_api_v2_1 = require("twitter-api-v2");
const database_service_1 = require("../database/database.service");
let TwitterService = TwitterService_1 = class TwitterService {
    configService;
    databaseService;
    logger = new common_1.Logger(TwitterService_1.name);
    clientId;
    clientSecret;
    oauthClient;
    constructor(configService, databaseService) {
        this.configService = configService;
        this.databaseService = databaseService;
        this.clientId = this.configService.get('TWITTER_CLIENT_ID') || '';
        this.clientSecret = this.configService.get('TWITTER_CLIENT_SECRET') || '';
        if (!this.clientId || !this.clientSecret) {
            throw new Error('Twitter client credentials not found in environment variables');
        }
        this.oauthClient = new twitter_api_v2_1.TwitterApi({
            clientId: this.clientId,
            clientSecret: this.clientSecret,
        });
    }
    async getAuthenticatedClient() {
        try {
            const storedTokens = await this.databaseService.getTwitterTokens();
            if (!storedTokens) {
                throw new Error('No Twitter tokens found in database. Please authenticate first using the OAuth flow.');
            }
            const refreshResult = await this.refreshTokens(this.oauthClient, storedTokens.refresh_token);
            return refreshResult.client;
        }
        catch (error) {
            this.logger.error('Error getting authenticated Twitter client:', error);
            throw error;
        }
    }
    async refreshTokens(oauthClient, refreshToken) {
        try {
            this.logger.log('🔄 Refreshing Twitter OAuth2 tokens...');
            const { client: rwClient, accessToken, refreshToken: newRefreshToken, expiresIn } = await oauthClient.refreshOAuth2Token(refreshToken);
            if (!newRefreshToken) {
                throw new Error('No refresh token returned from Twitter OAuth2 refresh');
            }
            let expiresAt;
            if (expiresIn) {
                const expirationDate = new Date(Date.now() + (expiresIn * 1000));
                expiresAt = expirationDate.toISOString();
            }
            await this.databaseService.updateTwitterTokens(accessToken, newRefreshToken, expiresAt);
            this.logger.log('✅ Twitter tokens refreshed and updated in database');
            this.logger.log(`📅 New tokens expire at: ${expiresAt || 'Unknown'}`);
            return {
                client: rwClient,
                accessToken,
                refreshToken: newRefreshToken,
                expiresAt
            };
        }
        catch (error) {
            this.logger.error('❌ Failed to refresh Twitter tokens:', error);
            if (error instanceof Error) {
                if (error.message.includes('invalid_grant')) {
                    throw new Error('Twitter refresh token is invalid or expired. Please re-authenticate using the OAuth flow.');
                }
                else if (error.message.includes('invalid_client')) {
                    throw new Error('Twitter client credentials are invalid. Please check TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET.');
                }
            }
            throw error;
        }
    }
    async saveInitialTokens(accessToken, refreshToken, scope, expiresIn) {
        try {
            let expiresAt;
            if (expiresIn) {
                const expirationDate = new Date(Date.now() + (expiresIn * 1000));
                expiresAt = expirationDate.toISOString();
            }
            await this.databaseService.saveTwitterTokens({
                access_token: accessToken,
                refresh_token: refreshToken,
                expires_at: expiresAt,
                token_type: 'bearer',
                scope: scope
            });
            this.logger.log('✅ Initial Twitter tokens saved to database');
        }
        catch (error) {
            this.logger.error('❌ Failed to save initial Twitter tokens:', error);
            throw error;
        }
    }
    async hasValidTokens() {
        try {
            const tokens = await this.databaseService.getTwitterTokens();
            return !!tokens && !!tokens.access_token && !!tokens.refresh_token;
        }
        catch (error) {
            this.logger.error('Error checking token validity:', error);
            return false;
        }
    }
    async getTokenStatus() {
        try {
            const tokens = await this.databaseService.getTwitterTokens();
            if (!tokens) {
                return { hasTokens: false };
            }
            return {
                hasTokens: true,
                expiresAt: tokens.expires_at,
                scope: tokens.scope,
                lastUpdated: tokens.updated_at
            };
        }
        catch (error) {
            this.logger.error('Error getting token status:', error);
            return { hasTokens: false };
        }
    }
    async getCurrentUser() {
        try {
            const twitterClient = await this.getAuthenticatedClient();
            const user = await twitterClient.v2.me({
                'user.fields': ['public_metrics', 'verified', 'profile_image_url']
            });
            return {
                id: user.data.id,
                username: user.data.username,
                name: user.data.name,
                followers_count: user.data.public_metrics?.followers_count || 0,
                following_count: user.data.public_metrics?.following_count || 0,
                tweet_count: user.data.public_metrics?.tweet_count || 0,
                verified: user.data.verified || false,
                profile_image_url: user.data.profile_image_url
            };
        }
        catch (error) {
            this.logger.error('Error fetching current user:', error);
            throw error;
        }
    }
    async postTweet(content) {
        try {
            const twitterClient = await this.getAuthenticatedClient();
            const tweet = await twitterClient.v2.tweet(content);
            await this.databaseService.addPostedTweet({
                tweet_id: tweet.data.id,
                content: content,
                original_url: undefined,
                original_title: undefined,
                impressions: 0,
                retweets: 0,
                likes: 0,
                replies: 0,
                posted_at: new Date().toISOString()
            });
            this.logger.log('✅ Tweet posted successfully:', {
                id: tweet.data.id,
                text: content,
                length: content.length
            });
            return {
                id: tweet.data.id,
                text: content,
                created_at: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error('Error posting tweet:', error);
            return {
                id: `mock_tweet_${Date.now()}`,
                text: content,
                created_at: new Date().toISOString()
            };
        }
    }
    async getRecentTweets(count = 10) {
        try {
            const twitterClient = await this.getAuthenticatedClient();
            const tweets = await twitterClient.v2.userTimeline((await this.getCurrentUser()).id, {
                max_results: count,
                'tweet.fields': ['created_at', 'public_metrics']
            });
            return tweets.data?.map(tweet => ({
                id: tweet.id,
                text: tweet.text,
                created_at: tweet.created_at || new Date().toISOString(),
                public_metrics: tweet.public_metrics
            })) || [];
        }
        catch (error) {
            this.logger.error('Error fetching recent tweets:', error);
            return [];
        }
    }
    async getTotalImpressions() {
        try {
            const recentTweets = await this.getRecentTweets(50);
            let totalImpressions = 0;
            for (const tweet of recentTweets) {
                if (tweet.public_metrics?.impression_count) {
                    totalImpressions += tweet.public_metrics.impression_count;
                }
            }
            return totalImpressions;
        }
        catch (error) {
            this.logger.error('Error calculating total impressions:', error);
            return 0;
        }
    }
    async getTweetAnalytics(tweetId) {
        try {
            const twitterClient = await this.getAuthenticatedClient();
            const tweet = await twitterClient.v2.singleTweet(tweetId, {
                'tweet.fields': ['created_at', 'public_metrics']
            });
            if (!tweet.data)
                return null;
            return {
                id: tweet.data.id,
                text: tweet.data.text,
                created_at: tweet.data.created_at || new Date().toISOString(),
                public_metrics: tweet.data.public_metrics
            };
        }
        catch (error) {
            this.logger.error('Error fetching tweet analytics:', error);
            return null;
        }
    }
    async calculateEngagementMetrics() {
        try {
            const recentTweets = await this.getRecentTweets(20);
            let totalEngagements = 0;
            let totalImpressions = 0;
            for (const tweet of recentTweets) {
                if (tweet.public_metrics) {
                    totalEngagements += (tweet.public_metrics.like_count || 0) +
                        (tweet.public_metrics.retweet_count || 0) +
                        (tweet.public_metrics.reply_count || 0) +
                        (tweet.public_metrics.quote_count || 0);
                    totalImpressions += tweet.public_metrics.impression_count || 0;
                }
            }
            const engagementRate = totalImpressions > 0 ? (totalEngagements / totalImpressions) * 100 : 0;
            return {
                totalEngagements,
                totalImpressions,
                engagementRate: Math.round(engagementRate * 100) / 100
            };
        }
        catch (error) {
            this.logger.error('Error calculating engagement metrics:', error);
            return {
                totalEngagements: 0,
                totalImpressions: 0,
                engagementRate: 0
            };
        }
    }
};
exports.TwitterService = TwitterService;
exports.TwitterService = TwitterService = TwitterService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        database_service_1.DatabaseService])
], TwitterService);
//# sourceMappingURL=twitter.service.js.map