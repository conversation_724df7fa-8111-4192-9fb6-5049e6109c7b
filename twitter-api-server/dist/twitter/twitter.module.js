"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwitterModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const twitter_service_1 = require("./twitter.service");
const twitter_controller_1 = require("./twitter.controller");
const database_module_1 = require("../database/database.module");
let TwitterModule = class TwitterModule {
};
exports.TwitterModule = TwitterModule;
exports.TwitterModule = TwitterModule = __decorate([
    (0, common_1.Module)({
        imports: [config_1.ConfigModule, database_module_1.DatabaseModule],
        providers: [twitter_service_1.TwitterService],
        controllers: [twitter_controller_1.TwitterController],
        exports: [twitter_service_1.TwitterService]
    })
], TwitterModule);
//# sourceMappingURL=twitter.module.js.map