export declare class TwitterStatsResponseDto {
    followers_count: number;
    following_count: number;
    total_tweets: number;
    total_impressions: number;
    total_engagements: number;
    engagement_rate: number;
    username: string;
    name: string;
    verified: boolean;
    profile_image_url?: string;
}
export declare class PostTweetResponseDto {
    success: boolean;
    tweet?: {
        id: string;
        text: string;
        created_at: string;
    };
    error?: string;
}
export declare class TokenStatusResponseDto {
    hasTokens: boolean;
    expiresAt?: string;
    scope?: string;
    lastUpdated?: string;
}
