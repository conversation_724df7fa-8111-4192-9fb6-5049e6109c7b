"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenStatusResponseDto = exports.PostTweetResponseDto = exports.TwitterStatsResponseDto = void 0;
class TwitterStatsResponseDto {
    followers_count;
    following_count;
    total_tweets;
    total_impressions;
    total_engagements;
    engagement_rate;
    username;
    name;
    verified;
    profile_image_url;
}
exports.TwitterStatsResponseDto = TwitterStatsResponseDto;
class PostTweetResponseDto {
    success;
    tweet;
    error;
}
exports.PostTweetResponseDto = PostTweetResponseDto;
class TokenStatusResponseDto {
    hasTokens;
    expiresAt;
    scope;
    lastUpdated;
}
exports.TokenStatusResponseDto = TokenStatusResponseDto;
//# sourceMappingURL=twitter-response.dto.js.map