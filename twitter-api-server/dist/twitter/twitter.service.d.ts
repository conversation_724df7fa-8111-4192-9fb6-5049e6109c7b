import { ConfigService } from '@nestjs/config';
import { DatabaseService } from '../database/database.service';
import { TwitterUserData, TweetData } from './interfaces/twitter.interface';
export declare class TwitterService {
    private configService;
    private databaseService;
    private readonly logger;
    private clientId;
    private clientSecret;
    private oauthClient;
    constructor(configService: ConfigService, databaseService: DatabaseService);
    getAuthenticatedClient(): Promise<any>;
    private refreshTokens;
    saveInitialTokens(accessToken: string, refreshToken: string, scope?: string, expiresIn?: number): Promise<void>;
    hasValidTokens(): Promise<boolean>;
    getTokenStatus(): Promise<{
        hasTokens: boolean;
        expiresAt?: string;
        scope?: string;
        lastUpdated?: string;
    }>;
    getCurrentUser(): Promise<TwitterUserData>;
    postTweet(content: string): Promise<TweetData>;
    getRecentTweets(count?: number): Promise<TweetData[]>;
    getTotalImpressions(): Promise<number>;
    getTweetAnalytics(tweetId: string): Promise<TweetData | null>;
    calculateEngagementMetrics(): Promise<{
        totalEngagements: number;
        totalImpressions: number;
        engagementRate: number;
    }>;
}
