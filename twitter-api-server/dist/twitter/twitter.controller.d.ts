import { TwitterService } from './twitter.service';
import { DatabaseService } from '../database/database.service';
import { PostTweetDto } from './dto/post-tweet.dto';
import { TwitterStatsResponseDto, PostTweetResponseDto, TokenStatusResponseDto } from './dto/twitter-response.dto';
export declare class TwitterController {
    private readonly twitterService;
    private readonly databaseService;
    private readonly logger;
    constructor(twitterService: TwitterService, databaseService: DatabaseService);
    postTweet(postTweetDto: PostTweetDto): Promise<PostTweetResponseDto>;
    getTwitterStats(): Promise<TwitterStatsResponseDto>;
    refreshTwitterStats(): Promise<{
        success: boolean;
    }>;
    getAuthStatus(): Promise<TokenStatusResponseDto>;
    healthCheck(): Promise<{
        status: string;
        database: boolean;
        tokens: boolean;
    }>;
}
