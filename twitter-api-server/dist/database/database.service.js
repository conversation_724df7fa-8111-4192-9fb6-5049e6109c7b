"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DatabaseService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_js_1 = require("@supabase/supabase-js");
let DatabaseService = DatabaseService_1 = class DatabaseService {
    configService;
    logger = new common_1.Logger(DatabaseService_1.name);
    supabase;
    constructor(configService) {
        this.configService = configService;
        const supabaseUrl = this.configService.get('SUPABASE_URL') || '';
        const supabaseKey = this.configService.get('SUPABASE_ANON_KEY') || '';
        if (!supabaseUrl || !supabaseKey) {
            throw new Error('Supabase configuration not found in environment variables');
        }
        this.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey);
    }
    async getTwitterTokens() {
        try {
            const { data, error } = await this.supabase
                .from('twitter_tokens')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(1)
                .single();
            if (error && error.code !== 'PGRST116') {
                throw error;
            }
            return data;
        }
        catch (error) {
            this.logger.error('Error fetching Twitter tokens:', error);
            throw error;
        }
    }
    async saveTwitterTokens(tokens) {
        try {
            const existingData = await this.getTwitterTokens();
            if (existingData) {
                const { data, error } = await this.supabase
                    .from('twitter_tokens')
                    .update({
                    ...tokens,
                    updated_at: new Date().toISOString()
                })
                    .eq('id', existingData.id)
                    .select()
                    .single();
                if (error)
                    throw error;
                return data;
            }
            else {
                const { data, error } = await this.supabase
                    .from('twitter_tokens')
                    .insert({
                    ...tokens,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                })
                    .select()
                    .single();
                if (error)
                    throw error;
                return data;
            }
        }
        catch (error) {
            this.logger.error('Error saving Twitter tokens:', error);
            throw error;
        }
    }
    async updateTwitterTokens(accessToken, refreshToken, expiresAt) {
        try {
            const { data: existingData, error: selectError } = await this.supabase
                .from('twitter_tokens')
                .select('id')
                .limit(1)
                .single();
            if (!existingData) {
                throw new Error('No existing Twitter tokens found. Please authenticate first.');
            }
            const { data, error } = await this.supabase
                .from('twitter_tokens')
                .update({
                access_token: accessToken,
                refresh_token: refreshToken,
                expires_at: expiresAt,
                updated_at: new Date().toISOString()
            })
                .eq('id', existingData.id)
                .select()
                .single();
            if (error)
                throw error;
            return data;
        }
        catch (error) {
            this.logger.error('Error updating Twitter tokens:', error);
            throw error;
        }
    }
    async addPostedTweet(tweet) {
        try {
            const { data, error } = await this.supabase
                .from('posted_tweets')
                .insert({
                ...tweet,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
                .select()
                .single();
            if (error)
                throw error;
            return data;
        }
        catch (error) {
            this.logger.error('Error adding posted tweet:', error);
            throw error;
        }
    }
    async getPostedTweets(limit = 50) {
        try {
            const { data, error } = await this.supabase
                .from('posted_tweets')
                .select('*')
                .order('posted_at', { ascending: false })
                .limit(limit);
            if (error)
                throw error;
            return data;
        }
        catch (error) {
            this.logger.error('Error fetching posted tweets:', error);
            throw error;
        }
    }
    async updatePostedTweetMetrics(tweetId, metrics) {
        try {
            const { data, error } = await this.supabase
                .from('posted_tweets')
                .update({
                ...metrics,
                updated_at: new Date().toISOString()
            })
                .eq('tweet_id', tweetId)
                .select()
                .single();
            if (error)
                throw error;
            return data;
        }
        catch (error) {
            this.logger.error('Error updating posted tweet metrics:', error);
            throw error;
        }
    }
    async addAnalytics(analytics) {
        try {
            const { data, error } = await this.supabase
                .from('analytics')
                .insert({
                ...analytics,
                date: new Date().toISOString().split('T')[0],
                created_at: new Date().toISOString()
            })
                .select()
                .single();
            if (error)
                throw error;
            return data;
        }
        catch (error) {
            this.logger.error('Error adding analytics:', error);
            throw error;
        }
    }
    async getLatestAnalytics() {
        try {
            const { data, error } = await this.supabase
                .from('analytics')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(1)
                .single();
            if (error && error.code !== 'PGRST116') {
                throw error;
            }
            return data;
        }
        catch (error) {
            this.logger.error('Error fetching latest analytics:', error);
            throw error;
        }
    }
    async getAnalyticsHistory(days = 30) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            const { data, error } = await this.supabase
                .from('analytics')
                .select('*')
                .gte('date', startDate.toISOString().split('T')[0])
                .order('date', { ascending: false });
            if (error)
                throw error;
            return data;
        }
        catch (error) {
            this.logger.error('Error fetching analytics history:', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            const { data, error } = await this.supabase
                .from('twitter_tokens')
                .select('count')
                .limit(1);
            return !error;
        }
        catch (error) {
            this.logger.error('Database health check failed:', error);
            return false;
        }
    }
};
exports.DatabaseService = DatabaseService;
exports.DatabaseService = DatabaseService = DatabaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], DatabaseService);
//# sourceMappingURL=database.service.js.map