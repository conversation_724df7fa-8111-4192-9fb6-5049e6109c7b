import { ConfigService } from '@nestjs/config';
import { TwitterTokens, PostedTweet, Analytics } from '../twitter/interfaces/twitter.interface';
export declare class DatabaseService {
    private configService;
    private readonly logger;
    private supabase;
    constructor(configService: ConfigService);
    getTwitterTokens(): Promise<TwitterTokens | null>;
    saveTwitterTokens(tokens: Partial<TwitterTokens>): Promise<TwitterTokens>;
    updateTwitterTokens(accessToken: string, refreshToken: string, expiresAt?: string): Promise<TwitterTokens>;
    addPostedTweet(tweet: Partial<PostedTweet>): Promise<PostedTweet>;
    getPostedTweets(limit?: number): Promise<PostedTweet[]>;
    updatePostedTweetMetrics(tweetId: string, metrics: {
        impressions?: number;
        retweets?: number;
        likes?: number;
        replies?: number;
    }): Promise<PostedTweet>;
    addAnalytics(analytics: Partial<Analytics>): Promise<Analytics>;
    getLatestAnalytics(): Promise<Analytics | null>;
    getAnalyticsHistory(days?: number): Promise<Analytics[]>;
    healthCheck(): Promise<boolean>;
}
