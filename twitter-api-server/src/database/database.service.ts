import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { TwitterTokens, PostedTweet, Analytics } from '../twitter/interfaces/twitter.interface';

@Injectable()
export class DatabaseService {
  private readonly logger = new Logger(DatabaseService.name);
  private supabase: SupabaseClient;

  constructor(private configService: ConfigService) {
    const supabaseUrl = this.configService.get<string>('SUPABASE_URL') || '';
    const supabaseKey = this.configService.get<string>('SUPABASE_ANON_KEY') || '';

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration not found in environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  // Twitter Tokens Management
  async getTwitterTokens(): Promise<TwitterTokens | null> {
    try {
      const { data, error } = await this.supabase
        .from('twitter_tokens')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data as TwitterTokens | null;
    } catch (error) {
      this.logger.error('Error fetching Twitter tokens:', error);
      throw error;
    }
  }

  async saveTwitterTokens(tokens: Partial<TwitterTokens>): Promise<TwitterTokens> {
    try {
      // Check if tokens already exist
      const existingData = await this.getTwitterTokens();

      if (existingData) {
        // Update existing tokens
        const { data, error } = await this.supabase
          .from('twitter_tokens')
          .update({
            ...tokens,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData.id)
          .select()
          .single();

        if (error) throw error;
        return data as TwitterTokens;
      } else {
        // Insert new tokens
        const { data, error } = await this.supabase
          .from('twitter_tokens')
          .insert({
            ...tokens,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) throw error;
        return data as TwitterTokens;
      }
    } catch (error) {
      this.logger.error('Error saving Twitter tokens:', error);
      throw error;
    }
  }

  async updateTwitterTokens(accessToken: string, refreshToken: string, expiresAt?: string): Promise<TwitterTokens> {
    try {
      const { data: existingData, error: selectError } = await this.supabase
        .from('twitter_tokens')
        .select('id')
        .limit(1)
        .single();

      if (!existingData) {
        throw new Error('No existing Twitter tokens found. Please authenticate first.');
      }

      const { data, error } = await this.supabase
        .from('twitter_tokens')
        .update({
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_at: expiresAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingData.id)
        .select()
        .single();

      if (error) throw error;
      return data as TwitterTokens;
    } catch (error) {
      this.logger.error('Error updating Twitter tokens:', error);
      throw error;
    }
  }

  // Posted Tweets Management
  async addPostedTweet(tweet: Partial<PostedTweet>): Promise<PostedTweet> {
    try {
      const { data, error } = await this.supabase
        .from('posted_tweets')
        .insert({
          ...tweet,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data as PostedTweet;
    } catch (error) {
      this.logger.error('Error adding posted tweet:', error);
      throw error;
    }
  }

  async getPostedTweets(limit: number = 50): Promise<PostedTweet[]> {
    try {
      const { data, error } = await this.supabase
        .from('posted_tweets')
        .select('*')
        .order('posted_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data as PostedTweet[];
    } catch (error) {
      this.logger.error('Error fetching posted tweets:', error);
      throw error;
    }
  }

  async updatePostedTweetMetrics(tweetId: string, metrics: {
    impressions?: number;
    retweets?: number;
    likes?: number;
    replies?: number;
  }): Promise<PostedTweet> {
    try {
      const { data, error } = await this.supabase
        .from('posted_tweets')
        .update({
          ...metrics,
          updated_at: new Date().toISOString()
        })
        .eq('tweet_id', tweetId)
        .select()
        .single();

      if (error) throw error;
      return data as PostedTweet;
    } catch (error) {
      this.logger.error('Error updating posted tweet metrics:', error);
      throw error;
    }
  }

  // Analytics Management
  async addAnalytics(analytics: Partial<Analytics>): Promise<Analytics> {
    try {
      const { data, error } = await this.supabase
        .from('analytics')
        .insert({
          ...analytics,
          date: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data as Analytics;
    } catch (error) {
      this.logger.error('Error adding analytics:', error);
      throw error;
    }
  }

  async getLatestAnalytics(): Promise<Analytics | null> {
    try {
      const { data, error } = await this.supabase
        .from('analytics')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data as Analytics | null;
    } catch (error) {
      this.logger.error('Error fetching latest analytics:', error);
      throw error;
    }
  }

  async getAnalyticsHistory(days: number = 30): Promise<Analytics[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await this.supabase
        .from('analytics')
        .select('*')
        .gte('date', startDate.toISOString().split('T')[0])
        .order('date', { ascending: false });

      if (error) throw error;
      return data as Analytics[];
    } catch (error) {
      this.logger.error('Error fetching analytics history:', error);
      throw error;
    }
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('twitter_tokens')
        .select('count')
        .limit(1);

      return !error;
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return false;
    }
  }
}
