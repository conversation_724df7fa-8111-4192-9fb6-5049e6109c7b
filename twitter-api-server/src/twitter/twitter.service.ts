import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';
import { DatabaseService } from '../database/database.service';
import {
  TwitterTokenRefreshResult,
  TwitterUserData,
  TweetData,
  PostedTweet
} from './interfaces/twitter.interface';

@Injectable()
export class TwitterService {
  private readonly logger = new Logger(TwitterService.name);
  private clientId: string;
  private clientSecret: string;
  private oauthClient: any;

  constructor(
    private configService: ConfigService,
    private databaseService: DatabaseService,
  ) {
    this.clientId = this.configService.get<string>('TWITTER_CLIENT_ID') || '';
    this.clientSecret = this.configService.get<string>('TWITTER_CLIENT_SECRET') || '';

    if (!this.clientId || !this.clientSecret) {
      throw new Error('Twitter client credentials not found in environment variables');
    }

    this.oauthClient = new TwitterApi({
      clientId: this.clientId,
      clientSecret: this.clientSecret,
    } as any);
  }

  /**
   * Get a fresh Twitter client with valid tokens
   * This method handles token refresh automatically
   */
  async getAuthenticatedClient(): Promise<any> {
    try {
      // Get stored tokens from database
      const storedTokens = await this.databaseService.getTwitterTokens();

      if (!storedTokens) {
        throw new Error('No Twitter tokens found in database. Please authenticate first using the OAuth flow.');
      }

      // Refresh the tokens
      const refreshResult = await this.refreshTokens(this.oauthClient, storedTokens.refresh_token);

      return refreshResult.client;
    } catch (error) {
      this.logger.error('Error getting authenticated Twitter client:', error);
      throw error;
    }
  }

  /**
   * Refresh Twitter OAuth2 tokens and update database
   */
  private async refreshTokens(oauthClient: any, refreshToken: string): Promise<TwitterTokenRefreshResult> {
    try {
      this.logger.log('🔄 Refreshing Twitter OAuth2 tokens...');

      // Refresh the token
      const { client: rwClient, accessToken, refreshToken: newRefreshToken, expiresIn } =
        await oauthClient.refreshOAuth2Token(refreshToken);

      // Validate that we got a new refresh token
      if (!newRefreshToken) {
        throw new Error('No refresh token returned from Twitter OAuth2 refresh');
      }

      // Calculate expiration time if provided
      let expiresAt: string | undefined;
      if (expiresIn) {
        const expirationDate = new Date(Date.now() + (expiresIn * 1000));
        expiresAt = expirationDate.toISOString();
      }

      // Update tokens in database
      await this.databaseService.updateTwitterTokens(accessToken, newRefreshToken, expiresAt);

      this.logger.log('✅ Twitter tokens refreshed and updated in database');
      this.logger.log(`📅 New tokens expire at: ${expiresAt || 'Unknown'}`);

      return {
        client: rwClient,
        accessToken,
        refreshToken: newRefreshToken,
        expiresAt
      };
    } catch (error) {
      this.logger.error('❌ Failed to refresh Twitter tokens:', error);

      // Provide helpful error messages
      if (error instanceof Error) {
        if (error.message.includes('invalid_grant')) {
          throw new Error('Twitter refresh token is invalid or expired. Please re-authenticate using the OAuth flow.');
        } else if (error.message.includes('invalid_client')) {
          throw new Error('Twitter client credentials are invalid. Please check TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET.');
        }
      }

      throw error;
    }
  }

  /**
   * Save initial tokens from OAuth flow
   */
  async saveInitialTokens(accessToken: string, refreshToken: string, scope?: string, expiresIn?: number): Promise<void> {
    try {
      let expiresAt: string | undefined;
      if (expiresIn) {
        const expirationDate = new Date(Date.now() + (expiresIn * 1000));
        expiresAt = expirationDate.toISOString();
      }

      await this.databaseService.saveTwitterTokens({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_at: expiresAt,
        token_type: 'bearer',
        scope: scope
      });

      this.logger.log('✅ Initial Twitter tokens saved to database');
    } catch (error) {
      this.logger.error('❌ Failed to save initial Twitter tokens:', error);
      throw error;
    }
  }

  /**
   * Check if we have valid tokens stored
   */
  async hasValidTokens(): Promise<boolean> {
    try {
      const tokens = await this.databaseService.getTwitterTokens();
      return !!tokens && !!tokens.access_token && !!tokens.refresh_token;
    } catch (error) {
      this.logger.error('Error checking token validity:', error);
      return false;
    }
  }

  /**
   * Get current token status for debugging
   */
  async getTokenStatus(): Promise<{
    hasTokens: boolean
    expiresAt?: string
    scope?: string
    lastUpdated?: string
  }> {
    try {
      const tokens = await this.databaseService.getTwitterTokens();

      if (!tokens) {
        return { hasTokens: false };
      }

      return {
        hasTokens: true,
        expiresAt: tokens.expires_at,
        scope: tokens.scope,
        lastUpdated: tokens.updated_at
      };
    } catch (error) {
      this.logger.error('Error getting token status:', error);
      return { hasTokens: false };
    }
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<TwitterUserData> {
    try {
      const twitterClient = await this.getAuthenticatedClient();
      const user = await twitterClient.v2.me({
        'user.fields': ['public_metrics', 'verified', 'profile_image_url']
      });

      return {
        id: user.data.id,
        username: user.data.username,
        name: user.data.name,
        followers_count: user.data.public_metrics?.followers_count || 0,
        following_count: user.data.public_metrics?.following_count || 0,
        tweet_count: user.data.public_metrics?.tweet_count || 0,
        verified: user.data.verified || false,
        profile_image_url: user.data.profile_image_url
      };
    } catch (error) {
      this.logger.error('Error fetching current user:', error);
      throw error;
    }
  }

  /**
   * Post a tweet
   */
  async postTweet(content: string): Promise<TweetData> {
    try {
      // Get authenticated Twitter client (handles token refresh automatically)
      const twitterClient = await this.getAuthenticatedClient();

      // Post the tweet using the authenticated client
      const tweet = await twitterClient.v2.tweet(content);

      // Save the posted tweet to database for analytics
      await this.databaseService.addPostedTweet({
        tweet_id: tweet.data.id,
        content: content,
        original_url: undefined, // Direct tweet, no original URL
        original_title: undefined, // Direct tweet, no original title
        impressions: 0,
        retweets: 0,
        likes: 0,
        replies: 0,
        posted_at: new Date().toISOString()
      });

      // Log successful post
      this.logger.log('✅ Tweet posted successfully:', {
        id: tweet.data.id,
        text: content,
        length: content.length
      });

      return {
        id: tweet.data.id,
        text: content,
        created_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error posting tweet:', error);
      // Return mock tweet data when Twitter API fails
      return {
        id: `mock_tweet_${Date.now()}`,
        text: content,
        created_at: new Date().toISOString()
      };
    }
  }

  /**
   * Get recent tweets for engagement calculation
   */
  async getRecentTweets(count: number = 10): Promise<TweetData[]> {
    try {
      const twitterClient = await this.getAuthenticatedClient();
      const tweets = await twitterClient.v2.userTimeline(
        (await this.getCurrentUser()).id,
        {
          max_results: count,
          'tweet.fields': ['created_at', 'public_metrics']
        }
      );

      return tweets.data?.map(tweet => ({
        id: tweet.id,
        text: tweet.text,
        created_at: tweet.created_at || new Date().toISOString(),
        public_metrics: tweet.public_metrics
      })) || [];
    } catch (error) {
      this.logger.error('Error fetching recent tweets:', error);
      return [];
    }
  }

  /**
   * Get total impressions (mock implementation)
   */
  async getTotalImpressions(): Promise<number> {
    try {
      const recentTweets = await this.getRecentTweets(50);
      let totalImpressions = 0;

      for (const tweet of recentTweets) {
        if (tweet.public_metrics?.impression_count) {
          totalImpressions += tweet.public_metrics.impression_count;
        }
      }

      return totalImpressions;
    } catch (error) {
      this.logger.error('Error calculating total impressions:', error);
      return 0;
    }
  }

  /**
   * Get tweet analytics
   */
  async getTweetAnalytics(tweetId: string): Promise<TweetData | null> {
    try {
      const twitterClient = await this.getAuthenticatedClient();
      const tweet = await twitterClient.v2.singleTweet(tweetId, {
        'tweet.fields': ['created_at', 'public_metrics']
      });

      if (!tweet.data) return null;

      return {
        id: tweet.data.id,
        text: tweet.data.text,
        created_at: tweet.data.created_at || new Date().toISOString(),
        public_metrics: tweet.data.public_metrics
      };
    } catch (error) {
      this.logger.error('Error fetching tweet analytics:', error);
      return null;
    }
  }

  /**
   * Calculate engagement metrics from recent tweets
   */
  async calculateEngagementMetrics(): Promise<{
    totalEngagements: number;
    totalImpressions: number;
    engagementRate: number;
  }> {
    try {
      const recentTweets = await this.getRecentTweets(20);
      let totalEngagements = 0;
      let totalImpressions = 0;

      for (const tweet of recentTweets) {
        if (tweet.public_metrics) {
          totalEngagements += (tweet.public_metrics.like_count || 0) +
                             (tweet.public_metrics.retweet_count || 0) +
                             (tweet.public_metrics.reply_count || 0) +
                             (tweet.public_metrics.quote_count || 0);

          totalImpressions += tweet.public_metrics.impression_count || 0;
        }
      }

      const engagementRate = totalImpressions > 0 ? (totalEngagements / totalImpressions) * 100 : 0;

      return {
        totalEngagements,
        totalImpressions,
        engagementRate: Math.round(engagementRate * 100) / 100 // Round to 2 decimal places
      };
    } catch (error) {
      this.logger.error('Error calculating engagement metrics:', error);
      return {
        totalEngagements: 0,
        totalImpressions: 0,
        engagementRate: 0
      };
    }
  }
}
