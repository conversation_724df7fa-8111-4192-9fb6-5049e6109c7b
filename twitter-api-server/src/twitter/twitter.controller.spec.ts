import { Test, TestingModule } from '@nestjs/testing';
import { Twitter<PERSON>ontroller } from './twitter.controller';

describe('TwitterController', () => {
  let controller: TwitterController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TwitterController],
    }).compile();

    controller = module.get<TwitterController>(TwitterController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
