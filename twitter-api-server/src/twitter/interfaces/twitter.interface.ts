export interface TwitterTokenRefreshResult {
  client: any // TwitterApi
  accessToken: string
  refreshToken: string
  expiresAt?: string
}

export interface TwitterTokens {
  id?: string
  access_token: string
  refresh_token: string
  expires_at?: string
  token_type: string
  scope?: string
  created_at?: string
  updated_at?: string
}

export interface TwitterUserData {
  id: string
  username: string
  name: string
  followers_count: number
  following_count: number
  tweet_count: number
  verified: boolean
  profile_image_url?: string
}

export interface TweetData {
  id: string
  text: string
  created_at: string
  public_metrics?: {
    retweet_count: number
    like_count: number
    reply_count: number
    quote_count: number
    impression_count?: number
  }
}

export interface PostedTweet {
  id?: string
  tweet_id: string
  content: string
  original_url?: string
  original_title?: string
  impressions: number
  retweets: number
  likes: number
  replies: number
  posted_at: string
  created_at?: string
  updated_at?: string
}

export interface Analytics {
  id?: string
  followers_count: number
  following_count: number
  total_tweets: number
  total_impressions: number
  total_engagements: number
  date?: string
  created_at?: string
}
