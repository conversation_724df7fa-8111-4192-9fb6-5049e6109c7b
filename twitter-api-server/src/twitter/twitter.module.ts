import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TwitterService } from './twitter.service';
import { TwitterController } from './twitter.controller';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [ConfigModule, DatabaseModule],
  providers: [TwitterService],
  controllers: [TwitterController],
  exports: [TwitterService]
})
export class TwitterModule {}
