import {
  <PERSON>,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Logger,
  ValidationPipe,
  UsePipes
} from '@nestjs/common';
import { TwitterService } from './twitter.service';
import { DatabaseService } from '../database/database.service';
import { PostTweetDto } from './dto/post-tweet.dto';
import {
  TwitterStatsResponseDto,
  PostTweetResponseDto,
  TokenStatusResponseDto
} from './dto/twitter-response.dto';

@Controller('twitter')
export class TwitterController {
  private readonly logger = new Logger(TwitterController.name);

  constructor(
    private readonly twitterService: TwitterService,
    private readonly databaseService: DatabaseService,
  ) {}

  @Post('post')
  @UsePipes(new ValidationPipe({ transform: true }))
  async postTweet(@Body() postTweetDto: PostTweetDto): Promise<PostTweetResponseDto> {
    try {
      this.logger.log(`Posting tweet: ${postTweetDto.content.substring(0, 50)}...`);

      const tweet = await this.twitterService.postTweet(postTweetDto.content);

      return {
        success: true,
        tweet: {
          id: tweet.id,
          text: tweet.text,
          created_at: tweet.created_at
        }
      };
    } catch (error) {
      this.logger.error('Error posting tweet:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to post tweet'
      };
    }
  }

  @Get('stats')
  async getTwitterStats(): Promise<TwitterStatsResponseDto> {
    try {
      // Get current user data from Twitter
      const userData = await this.twitterService.getCurrentUser();

      // Calculate engagement metrics
      const engagementMetrics = await this.twitterService.calculateEngagementMetrics();

      return {
        followers_count: userData.followers_count,
        following_count: userData.following_count,
        total_tweets: userData.tweet_count,
        total_impressions: engagementMetrics.totalImpressions,
        total_engagements: engagementMetrics.totalEngagements,
        engagement_rate: engagementMetrics.engagementRate,
        username: userData.username,
        name: userData.name,
        verified: userData.verified,
        profile_image_url: userData.profile_image_url
      };
    } catch (error) {
      this.logger.error('Error fetching Twitter stats:', error);
      throw new HttpException(
        'Failed to fetch Twitter stats',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('stats')
  async refreshTwitterStats(): Promise<{ success: boolean }> {
    try {
      // Force refresh of Twitter stats
      const userData = await this.twitterService.getCurrentUser();
      const totalImpressions = await this.twitterService.getTotalImpressions();
      const engagementMetrics = await this.twitterService.calculateEngagementMetrics();

      // Update analytics in database
      await this.databaseService.addAnalytics({
        followers_count: userData.followers_count,
        following_count: userData.following_count,
        total_tweets: userData.tweet_count,
        total_impressions: totalImpressions,
        total_engagements: engagementMetrics.totalEngagements
      });

      return { success: true };
    } catch (error) {
      this.logger.error('Error refreshing Twitter stats:', error);
      throw new HttpException(
        'Failed to refresh Twitter stats',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('auth/status')
  async getAuthStatus(): Promise<TokenStatusResponseDto> {
    try {
      return await this.twitterService.getTokenStatus();
    } catch (error) {
      this.logger.error('Error getting auth status:', error);
      throw new HttpException(
        'Failed to get authentication status',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('health')
  async healthCheck(): Promise<{ status: string; database: boolean; tokens: boolean }> {
    try {
      const databaseHealth = await this.databaseService.healthCheck();
      const hasTokens = await this.twitterService.hasValidTokens();

      return {
        status: databaseHealth && hasTokens ? 'healthy' : 'degraded',
        database: databaseHealth,
        tokens: hasTokens
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        database: false,
        tokens: false
      };
    }
  }
}
