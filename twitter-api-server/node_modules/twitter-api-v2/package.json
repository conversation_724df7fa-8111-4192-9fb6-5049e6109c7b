{"name": "twitter-api-v2", "version": "1.24.0", "description": "Strongly typed, full-featured, light, versatile yet powerful Twitter API v1.1 and v2 client for Node.js.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "keywords": ["twitter", "api", "typed", "types", "v2", "v1.1"], "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc -b tsconfig.cjs.json", "build:esm": "tsc -b tsconfig.esm.json", "build-doc": "typedoc src/index.ts --out tsdocs", "lint": "eslint --ext \".ts\" --ignore-path .gitignore .", "mocha": "mocha -r ts-node/register --timeout 10000", "test": "npm run mocha 'test/**/*.test.ts'", "test-tweet": "npm run mocha 'test/tweet.*.test.ts'", "test-user": "npm run mocha 'test/user.*.test.ts'", "test-stream": "npm run mocha test/stream.test.ts", "test-media": "npm run mocha test/media-upload.test.ts", "test-auth": "npm run mocha test/auth.test.ts", "test-dm": "npm run mocha test/dm.*.test.ts", "test-list": "npm run mocha test/list.*.test.ts", "test-space": "npm run mocha test/space.v2.test.ts", "test-account": "npm run mocha test/account.*.test.ts", "test-plugin": "npm run mocha test/plugin.test.ts", "prepublish": "npm run build"}, "repository": "github:plhery/node-twitter-api-v2", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://x.com/plhery)", "license": "Apache-2.0", "files": ["dist"], "devDependencies": {"@types/chai": "^4.2.16", "@types/mocha": "^10.0.1", "@types/node": "^18.11.17", "@typescript-eslint/eslint-plugin": "^5.47.0", "@typescript-eslint/parser": "^5.47.0", "chai": "^4.3.4", "dotenv": "^16.0.3", "eslint": "^8.30.0", "mocha": "^10.0.0", "ts-node": "^10.9.1", "typedoc": "^0.23.23", "typescript": "^4.2.4"}, "bugs": {"url": "https://github.com/plhery/node-twitter-api/issues"}}