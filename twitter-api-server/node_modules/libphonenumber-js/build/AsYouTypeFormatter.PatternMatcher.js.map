{"version": 3, "file": "AsYouTypeFormatter.PatternMatcher.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "pattern", "matchTree", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "string", "allowOverflow", "Error", "result", "match", "split", "matched<PERSON><PERSON><PERSON>", "overflow", "characters", "tree", "last", "characterString", "join", "indexOf", "length", "partialMatch", "slice", "Array", "isArray", "restCharacters", "i", "subtree", "JSON", "stringify", "op", "args", "branch", "char"], "sources": ["../source/AsYouTypeFormatter.PatternMatcher.js"], "sourcesContent": ["import PatternParser from './AsYouTypeFormatter.PatternParser.js'\r\n\r\nexport default class PatternMatcher {\r\n\tconstructor(pattern) {\r\n\t\tthis.matchTree = new PatternParser().parse(pattern)\r\n\t}\r\n\r\n\tmatch(string, { allowOverflow } = {}) {\r\n\t\tif (!string) {\r\n\t\t\tthrow new Error('String is required')\r\n\t\t}\r\n\t\tconst result = match(string.split(''), this.matchTree, true)\r\n\t\tif (result && result.match) {\r\n\t\t\tdelete result.matchedChars\r\n\t\t}\r\n\t\tif (result && result.overflow) {\r\n\t\t\tif (!allowOverflow) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn result\r\n\t}\r\n}\r\n\r\n/**\r\n * Matches `characters` against a pattern compiled into a `tree`.\r\n * @param  {string[]} characters\r\n * @param  {Tree} tree — A pattern compiled into a `tree`. See the `*.d.ts` file for the description of the `tree` structure.\r\n * @param  {boolean} last — Whether it's the last (rightmost) subtree on its level of the match tree.\r\n * @return {object} See the `*.d.ts` file for the description of the result object.\r\n */\r\nfunction match(characters, tree, last) {\r\n\t// If `tree` is a string, then `tree` is a single character.\r\n\t// That's because when a pattern is parsed, multi-character-string parts\r\n\t// of a pattern are compiled into arrays of single characters.\r\n\t// I still wrote this piece of code for a \"general\" hypothetical case\r\n\t// when `tree` could be a string of several characters, even though\r\n\t// such case is not possible with the current implementation.\r\n\tif (typeof tree === 'string') {\r\n\t\tconst characterString = characters.join('')\r\n\t\tif (tree.indexOf(characterString) === 0) {\r\n\t\t\t// `tree` is always a single character.\r\n\t\t\t// If `tree.indexOf(characterString) === 0`\r\n\t\t\t// then `characters.length === tree.length`.\r\n\t\t\t/* istanbul ignore else */\r\n\t\t\tif (characters.length === tree.length) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tmatch: true,\r\n\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// `tree` is always a single character.\r\n\t\t\t// If `tree.indexOf(characterString) === 0`\r\n\t\t\t// then `characters.length === tree.length`.\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\treturn {\r\n\t\t\t\tpartialMatch: true,\r\n\t\t\t\t// matchedChars: characters\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (characterString.indexOf(tree) === 0) {\r\n\t\t\tif (last) {\r\n\t\t\t\t// The `else` path is not possible because `tree` is always a single character.\r\n\t\t\t\t// The `else` case for `characters.length > tree.length` would be\r\n\t\t\t\t// `characters.length <= tree.length` which means `characters.length <= 1`.\r\n\t\t\t\t// `characters` array can't be empty, so that means `characters === [tree]`,\r\n\t\t\t\t// which would also mean `tree.indexOf(characterString) === 0` and that'd mean\r\n\t\t\t\t// that the `if (tree.indexOf(characterString) === 0)` condition before this\r\n\t\t\t\t// `if` condition would be entered, and returned from there, not reaching this code.\r\n\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\tif (characters.length > tree.length) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\toverflow: true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tmatch: true,\r\n\t\t\t\tmatchedChars: characters.slice(0, tree.length)\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn\r\n\t}\r\n\r\n\tif (Array.isArray(tree)) {\r\n\t\tlet restCharacters = characters.slice()\r\n\t\tlet i = 0\r\n\t\twhile (i < tree.length) {\r\n\t\t\tconst subtree = tree[i]\r\n\t\t\tconst result = match(restCharacters, subtree, last && (i === tree.length - 1))\r\n\t\t\tif (!result) {\r\n\t\t\t\treturn\r\n\t\t\t} else if (result.overflow) {\r\n\t\t\t\treturn result\r\n\t\t\t} else if (result.match) {\r\n\t\t\t\t// Continue with the next subtree with the rest of the characters.\r\n\t\t\t\trestCharacters = restCharacters.slice(result.matchedChars.length)\r\n\t\t\t\tif (restCharacters.length === 0) {\r\n\t\t\t\t\tif (i === tree.length - 1) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t\t\t// matchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\tif (result.partialMatch) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t\t// matchedChars: characters\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error(`Unsupported match result:\\n${JSON.stringify(result, null, 2)}`)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ti++\r\n\t\t}\r\n\t\t// If `last` then overflow has already been checked\r\n\t\t// by the last element of the `tree` array.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (last) {\r\n\t\t\treturn {\r\n\t\t\t\toverflow: true\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tmatch: true,\r\n\t\t\tmatchedChars: characters.slice(0, characters.length - restCharacters.length)\r\n\t\t}\r\n\t}\r\n\r\n\tswitch (tree.op) {\r\n\t\tcase '|':\r\n\t\t\tlet partialMatch\r\n\t\t\tfor (const branch of tree.args) {\r\n\t\t\t\tconst result = match(characters, branch, last)\r\n\t\t\t\tif (result) {\r\n\t\t\t\t\tif (result.overflow) {\r\n\t\t\t\t\t\treturn result\r\n\t\t\t\t\t} else if (result.match) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: result.matchedChars\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\t\t\tif (result.partialMatch) {\r\n\t\t\t\t\t\t\tpartialMatch = true\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthrow new Error(`Unsupported match result:\\n${JSON.stringify(result, null, 2)}`)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (partialMatch) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t// matchedChars: ...\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// Not even a partial match.\r\n\t\t\treturn\r\n\r\n\t\tcase '[]':\r\n\t\t\tfor (const char of tree.args) {\r\n\t\t\t\tif (characters[0] === char) {\r\n\t\t\t\t\tif (characters.length === 1) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (last) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\toverflow: true\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\tmatchedChars: [char]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// No character matches.\r\n\t\t\treturn\r\n\r\n\t\t/* istanbul ignore next */\r\n\t\tdefault:\r\n\t\t\tthrow new Error(`Unsupported instruction tree: ${tree}`)\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA;;;;;;;;;;;;;;;;IAEqBA,c;EACpB,wBAAYC,OAAZ,EAAqB;IAAA;;IACpB,KAAKC,SAAL,GAAiB,IAAIC,2CAAJ,GAAoBC,KAApB,CAA0BH,OAA1B,CAAjB;EACA;;;;WAED,eAAMI,MAAN,EAAsC;MAAA,+EAAJ,EAAI;MAAA,IAAtBC,aAAsB,QAAtBA,aAAsB;;MACrC,IAAI,CAACD,MAAL,EAAa;QACZ,MAAM,IAAIE,KAAJ,CAAU,oBAAV,CAAN;MACA;;MACD,IAAMC,MAAM,GAAGC,MAAK,CAACJ,MAAM,CAACK,KAAP,CAAa,EAAb,CAAD,EAAmB,KAAKR,SAAxB,EAAmC,IAAnC,CAApB;;MACA,IAAIM,MAAM,IAAIA,MAAM,CAACC,KAArB,EAA4B;QAC3B,OAAOD,MAAM,CAACG,YAAd;MACA;;MACD,IAAIH,MAAM,IAAIA,MAAM,CAACI,QAArB,EAA+B;QAC9B,IAAI,CAACN,aAAL,EAAoB;UACnB;QACA;MACD;;MACD,OAAOE,MAAP;IACA;;;;;AAGF;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AACA,SAASC,MAAT,CAAeI,UAAf,EAA2BC,IAA3B,EAAiCC,IAAjC,EAAuC;EACtC;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOD,IAAP,KAAgB,QAApB,EAA8B;IAC7B,IAAME,eAAe,GAAGH,UAAU,CAACI,IAAX,CAAgB,EAAhB,CAAxB;;IACA,IAAIH,IAAI,CAACI,OAAL,CAAaF,eAAb,MAAkC,CAAtC,EAAyC;MACxC;MACA;MACA;;MACA;MACA,IAAIH,UAAU,CAACM,MAAX,KAAsBL,IAAI,CAACK,MAA/B,EAAuC;QACtC,OAAO;UACNV,KAAK,EAAE,IADD;UAENE,YAAY,EAAEE;QAFR,CAAP;MAIA,CAVuC,CAWxC;MACA;MACA;;MACA;;;MACA,OAAO;QACNO,YAAY,EAAE,IADR,CAEN;;MAFM,CAAP;IAIA;;IACD,IAAIJ,eAAe,CAACE,OAAhB,CAAwBJ,IAAxB,MAAkC,CAAtC,EAAyC;MACxC,IAAIC,IAAJ,EAAU;QACT;QACA;QACA;QACA;QACA;QACA;QACA;;QACA;QACA,IAAIF,UAAU,CAACM,MAAX,GAAoBL,IAAI,CAACK,MAA7B,EAAqC;UACpC,OAAO;YACNP,QAAQ,EAAE;UADJ,CAAP;QAGA;MACD;;MACD,OAAO;QACNH,KAAK,EAAE,IADD;QAENE,YAAY,EAAEE,UAAU,CAACQ,KAAX,CAAiB,CAAjB,EAAoBP,IAAI,CAACK,MAAzB;MAFR,CAAP;IAIA;;IACD;EACA;;EAED,IAAIG,KAAK,CAACC,OAAN,CAAcT,IAAd,CAAJ,EAAyB;IACxB,IAAIU,cAAc,GAAGX,UAAU,CAACQ,KAAX,EAArB;IACA,IAAII,CAAC,GAAG,CAAR;;IACA,OAAOA,CAAC,GAAGX,IAAI,CAACK,MAAhB,EAAwB;MACvB,IAAMO,OAAO,GAAGZ,IAAI,CAACW,CAAD,CAApB;;MACA,IAAMjB,MAAM,GAAGC,MAAK,CAACe,cAAD,EAAiBE,OAAjB,EAA0BX,IAAI,IAAKU,CAAC,KAAKX,IAAI,CAACK,MAAL,GAAc,CAAvD,CAApB;;MACA,IAAI,CAACX,MAAL,EAAa;QACZ;MACA,CAFD,MAEO,IAAIA,MAAM,CAACI,QAAX,EAAqB;QAC3B,OAAOJ,MAAP;MACA,CAFM,MAEA,IAAIA,MAAM,CAACC,KAAX,EAAkB;QACxB;QACAe,cAAc,GAAGA,cAAc,CAACH,KAAf,CAAqBb,MAAM,CAACG,YAAP,CAAoBQ,MAAzC,CAAjB;;QACA,IAAIK,cAAc,CAACL,MAAf,KAA0B,CAA9B,EAAiC;UAChC,IAAIM,CAAC,KAAKX,IAAI,CAACK,MAAL,GAAc,CAAxB,EAA2B;YAC1B,OAAO;cACNV,KAAK,EAAE,IADD;cAENE,YAAY,EAAEE;YAFR,CAAP;UAIA,CALD,MAKO;YACN,OAAO;cACNO,YAAY,EAAE,IADR,CAEN;;YAFM,CAAP;UAIA;QACD;MACD,CAhBM,MAgBA;QACN;QACA,IAAIZ,MAAM,CAACY,YAAX,EAAyB;UACxB,OAAO;YACNA,YAAY,EAAE,IADR,CAEN;;UAFM,CAAP;QAIA,CALD,MAKO;UACN,MAAM,IAAIb,KAAJ,sCAAwCoB,IAAI,CAACC,SAAL,CAAepB,MAAf,EAAuB,IAAvB,EAA6B,CAA7B,CAAxC,EAAN;QACA;MACD;;MACDiB,CAAC;IACD,CAtCuB,CAuCxB;IACA;;IACA;;;IACA,IAAIV,IAAJ,EAAU;MACT,OAAO;QACNH,QAAQ,EAAE;MADJ,CAAP;IAGA;;IACD,OAAO;MACNH,KAAK,EAAE,IADD;MAENE,YAAY,EAAEE,UAAU,CAACQ,KAAX,CAAiB,CAAjB,EAAoBR,UAAU,CAACM,MAAX,GAAoBK,cAAc,CAACL,MAAvD;IAFR,CAAP;EAIA;;EAED,QAAQL,IAAI,CAACe,EAAb;IACC,KAAK,GAAL;MACC,IAAIT,YAAJ;;MACA,qDAAqBN,IAAI,CAACgB,IAA1B,wCAAgC;QAAA,IAArBC,MAAqB;;QAC/B,IAAMvB,OAAM,GAAGC,MAAK,CAACI,UAAD,EAAakB,MAAb,EAAqBhB,IAArB,CAApB;;QACA,IAAIP,OAAJ,EAAY;UACX,IAAIA,OAAM,CAACI,QAAX,EAAqB;YACpB,OAAOJ,OAAP;UACA,CAFD,MAEO,IAAIA,OAAM,CAACC,KAAX,EAAkB;YACxB,OAAO;cACNA,KAAK,EAAE,IADD;cAENE,YAAY,EAAEH,OAAM,CAACG;YAFf,CAAP;UAIA,CALM,MAKA;YACN;YACA,IAAIH,OAAM,CAACY,YAAX,EAAyB;cACxBA,YAAY,GAAG,IAAf;YACA,CAFD,MAEO;cACN,MAAM,IAAIb,KAAJ,sCAAwCoB,IAAI,CAACC,SAAL,CAAepB,OAAf,EAAuB,IAAvB,EAA6B,CAA7B,CAAxC,EAAN;YACA;UACD;QACD;MACD;;MACD,IAAIY,YAAJ,EAAkB;QACjB,OAAO;UACNA,YAAY,EAAE,IADR,CAEN;;QAFM,CAAP;MAIA,CA3BF,CA4BC;;;MACA;;IAED,KAAK,IAAL;MACC,sDAAmBN,IAAI,CAACgB,IAAxB,2CAA8B;QAAA,IAAnBE,KAAmB;;QAC7B,IAAInB,UAAU,CAAC,CAAD,CAAV,KAAkBmB,KAAtB,EAA4B;UAC3B,IAAInB,UAAU,CAACM,MAAX,KAAsB,CAA1B,EAA6B;YAC5B,OAAO;cACNV,KAAK,EAAE,IADD;cAENE,YAAY,EAAEE;YAFR,CAAP;UAIA;;UACD,IAAIE,IAAJ,EAAU;YACT,OAAO;cACNH,QAAQ,EAAE;YADJ,CAAP;UAGA;;UACD,OAAO;YACNH,KAAK,EAAE,IADD;YAENE,YAAY,EAAE,CAACqB,KAAD;UAFR,CAAP;QAIA;MACD,CAnBF,CAoBC;;;MACA;;IAED;;IACA;MACC,MAAM,IAAIzB,KAAJ,yCAA2CO,IAA3C,EAAN;EAzDF;AA2DA"}