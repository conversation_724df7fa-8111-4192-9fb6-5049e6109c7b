{"version": 3, "file": "matchPhoneNumberStringAgainstPhoneNumber.js", "names": ["matchPhoneNumberStringAgainstPhoneNumber", "phoneNumberString", "phoneNumber", "metadata", "phoneNumberStringContainsCallingCode", "parsedPhoneNumber", "parsePhoneNumber", "defaultCallingCode", "countryCallingCode", "ext", "number", "nationalNumber", "indexOf"], "sources": ["../../source/findNumbers/matchPhoneNumberStringAgainstPhoneNumber.js"], "sourcesContent": ["import parsePhoneNumber from '../parsePhoneNumber.js'\r\n\r\n/**\r\n * Matches a phone number object against a phone number string.\r\n * @param  {string} phoneNumberString\r\n * @param  {PhoneNumber} phoneNumber\r\n * @param  {object} metadata — Metadata JSON\r\n * @return {'INVALID_NUMBER'|'NO_MATCH'|'SHORT_NSN_MATCH'|'NSN_MATCH'|'EXACT_MATCH'}\r\n */\r\nexport default function matchPhoneNumberStringAgainstPhoneNumber(phoneNumberString, phoneNumber, metadata) {\r\n\t// Parse `phoneNumberString`.\r\n\tlet phoneNumberStringContainsCallingCode = true\r\n\tlet parsedPhoneNumber = parsePhoneNumber(phoneNumberString, metadata)\r\n\tif (!parsedPhoneNumber) {\r\n\t\t// If `phoneNumberString` didn't contain a country calling code\r\n\t\t// then substitute it with the `phoneNumber`'s country calling code.\r\n\t\tphoneNumberStringContainsCallingCode = false\r\n\t\tparsedPhoneNumber = parsePhoneNumber(phoneNumberString, { defaultCallingCode: phoneNumber.countryCallingCode }, metadata)\r\n\t}\r\n\tif (!parsedPhoneNumber) {\r\n\t\treturn 'INVALID_NUMBER'\r\n\t}\r\n\r\n\t// Check that the extensions match.\r\n\tif (phoneNumber.ext) {\r\n\t\tif (parsedPhoneNumber.ext !== phoneNumber.ext) {\r\n\t\t\treturn 'NO_MATCH'\r\n\t\t}\r\n\t} else {\r\n\t\tif (parsedPhoneNumber.ext) {\r\n\t\t\treturn 'NO_MATCH'\r\n\t\t}\r\n\t}\r\n\r\n\t// Check that country calling codes match.\r\n\tif (phoneNumberStringContainsCallingCode) {\r\n\t\tif (phoneNumber.countryCallingCode !== parsedPhoneNumber.countryCallingCode) {\r\n\t\t\treturn 'NO_MATCH'\r\n\t\t}\r\n\t}\r\n\r\n\t// Check if the whole numbers match.\r\n\tif (phoneNumber.number === parsedPhoneNumber.number) {\r\n\t\tif (phoneNumberStringContainsCallingCode) {\r\n\t\t\treturn 'EXACT_MATCH'\r\n\t\t} else {\r\n\t\t\treturn 'NSN_MATCH'\r\n\t\t}\r\n\t}\r\n\r\n\t// Check if one national number is a \"suffix\" of the other.\r\n\tif (\r\n\t\tphoneNumber.nationalNumber.indexOf(parsedPhoneNumber.nationalNumber) === 0 ||\r\n\t\tparsedPhoneNumber.nationalNumber.indexOf(phoneNumber.nationalNumber) === 0\r\n\t) {\r\n\t\t// \"A SHORT_NSN_MATCH occurs if there is a difference because of the\r\n\t\t//  presence or absence of an 'Italian leading zero', the presence or\r\n\t\t//  absence of an extension, or one NSN being a shorter variant of the\r\n\t\t//  other.\"\r\n\t\treturn 'SHORT_NSN_MATCH'\r\n\t}\r\n\r\n\treturn 'NO_MATCH'\r\n}"], "mappings": ";;;;;;;AAAA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,wCAAT,CAAkDC,iBAAlD,EAAqEC,WAArE,EAAkFC,QAAlF,EAA4F;EAC1G;EACA,IAAIC,oCAAoC,GAAG,IAA3C;EACA,IAAIC,iBAAiB,GAAG,IAAAC,4BAAA,EAAiBL,iBAAjB,EAAoCE,QAApC,CAAxB;;EACA,IAAI,CAACE,iBAAL,EAAwB;IACvB;IACA;IACAD,oCAAoC,GAAG,KAAvC;IACAC,iBAAiB,GAAG,IAAAC,4BAAA,EAAiBL,iBAAjB,EAAoC;MAAEM,kBAAkB,EAAEL,WAAW,CAACM;IAAlC,CAApC,EAA4FL,QAA5F,CAApB;EACA;;EACD,IAAI,CAACE,iBAAL,EAAwB;IACvB,OAAO,gBAAP;EACA,CAZyG,CAc1G;;;EACA,IAAIH,WAAW,CAACO,GAAhB,EAAqB;IACpB,IAAIJ,iBAAiB,CAACI,GAAlB,KAA0BP,WAAW,CAACO,GAA1C,EAA+C;MAC9C,OAAO,UAAP;IACA;EACD,CAJD,MAIO;IACN,IAAIJ,iBAAiB,CAACI,GAAtB,EAA2B;MAC1B,OAAO,UAAP;IACA;EACD,CAvByG,CAyB1G;;;EACA,IAAIL,oCAAJ,EAA0C;IACzC,IAAIF,WAAW,CAACM,kBAAZ,KAAmCH,iBAAiB,CAACG,kBAAzD,EAA6E;MAC5E,OAAO,UAAP;IACA;EACD,CA9ByG,CAgC1G;;;EACA,IAAIN,WAAW,CAACQ,MAAZ,KAAuBL,iBAAiB,CAACK,MAA7C,EAAqD;IACpD,IAAIN,oCAAJ,EAA0C;MACzC,OAAO,aAAP;IACA,CAFD,MAEO;MACN,OAAO,WAAP;IACA;EACD,CAvCyG,CAyC1G;;;EACA,IACCF,WAAW,CAACS,cAAZ,CAA2BC,OAA3B,CAAmCP,iBAAiB,CAACM,cAArD,MAAyE,CAAzE,IACAN,iBAAiB,CAACM,cAAlB,CAAiCC,OAAjC,CAAyCV,WAAW,CAACS,cAArD,MAAyE,CAF1E,EAGE;IACD;IACA;IACA;IACA;IACA,OAAO,iBAAP;EACA;;EAED,OAAO,UAAP;AACA"}