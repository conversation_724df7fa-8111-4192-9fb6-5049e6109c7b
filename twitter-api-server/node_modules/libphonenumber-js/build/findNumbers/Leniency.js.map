{"version": 3, "file": "Leniency.js", "names": ["POSSIBLE", "phoneNumber", "candidate", "metadata", "VALID", "defaultCountry", "<PERSON><PERSON><PERSON><PERSON>", "containsOnlyValidXChars", "STRICT_GROUPING", "regExpCache", "containsMoreThanOneSlashInNationalNumber", "isNationalPrefixPresentIfRequired", "checkNumberGroupingIsValid", "allNumberGroupsRemainGrouped", "EXACT_GROUPING", "allNumberGroupsAreExactlyPresent", "index", "length", "charAtIndex", "char<PERSON>t", "charAtNextIndex", "matchPhoneNumberStringAgainstPhoneNumber", "substring", "ext", "parseDigits", "_metadata", "__countryCallingCodeSource", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "countryCallingCode", "phoneNumberRegion", "country", "getCountryByCallingCode", "nationalNumber", "format", "chooseFormatForNumber", "numberingPlan", "formats", "nationalPrefixFormattingRule", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "usesNationalPrefix", "Boolean", "nationalPrefix", "firstSlashInBodyIndex", "indexOf", "secondSlashInBodyIndex", "candidateHasCountryCode", "slice", "number", "checkGroups", "Error", "normalizedCandidate", "normalizeDigits", "formattedNumberGroups", "getNationalNumberGroups", "alternateFormats", "MetadataManager", "getAlternateFormatsForCountry", "getCountryCode", "nationalSignificantNumber", "util", "getNationalSignificantNumber", "numberFormats", "alternateFormat", "leadingDigitsPatterns", "leadingDigitsRegExp", "getPatternForRegExp", "test", "formattingPattern", "formatNsnUsingPattern", "split", "rfc3966Format", "formatNumber", "endIndex", "startIndex", "candidateGroups", "NON_DIGITS_PATTERN", "candidateNumberGroupIndex", "hasExtension", "contains", "formattedNumberGroupIndex", "endsWith", "fromIndex", "getCountryCodeSource", "CountryCodeSource", "FROM_DEFAULT_COUNTRY", "countryCode", "String", "i", "region", "getRegionCodeForCountryCode", "getNddPrefixForRegion", "Character", "isDigit", "startsWith", "getExtension"], "sources": ["../../source/findNumbers/Leniency.js"], "sourcesContent": ["import isValidNumber from '../isValid.js'\r\nimport parseDigits from '../helpers/parseDigits.js'\r\nimport matchPhoneNumberStringAgainstPhoneNumber from './matchPhoneNumberStringAgainstPhoneNumber.js'\r\nimport Metadata from '../metadata.js'\r\nimport getCountryByCallingCode from '../helpers/getCountryByCallingCode.js'\r\nimport { chooseFormatForNumber } from '../format.js'\r\n\r\nimport {\r\n\tstartsWith,\r\n\tendsWith\r\n} from './util.js'\r\n\r\n/**\r\n * Leniency when finding potential phone numbers in text segments\r\n * The levels here are ordered in increasing strictness.\r\n */\r\nexport default\r\n{\r\n\t/**\r\n\t * Phone numbers accepted are \"possible\", but not necessarily \"valid\".\r\n\t */\r\n\tPOSSIBLE(phoneNumber, { candidate, metadata })\r\n\t{\r\n\t\treturn true\r\n\t},\r\n\r\n\t/**\r\n\t * Phone numbers accepted are \"possible\" and \"valid\".\r\n\t * Numbers written in national format must have their national-prefix\r\n\t * present if it is usually written for a number of this type.\r\n\t */\r\n\tVALID(phoneNumber, { candidate, defaultCountry, metadata })\r\n\t{\r\n\t\tif (\r\n\t\t\t!phoneNumber.isValid() ||\r\n\t\t\t!containsOnlyValidXChars(phoneNumber, candidate, metadata)\r\n\t\t)\r\n\t\t{\r\n\t\t\treturn false\r\n\t\t}\r\n\r\n\t\t// Skipped for simplicity.\r\n\t\t// return isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\r\n\t\treturn true\r\n\t},\r\n\r\n\t/**\r\n\t * Phone numbers accepted are \"valid\" and\r\n\t * are grouped in a possible way for this locale. For example, a US number written as\r\n\t * \"65 02 53 00 00\" and \"650253 0000\" are not accepted at this leniency level, whereas\r\n\t * \"************\", \"650 2530000\" or \"6502530000\" are.\r\n\t * Numbers with more than one '/' symbol in the national significant number\r\n\t * are also dropped at this level.\r\n\t *\r\n\t * Warning: This level might result in lower coverage especially for regions outside of\r\n\t * country code \"+1\". If you are not sure about which level to use,\r\n\t * email the <NAME_EMAIL>.\r\n\t */\r\n\tSTRICT_GROUPING(phoneNumber, { candidate, defaultCountry, metadata, regExpCache })\r\n\t{\r\n\t\tif (\r\n\t\t\t!phoneNumber.isValid() ||\r\n\t\t\t!containsOnlyValidXChars(phoneNumber, candidate, metadata) ||\r\n\t\t\tcontainsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) ||\r\n\t\t\t!isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\r\n\t\t)\r\n\t\t{\r\n\t\t\treturn false\r\n\t\t}\r\n\r\n\t\treturn checkNumberGroupingIsValid\r\n\t\t(\r\n\t\t\tphoneNumber,\r\n\t\t\tcandidate,\r\n\t\t\tmetadata,\r\n\t\t\tallNumberGroupsRemainGrouped,\r\n\t\t\tregExpCache\r\n\t\t)\r\n\t},\r\n\r\n\t/**\r\n\t * Phone numbers accepted are \"valid\" and are grouped in the same way\r\n\t * that we would have formatted it, or as a single block.\r\n\t * For example, a US number written as \"650 2530000\" is not accepted\r\n\t * at this leniency level, whereas \"************\" or \"6502530000\" are.\r\n\t * Numbers with more than one '/' symbol are also dropped at this level.\r\n\t *\r\n\t * Warning: This level might result in lower coverage especially for regions outside of\r\n\t * country code \"+1\". If you are not sure about which level to use, email the discussion group\r\n\t * <EMAIL>.\r\n\t */\r\n\tEXACT_GROUPING(phoneNumber, { candidate, defaultCountry, metadata, regExpCache })\r\n\t{\r\n\t\tif (\r\n\t\t\t!phoneNumber.isValid() ||\r\n\t\t\t!containsOnlyValidXChars(phoneNumber, candidate, metadata) ||\r\n\t\t\tcontainsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) ||\r\n\t\t\t!isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\r\n\t\t)\r\n\t\t{\r\n\t\t\treturn false\r\n\t\t}\r\n\r\n\t\treturn checkNumberGroupingIsValid\r\n\t\t(\r\n\t\t\tphoneNumber,\r\n\t\t\tcandidate,\r\n\t\t\tmetadata,\r\n\t\t\tallNumberGroupsAreExactlyPresent,\r\n\t\t\tregExpCache\r\n\t\t)\r\n\t}\r\n}\r\n\r\nfunction containsOnlyValidXChars(phoneNumber, candidate, metadata)\r\n{\r\n\t// The characters 'x' and 'X' can be (1) a carrier code, in which case they always precede the\r\n\t// national significant number or (2) an extension sign, in which case they always precede the\r\n\t// extension number. We assume a carrier code is more than 1 digit, so the first case has to\r\n\t// have more than 1 consecutive 'x' or 'X', whereas the second case can only have exactly 1 'x'\r\n\t// or 'X'. We ignore the character if it appears as the last character of the string.\r\n\tfor (let index = 0; index < candidate.length - 1; index++)\r\n\t{\r\n\t\tconst charAtIndex = candidate.charAt(index)\r\n\r\n\t\tif (charAtIndex === 'x' || charAtIndex === 'X')\r\n\t\t{\r\n\t\t\tconst charAtNextIndex = candidate.charAt(index + 1)\r\n\r\n\t\t\tif (charAtNextIndex === 'x' || charAtNextIndex === 'X')\r\n\t\t\t{\r\n\t\t\t\t// This is the carrier code case, in which the 'X's always precede the national\r\n\t\t\t\t// significant number.\r\n\t\t\t\tindex++\r\n\t\t\t\tif (matchPhoneNumberStringAgainstPhoneNumber(candidate.substring(index), phoneNumber, metadata) !== 'NSN_MATCH')\r\n\t\t\t\t{\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t// This is the extension sign case, in which the 'x' or 'X' should always precede the\r\n\t\t\t\t// extension number.\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tconst ext = parseDigits(candidate.substring(index))\r\n\t\t\t\tif (ext) {\r\n\t\t\t\t\tif (phoneNumber.ext !== ext)  {\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (phoneNumber.ext) {\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn true\r\n}\r\n\r\nfunction isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata: _metadata })\r\n{\r\n\t// First, check how we deduced the country code. If it was written in international format, then\r\n\t// the national prefix is not required.\r\n\tif (phoneNumber.__countryCallingCodeSource !== 'FROM_DEFAULT_COUNTRY')\r\n\t{\r\n\t\treturn true\r\n\t}\r\n\r\n\tconst metadata = new Metadata(_metadata)\r\n\tmetadata.selectNumberingPlan(phoneNumber.countryCallingCode)\r\n\r\n\tconst phoneNumberRegion = phoneNumber.country || getCountryByCallingCode(phoneNumber.countryCallingCode, {\r\n\t\tnationalNumber: phoneNumber.nationalNumber,\r\n\t\tdefaultCountry,\r\n\t\tmetadata\r\n\t})\r\n\r\n\t// Check if a national prefix should be present when formatting this number.\r\n\tconst nationalNumber = phoneNumber.nationalNumber\r\n\tconst format = chooseFormatForNumber(metadata.numberingPlan.formats(), nationalNumber)\r\n\r\n\t// To do this, we check that a national prefix formatting rule was present\r\n\t// and that it wasn't just the first-group symbol ($1) with punctuation.\r\n\tif (format.nationalPrefixFormattingRule())\r\n\t{\r\n\t\tif (metadata.numberingPlan.nationalPrefixIsOptionalWhenFormattingInNationalFormat())\r\n\t\t{\r\n\t\t\t// The national-prefix is optional in these cases, so we don't need to check if it was present.\r\n\t\t\treturn true\r\n\t\t}\r\n\r\n\t\tif (!format.usesNationalPrefix())\r\n\t\t{\r\n\t\t\t// National Prefix not needed for this number.\r\n\t\t\treturn true\r\n\t\t}\r\n\r\n\t\treturn Boolean(phoneNumber.nationalPrefix)\r\n\t}\r\n\r\n\treturn true\r\n}\r\n\r\nexport function containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate)\r\n{\r\n\tconst firstSlashInBodyIndex = candidate.indexOf('/')\r\n\tif (firstSlashInBodyIndex < 0)\r\n\t{\r\n\t\t// No slashes, this is okay.\r\n\t\treturn false\r\n\t}\r\n\r\n\t// Now look for a second one.\r\n\tconst secondSlashInBodyIndex = candidate.indexOf('/', firstSlashInBodyIndex + 1)\r\n\tif (secondSlashInBodyIndex < 0)\r\n\t{\r\n\t\t// Only one slash, this is okay.\r\n\t\treturn false\r\n\t}\r\n\r\n\t// If the first slash is after the country calling code, this is permitted.\r\n\tconst candidateHasCountryCode =\r\n\t\t\tphoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITH_PLUS_SIGN' ||\r\n\t\t\tphoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITHOUT_PLUS_SIGN'\r\n\r\n\tif (candidateHasCountryCode && parseDigits(candidate.substring(0, firstSlashInBodyIndex)) === phoneNumber.countryCallingCode)\r\n\t{\r\n\t\t// Any more slashes and this is illegal.\r\n\t\treturn candidate.slice(secondSlashInBodyIndex + 1).indexOf('/') >= 0\r\n\t}\r\n\r\n\treturn true\r\n}\r\n\r\nfunction checkNumberGroupingIsValid(\r\n\tnumber,\r\n\tcandidate,\r\n\tmetadata,\r\n\tcheckGroups,\r\n\tregExpCache\r\n) {\r\n\tthrow new Error('This part of code hasn\\'t been ported')\r\n\r\n\tconst normalizedCandidate = normalizeDigits(candidate, true /* keep non-digits */)\r\n\tlet formattedNumberGroups = getNationalNumberGroups(metadata, number, null)\r\n\tif (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\r\n\t\treturn true\r\n\t}\r\n\r\n\t// If this didn't pass, see if there are any alternate formats that match, and try them instead.\r\n\tconst alternateFormats = MetadataManager.getAlternateFormatsForCountry(number.getCountryCode())\r\n\tconst nationalSignificantNumber = util.getNationalSignificantNumber(number)\r\n\r\n\tif (alternateFormats) {\r\n\t\tfor (const alternateFormat of alternateFormats.numberFormats()) {\r\n\t\t\tif (alternateFormat.leadingDigitsPatterns().length > 0) {\r\n\t\t\t\t// There is only one leading digits pattern for alternate formats.\r\n\t\t\t\tconst leadingDigitsRegExp = regExpCache.getPatternForRegExp('^' + alternateFormat.leadingDigitsPatterns()[0])\r\n\t\t\t\tif (!leadingDigitsRegExp.test(nationalSignificantNumber)) {\r\n\t\t\t\t\t// Leading digits don't match; try another one.\r\n\t\t\t\t\tcontinue\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tformattedNumberGroups = getNationalNumberGroups(metadata, number, alternateFormat)\r\n\t\t\tif (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn false\r\n}\r\n\r\n/**\r\n * Helper method to get the national-number part of a number, formatted without any national\r\n * prefix, and return it as a set of digit blocks that would be formatted together following\r\n * standard formatting rules.\r\n */\r\nfunction getNationalNumberGroups(\r\n\tmetadata,\r\n\tnumber,\r\n\tformattingPattern\r\n) {\r\n\tthrow new Error('This part of code hasn\\'t been ported')\r\n\r\n\tif (formattingPattern) {\r\n\t\t// We format the NSN only, and split that according to the separator.\r\n\t\tconst nationalSignificantNumber = util.getNationalSignificantNumber(number)\r\n\t\treturn util.formatNsnUsingPattern(nationalSignificantNumber,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tformattingPattern, 'RFC3966', metadata).split('-')\r\n\t}\r\n\r\n\t// This will be in the format +CC-DG1-DG2-DGX;ext=EXT where DG1..DGX represents groups of digits.\r\n\tconst rfc3966Format = formatNumber(number, 'RFC3966', metadata)\r\n\r\n\t// We remove the extension part from the formatted string before splitting it into different\r\n\t// groups.\r\n\tlet endIndex = rfc3966Format.indexOf(';')\r\n\tif (endIndex < 0) {\r\n\t\tendIndex = rfc3966Format.length\r\n\t}\r\n\r\n\t// The country-code will have a '-' following it.\r\n\tconst startIndex = rfc3966Format.indexOf('-') + 1\r\n\treturn rfc3966Format.slice(startIndex, endIndex).split('-')\r\n}\r\n\r\nfunction allNumberGroupsAreExactlyPresent\r\n(\r\n\tmetadata,\r\n\tnumber,\r\n\tnormalizedCandidate,\r\n\tformattedNumberGroups\r\n)\r\n{\r\n\tthrow new Error('This part of code hasn\\'t been ported')\r\n\r\n\tconst candidateGroups = normalizedCandidate.split(NON_DIGITS_PATTERN)\r\n\r\n\t// Set this to the last group, skipping it if the number has an extension.\r\n\tlet candidateNumberGroupIndex =\r\n\t\t\tnumber.hasExtension() ? candidateGroups.length - 2 : candidateGroups.length - 1\r\n\r\n\t// First we check if the national significant number is formatted as a block.\r\n\t// We use contains and not equals, since the national significant number may be present with\r\n\t// a prefix such as a national number prefix, or the country code itself.\r\n\tif (candidateGroups.length == 1\r\n\t\t\t|| candidateGroups[candidateNumberGroupIndex].contains(\r\n\t\t\t\t\tutil.getNationalSignificantNumber(number)))\r\n\t{\r\n\t\treturn true\r\n\t}\r\n\r\n\t// Starting from the end, go through in reverse, excluding the first group, and check the\r\n\t// candidate and number groups are the same.\r\n\tlet formattedNumberGroupIndex = (formattedNumberGroups.length - 1)\r\n\twhile (formattedNumberGroupIndex > 0 && candidateNumberGroupIndex >= 0)\r\n\t{\r\n\t\tif (candidateGroups[candidateNumberGroupIndex] !== formattedNumberGroups[formattedNumberGroupIndex])\r\n\t\t{\r\n\t\t\treturn false\r\n\t\t}\r\n\t\tformattedNumberGroupIndex--\r\n\t\tcandidateNumberGroupIndex--\r\n\t}\r\n\r\n\t// Now check the first group. There may be a national prefix at the start, so we only check\r\n\t// that the candidate group ends with the formatted number group.\r\n\treturn (candidateNumberGroupIndex >= 0\r\n\t\t\t&& endsWith(candidateGroups[candidateNumberGroupIndex], formattedNumberGroups[0]))\r\n}\r\n\r\n\r\nfunction allNumberGroupsRemainGrouped\r\n(\r\n\tmetadata,\r\n\tnumber,\r\n\tnormalizedCandidate,\r\n\tformattedNumberGroups\r\n)\r\n{\r\n\tthrow new Error('This part of code hasn\\'t been ported')\r\n\r\n\tlet fromIndex = 0\r\n\tif (number.getCountryCodeSource() !== CountryCodeSource.FROM_DEFAULT_COUNTRY)\r\n\t{\r\n\t\t// First skip the country code if the normalized candidate contained it.\r\n\t\tconst countryCode = String(number.getCountryCode())\r\n\t\tfromIndex = normalizedCandidate.indexOf(countryCode) + countryCode.length()\r\n\t}\r\n\r\n\t// Check each group of consecutive digits are not broken into separate groupings in the\r\n\t// {@code normalizedCandidate} string.\r\n\tfor (let i = 0; i < formattedNumberGroups.length; i++)\r\n\t{\r\n\t\t// Fails if the substring of {@code normalizedCandidate} starting from {@code fromIndex}\r\n\t\t// doesn't contain the consecutive digits in formattedNumberGroups[i].\r\n\t\tfromIndex = normalizedCandidate.indexOf(formattedNumberGroups[i], fromIndex)\r\n\t\tif (fromIndex < 0) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\t// Moves {@code fromIndex} forward.\r\n\t\tfromIndex += formattedNumberGroups[i].length()\r\n\t\tif (i == 0 && fromIndex < normalizedCandidate.length())\r\n\t\t{\r\n\t\t\t// We are at the position right after the NDC. We get the region used for formatting\r\n\t\t\t// information based on the country code in the phone number, rather than the number itself,\r\n\t\t\t// as we do not need to distinguish between different countries with the same country\r\n\t\t\t// calling code and this is faster.\r\n\t\t\tconst region = util.getRegionCodeForCountryCode(number.getCountryCode())\r\n\t\t\tif (util.getNddPrefixForRegion(region, true) != null\r\n\t\t\t\t\t&& Character.isDigit(normalizedCandidate.charAt(fromIndex))) {\r\n\t\t\t\t// This means there is no formatting symbol after the NDC. In this case, we only\r\n\t\t\t\t// accept the number if there is no formatting symbol at all in the number, except\r\n\t\t\t\t// for extensions. This is only important for countries with national prefixes.\r\n\t\t\t\tconst nationalSignificantNumber = util.getNationalSignificantNumber(number)\r\n\t\t\t\treturn startsWith\r\n\t\t\t\t(\r\n\t\t\t\t\tnormalizedCandidate.slice(fromIndex - formattedNumberGroups[i].length),\r\n\t\t\t\t\t nationalSignificantNumber\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// The check here makes sure that we haven't mistakenly already used the extension to\r\n\t// match the last group of the subscriber number. Note the extension cannot have\r\n\t// formatting in-between digits.\r\n\treturn normalizedCandidate.slice(fromIndex).contains(number.getExtension())\r\n}"], "mappings": ";;;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;;;;;;;;;;AAKA;AACA;AACA;AACA;eAEA;EACC;AACD;AACA;EACCA,QAJD,oBAIUC,WAJV,QAKC;IAAA,IADwBC,SACxB,QADwBA,SACxB;IAAA,IADmCC,QACnC,QADmCA,QACnC;IACC,OAAO,IAAP;EACA,CAPF;;EASC;AACD;AACA;AACA;AACA;EACCC,KAdD,iBAcOH,WAdP,SAeC;IAAA,IADqBC,SACrB,SADqBA,SACrB;IAAA,IADgCG,cAChC,SADgCA,cAChC;IAAA,IADgDF,QAChD,SADgDA,QAChD;;IACC,IACC,CAACF,WAAW,CAACK,OAAZ,EAAD,IACA,CAACC,uBAAuB,CAACN,WAAD,EAAcC,SAAd,EAAyBC,QAAzB,CAFzB,EAIA;MACC,OAAO,KAAP;IACA,CAPF,CASC;IACA;;;IACA,OAAO,IAAP;EACA,CA3BF;;EA6BC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCK,eAzCD,2BAyCiBP,WAzCjB,SA0CC;IAAA,IAD+BC,SAC/B,SAD+BA,SAC/B;IAAA,IAD0CG,cAC1C,SAD0CA,cAC1C;IAAA,IAD0DF,QAC1D,SAD0DA,QAC1D;IAAA,IADoEM,WACpE,SADoEA,WACpE;;IACC,IACC,CAACR,WAAW,CAACK,OAAZ,EAAD,IACA,CAACC,uBAAuB,CAACN,WAAD,EAAcC,SAAd,EAAyBC,QAAzB,CADxB,IAEAO,wCAAwC,CAACT,WAAD,EAAcC,SAAd,CAFxC,IAGA,CAACS,iCAAiC,CAACV,WAAD,EAAc;MAAEI,cAAc,EAAdA,cAAF;MAAkBF,QAAQ,EAARA;IAAlB,CAAd,CAJnC,EAMA;MACC,OAAO,KAAP;IACA;;IAED,OAAOS,0BAA0B,CAEhCX,WAFgC,EAGhCC,SAHgC,EAIhCC,QAJgC,EAKhCU,4BALgC,EAMhCJ,WANgC,CAAjC;EAQA,CA7DF;;EA+DC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCK,cA1ED,0BA0EgBb,WA1EhB,SA2EC;IAAA,IAD8BC,SAC9B,SAD8BA,SAC9B;IAAA,IADyCG,cACzC,SADyCA,cACzC;IAAA,IADyDF,QACzD,SADyDA,QACzD;IAAA,IADmEM,WACnE,SADmEA,WACnE;;IACC,IACC,CAACR,WAAW,CAACK,OAAZ,EAAD,IACA,CAACC,uBAAuB,CAACN,WAAD,EAAcC,SAAd,EAAyBC,QAAzB,CADxB,IAEAO,wCAAwC,CAACT,WAAD,EAAcC,SAAd,CAFxC,IAGA,CAACS,iCAAiC,CAACV,WAAD,EAAc;MAAEI,cAAc,EAAdA,cAAF;MAAkBF,QAAQ,EAARA;IAAlB,CAAd,CAJnC,EAMA;MACC,OAAO,KAAP;IACA;;IAED,OAAOS,0BAA0B,CAEhCX,WAFgC,EAGhCC,SAHgC,EAIhCC,QAJgC,EAKhCY,gCALgC,EAMhCN,WANgC,CAAjC;EAQA;AA9FF,C;;;AAiGA,SAASF,uBAAT,CAAiCN,WAAjC,EAA8CC,SAA9C,EAAyDC,QAAzD,EACA;EACC;EACA;EACA;EACA;EACA;EACA,KAAK,IAAIa,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGd,SAAS,CAACe,MAAV,GAAmB,CAA/C,EAAkDD,KAAK,EAAvD,EACA;IACC,IAAME,WAAW,GAAGhB,SAAS,CAACiB,MAAV,CAAiBH,KAAjB,CAApB;;IAEA,IAAIE,WAAW,KAAK,GAAhB,IAAuBA,WAAW,KAAK,GAA3C,EACA;MACC,IAAME,eAAe,GAAGlB,SAAS,CAACiB,MAAV,CAAiBH,KAAK,GAAG,CAAzB,CAAxB;;MAEA,IAAII,eAAe,KAAK,GAApB,IAA2BA,eAAe,KAAK,GAAnD,EACA;QACC;QACA;QACAJ,KAAK;;QACL,IAAI,IAAAK,oDAAA,EAAyCnB,SAAS,CAACoB,SAAV,CAAoBN,KAApB,CAAzC,EAAqEf,WAArE,EAAkFE,QAAlF,MAAgG,WAApG,EACA;UACC,OAAO,KAAP;QACA,CAPF,CAQC;QACA;;MACA,CAXD,MAYK;QACJ,IAAMoB,GAAG,GAAG,IAAAC,uBAAA,EAAYtB,SAAS,CAACoB,SAAV,CAAoBN,KAApB,CAAZ,CAAZ;;QACA,IAAIO,GAAJ,EAAS;UACR,IAAItB,WAAW,CAACsB,GAAZ,KAAoBA,GAAxB,EAA8B;YAC7B,OAAO,KAAP;UACA;QACD,CAJD,MAIO;UACN,IAAItB,WAAW,CAACsB,GAAhB,EAAqB;YACpB,OAAO,KAAP;UACA;QACD;MACD;IACD;EACD;;EAED,OAAO,IAAP;AACA;;AAED,SAASZ,iCAAT,CAA2CV,WAA3C,SACA;EAAA,IAD0DI,cAC1D,SAD0DA,cAC1D;EAAA,IADoFoB,SACpF,SAD0EtB,QAC1E;;EACC;EACA;EACA,IAAIF,WAAW,CAACyB,0BAAZ,KAA2C,sBAA/C,EACA;IACC,OAAO,IAAP;EACA;;EAED,IAAMvB,QAAQ,GAAG,IAAIwB,qBAAJ,CAAaF,SAAb,CAAjB;EACAtB,QAAQ,CAACyB,mBAAT,CAA6B3B,WAAW,CAAC4B,kBAAzC;EAEA,IAAMC,iBAAiB,GAAG7B,WAAW,CAAC8B,OAAZ,IAAuB,IAAAC,mCAAA,EAAwB/B,WAAW,CAAC4B,kBAApC,EAAwD;IACxGI,cAAc,EAAEhC,WAAW,CAACgC,cAD4E;IAExG5B,cAAc,EAAdA,cAFwG;IAGxGF,QAAQ,EAARA;EAHwG,CAAxD,CAAjD,CAXD,CAiBC;;EACA,IAAM8B,cAAc,GAAGhC,WAAW,CAACgC,cAAnC;EACA,IAAMC,MAAM,GAAG,IAAAC,6BAAA,EAAsBhC,QAAQ,CAACiC,aAAT,CAAuBC,OAAvB,EAAtB,EAAwDJ,cAAxD,CAAf,CAnBD,CAqBC;EACA;;EACA,IAAIC,MAAM,CAACI,4BAAP,EAAJ,EACA;IACC,IAAInC,QAAQ,CAACiC,aAAT,CAAuBG,sDAAvB,EAAJ,EACA;MACC;MACA,OAAO,IAAP;IACA;;IAED,IAAI,CAACL,MAAM,CAACM,kBAAP,EAAL,EACA;MACC;MACA,OAAO,IAAP;IACA;;IAED,OAAOC,OAAO,CAACxC,WAAW,CAACyC,cAAb,CAAd;EACA;;EAED,OAAO,IAAP;AACA;;AAEM,SAAShC,wCAAT,CAAkDT,WAAlD,EAA+DC,SAA/D,EACP;EACC,IAAMyC,qBAAqB,GAAGzC,SAAS,CAAC0C,OAAV,CAAkB,GAAlB,CAA9B;;EACA,IAAID,qBAAqB,GAAG,CAA5B,EACA;IACC;IACA,OAAO,KAAP;EACA,CANF,CAQC;;;EACA,IAAME,sBAAsB,GAAG3C,SAAS,CAAC0C,OAAV,CAAkB,GAAlB,EAAuBD,qBAAqB,GAAG,CAA/C,CAA/B;;EACA,IAAIE,sBAAsB,GAAG,CAA7B,EACA;IACC;IACA,OAAO,KAAP;EACA,CAdF,CAgBC;;;EACA,IAAMC,uBAAuB,GAC3B7C,WAAW,CAACyB,0BAAZ,KAA2C,4BAA3C,IACAzB,WAAW,CAACyB,0BAAZ,KAA2C,+BAF7C;;EAIA,IAAIoB,uBAAuB,IAAI,IAAAtB,uBAAA,EAAYtB,SAAS,CAACoB,SAAV,CAAoB,CAApB,EAAuBqB,qBAAvB,CAAZ,MAA+D1C,WAAW,CAAC4B,kBAA1G,EACA;IACC;IACA,OAAO3B,SAAS,CAAC6C,KAAV,CAAgBF,sBAAsB,GAAG,CAAzC,EAA4CD,OAA5C,CAAoD,GAApD,KAA4D,CAAnE;EACA;;EAED,OAAO,IAAP;AACA;;AAED,SAAShC,0BAAT,CACCoC,MADD,EAEC9C,SAFD,EAGCC,QAHD,EAIC8C,WAJD,EAKCxC,WALD,EAME;EACD,MAAM,IAAIyC,KAAJ,CAAU,uCAAV,CAAN;EAEA,IAAMC,mBAAmB,GAAGC,eAAe,CAAClD,SAAD,EAAY;EAAK;EAAjB,CAA3C;EACA,IAAImD,qBAAqB,GAAGC,uBAAuB,CAACnD,QAAD,EAAW6C,MAAX,EAAmB,IAAnB,CAAnD;;EACA,IAAIC,WAAW,CAAC9C,QAAD,EAAW6C,MAAX,EAAmBG,mBAAnB,EAAwCE,qBAAxC,CAAf,EAA+E;IAC9E,OAAO,IAAP;EACA,CAPA,CASD;;;EACA,IAAME,gBAAgB,GAAGC,eAAe,CAACC,6BAAhB,CAA8CT,MAAM,CAACU,cAAP,EAA9C,CAAzB;EACA,IAAMC,yBAAyB,GAAGC,IAAI,CAACC,4BAAL,CAAkCb,MAAlC,CAAlC;;EAEA,IAAIO,gBAAJ,EAAsB;IACrB,qDAA8BA,gBAAgB,CAACO,aAAjB,EAA9B,wCAAgE;MAAA,IAArDC,eAAqD;;MAC/D,IAAIA,eAAe,CAACC,qBAAhB,GAAwC/C,MAAxC,GAAiD,CAArD,EAAwD;QACvD;QACA,IAAMgD,mBAAmB,GAAGxD,WAAW,CAACyD,mBAAZ,CAAgC,MAAMH,eAAe,CAACC,qBAAhB,GAAwC,CAAxC,CAAtC,CAA5B;;QACA,IAAI,CAACC,mBAAmB,CAACE,IAApB,CAAyBR,yBAAzB,CAAL,EAA0D;UACzD;UACA;QACA;MACD;;MACDN,qBAAqB,GAAGC,uBAAuB,CAACnD,QAAD,EAAW6C,MAAX,EAAmBe,eAAnB,CAA/C;;MACA,IAAId,WAAW,CAAC9C,QAAD,EAAW6C,MAAX,EAAmBG,mBAAnB,EAAwCE,qBAAxC,CAAf,EAA+E;QAC9E,OAAO,IAAP;MACA;IACD;EACD;;EAED,OAAO,KAAP;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASC,uBAAT,CACCnD,QADD,EAEC6C,MAFD,EAGCoB,iBAHD,EAIE;EACD,MAAM,IAAIlB,KAAJ,CAAU,uCAAV,CAAN;;EAEA,IAAIkB,iBAAJ,EAAuB;IACtB;IACA,IAAMT,yBAAyB,GAAGC,IAAI,CAACC,4BAAL,CAAkCb,MAAlC,CAAlC;IACA,OAAOY,IAAI,CAACS,qBAAL,CAA2BV,yBAA3B,EACUS,iBADV,EAC6B,SAD7B,EACwCjE,QADxC,EACkDmE,KADlD,CACwD,GADxD,CAAP;EAEA,CARA,CAUD;;;EACA,IAAMC,aAAa,GAAGC,YAAY,CAACxB,MAAD,EAAS,SAAT,EAAoB7C,QAApB,CAAlC,CAXC,CAaD;EACA;;EACA,IAAIsE,QAAQ,GAAGF,aAAa,CAAC3B,OAAd,CAAsB,GAAtB,CAAf;;EACA,IAAI6B,QAAQ,GAAG,CAAf,EAAkB;IACjBA,QAAQ,GAAGF,aAAa,CAACtD,MAAzB;EACA,CAlBA,CAoBD;;;EACA,IAAMyD,UAAU,GAAGH,aAAa,CAAC3B,OAAd,CAAsB,GAAtB,IAA6B,CAAhD;EACA,OAAO2B,aAAa,CAACxB,KAAd,CAAoB2B,UAApB,EAAgCD,QAAhC,EAA0CH,KAA1C,CAAgD,GAAhD,CAAP;AACA;;AAED,SAASvD,gCAAT,CAECZ,QAFD,EAGC6C,MAHD,EAICG,mBAJD,EAKCE,qBALD,EAOA;EACC,MAAM,IAAIH,KAAJ,CAAU,uCAAV,CAAN;EAEA,IAAMyB,eAAe,GAAGxB,mBAAmB,CAACmB,KAApB,CAA0BM,kBAA1B,CAAxB,CAHD,CAKC;;EACA,IAAIC,yBAAyB,GAC3B7B,MAAM,CAAC8B,YAAP,KAAwBH,eAAe,CAAC1D,MAAhB,GAAyB,CAAjD,GAAqD0D,eAAe,CAAC1D,MAAhB,GAAyB,CADhF,CAND,CASC;EACA;EACA;;EACA,IAAI0D,eAAe,CAAC1D,MAAhB,IAA0B,CAA1B,IACC0D,eAAe,CAACE,yBAAD,CAAf,CAA2CE,QAA3C,CACDnB,IAAI,CAACC,4BAAL,CAAkCb,MAAlC,CADC,CADL,EAGA;IACC,OAAO,IAAP;EACA,CAjBF,CAmBC;EACA;;;EACA,IAAIgC,yBAAyB,GAAI3B,qBAAqB,CAACpC,MAAtB,GAA+B,CAAhE;;EACA,OAAO+D,yBAAyB,GAAG,CAA5B,IAAiCH,yBAAyB,IAAI,CAArE,EACA;IACC,IAAIF,eAAe,CAACE,yBAAD,CAAf,KAA+CxB,qBAAqB,CAAC2B,yBAAD,CAAxE,EACA;MACC,OAAO,KAAP;IACA;;IACDA,yBAAyB;IACzBH,yBAAyB;EACzB,CA9BF,CAgCC;EACA;;;EACA,OAAQA,yBAAyB,IAAI,CAA7B,IACH,IAAAI,cAAA,EAASN,eAAe,CAACE,yBAAD,CAAxB,EAAqDxB,qBAAqB,CAAC,CAAD,CAA1E,CADL;AAEA;;AAGD,SAASxC,4BAAT,CAECV,QAFD,EAGC6C,MAHD,EAICG,mBAJD,EAKCE,qBALD,EAOA;EACC,MAAM,IAAIH,KAAJ,CAAU,uCAAV,CAAN;EAEA,IAAIgC,SAAS,GAAG,CAAhB;;EACA,IAAIlC,MAAM,CAACmC,oBAAP,OAAkCC,iBAAiB,CAACC,oBAAxD,EACA;IACC;IACA,IAAMC,WAAW,GAAGC,MAAM,CAACvC,MAAM,CAACU,cAAP,EAAD,CAA1B;IACAwB,SAAS,GAAG/B,mBAAmB,CAACP,OAApB,CAA4B0C,WAA5B,IAA2CA,WAAW,CAACrE,MAAZ,EAAvD;EACA,CATF,CAWC;EACA;;;EACA,KAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnC,qBAAqB,CAACpC,MAA1C,EAAkDuE,CAAC,EAAnD,EACA;IACC;IACA;IACAN,SAAS,GAAG/B,mBAAmB,CAACP,OAApB,CAA4BS,qBAAqB,CAACmC,CAAD,CAAjD,EAAsDN,SAAtD,CAAZ;;IACA,IAAIA,SAAS,GAAG,CAAhB,EAAmB;MAClB,OAAO,KAAP;IACA,CANF,CAOC;;;IACAA,SAAS,IAAI7B,qBAAqB,CAACmC,CAAD,CAArB,CAAyBvE,MAAzB,EAAb;;IACA,IAAIuE,CAAC,IAAI,CAAL,IAAUN,SAAS,GAAG/B,mBAAmB,CAAClC,MAApB,EAA1B,EACA;MACC;MACA;MACA;MACA;MACA,IAAMwE,MAAM,GAAG7B,IAAI,CAAC8B,2BAAL,CAAiC1C,MAAM,CAACU,cAAP,EAAjC,CAAf;;MACA,IAAIE,IAAI,CAAC+B,qBAAL,CAA2BF,MAA3B,EAAmC,IAAnC,KAA4C,IAA5C,IACCG,SAAS,CAACC,OAAV,CAAkB1C,mBAAmB,CAAChC,MAApB,CAA2B+D,SAA3B,CAAlB,CADL,EAC+D;QAC9D;QACA;QACA;QACA,IAAMvB,yBAAyB,GAAGC,IAAI,CAACC,4BAAL,CAAkCb,MAAlC,CAAlC;QACA,OAAO,IAAA8C,gBAAA,EAEN3C,mBAAmB,CAACJ,KAApB,CAA0BmC,SAAS,GAAG7B,qBAAqB,CAACmC,CAAD,CAArB,CAAyBvE,MAA/D,CAFM,EAGL0C,yBAHK,CAAP;MAKA;IACD;EACD,CA3CF,CA6CC;EACA;EACA;;;EACA,OAAOR,mBAAmB,CAACJ,KAApB,CAA0BmC,SAA1B,EAAqCH,QAArC,CAA8C/B,MAAM,CAAC+C,YAAP,EAA9C,CAAP;AACA"}