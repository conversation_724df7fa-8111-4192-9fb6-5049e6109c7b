{"version": 3, "file": "searchNumbers.test.js", "names": ["describe", "it", "expectedNumbers", "country", "phone", "startsAt", "endsAt", "searchNumbers", "metadata", "number", "should", "deep", "equal", "shift", "length"], "sources": ["../../source/legacy/searchNumbers.test.js"], "sourcesContent": ["import searchNumbers from './searchNumbers.js'\r\nimport metadata from '../../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('searchNumbers', () => {\r\n\tit('should iterate', () => {\r\n\t\tconst expectedNumbers =[{\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\t// number   : '(*************',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}]\r\n\r\n\t\tfor (const number of searchNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\tnumber.should.deep.equal(expectedNumbers.shift())\r\n\t\t}\r\n\r\n\t\texpectedNumbers.length.should.equal(0)\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;;;;;;;;;AAEAA,QAAQ,CAAC,eAAD,EAAkB,YAAM;EAC/BC,EAAE,CAAC,gBAAD,EAAmB,YAAM;IAC1B,IAAMC,eAAe,GAAE,CAAC;MACvBC,OAAO,EAAG,IADa;MAEvBC,KAAK,EAAK,YAFa;MAGvB;MACAC,QAAQ,EAAG,EAJY;MAKvBC,MAAM,EAAK;IALY,CAAD,EAMpB;MACFH,OAAO,EAAG,IADR;MAEFC,KAAK,EAAK,YAFR;MAGF;MACAC,QAAQ,EAAG,EAJT;MAKFC,MAAM,EAAK;IALT,CANoB,CAAvB;;IAcA,qDAAqB,IAAAC,yBAAA,EAAc,qFAAd,EAAqG,IAArG,EAA2GC,uBAA3G,CAArB,wCAA2I;MAAA,IAAhIC,MAAgI;MAC1IA,MAAM,CAACC,MAAP,CAAcC,IAAd,CAAmBC,KAAnB,CAAyBV,eAAe,CAACW,KAAhB,EAAzB;IACA;;IAEDX,eAAe,CAACY,MAAhB,CAAuBJ,MAAvB,CAA8BE,KAA9B,CAAoC,CAApC;EACA,CApBC,CAAF;AAqBA,CAtBO,CAAR"}