{"version": 3, "file": "parsePhoneNumber.js", "names": ["parsePhoneNumber", "normalizeArguments", "arguments", "text", "options", "metadata", "parsePhoneNumber_"], "sources": ["../source/parsePhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumber_ from './parsePhoneNumber_.js'\r\n\r\nexport default function parsePhoneNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn parsePhoneNumber_(text, options, metadata)\r\n}\r\n"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEe,SAASA,gBAAT,GAA4B;EAC1C,0BAAoC,IAAAC,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,OAAO,IAAAC,6BAAA,EAAkBH,IAAlB,EAAwBC,OAAxB,EAAiCC,QAAjC,CAAP;AACA"}