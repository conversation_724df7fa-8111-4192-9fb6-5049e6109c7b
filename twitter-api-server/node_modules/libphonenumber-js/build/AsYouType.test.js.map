{"version": 3, "file": "AsYouType.test.js", "names": ["AsYouType", "country_code", "metadata", "AsYouType_", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "describe", "it", "input", "should", "equal", "formatter", "template", "populatedNationalNumberTemplate", "expect", "to", "be", "undefined", "getTemplate", "type", "getCountry", "getCountryCallingCode", "reset", "getNationalNumber", "asYouType", "thrower", "getNumber", "phoneNumber", "country", "countryCallingCode", "number", "nationalNumber", "state", "nationalSignificantNumber", "nationalPrefix", "isPossible", "<PERSON><PERSON><PERSON><PERSON>", "getChars", "complexPrefixBeforeNationalSignificantNumber", "carrierCode", "defaultCallingCode", "defaultCountry", "chosenFormat", "format", "formats", "nationalPrefixFormattingRule", "getSeparatorAfterNationalPrefix", "formatter2", "format2", "isInternational", "formatterInt", "formatterIntRu", "getNumberValue", "not", "formatter3", "something"], "sources": ["../source/AsYouType.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport AsYouType_ from './AsYouType.js'\r\n\r\nclass AsYouType extends AsYouType_ {\r\n\tconstructor(country_code) {\r\n\t\tsuper(country_code, metadata)\r\n\t}\r\n}\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\ndescribe('AsYouType', () => {\r\n\tit('should use \"national_prefix_formatting_rule\"', () => {\r\n\t\t// With national prefix (full).\r\n\t\tnew AsYouType('RU').input('88005553535').should.equal('8 (800) 555-35-35')\r\n\t\t// With national prefix (partial).\r\n\t\tnew AsYouType('RU').input('880055535').should.equal('8 (800) 555-35')\r\n\t})\r\n\r\n\tit('should populate national number template (digit by digit)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\tformatter.input('1')\r\n\t\t// formatter.formatter.template.should.equal('x (xxx) xxx-xxxx')\r\n\t\tformatter.formatter.template.should.equal('x xxx-xxxx')\r\n\t\t// formatter.formatter.populatedNationalNumberTemplate.should.equal('1 (xxx) xxx-xxxx')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('1 xxx-xxxx')\r\n\t\tformatter.input('213')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('1 (213) xxx-xxxx')\r\n\t\tformatter.input('3734253')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('****************')\r\n\t})\r\n\r\n\tit('should populate international number template (digit by digit) (default country)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\tformatter.input('').should.equal('')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\tformatter.input('+').should.equal('+')\r\n\t\tformatter.getTemplate().should.equal('x')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\tformatter.input('1').should.equal('+1')\r\n\t\tformatter.getTemplate().should.equal('xx')\r\n\t\t// Hasn't started formatting the phone number using the template yet.\r\n\t\t// formatter.formatter.template.should.equal('xx xxx xxx xxxx')\r\n\t\tformatter.formatter.template.should.equal('xx xxx xxxx')\r\n\t\t// formatter.formatter.populatedNationalNumberTemplate.should.equal('xxx xxx xxxx')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('xxx xxxx')\r\n\t\t// Has some national number digits, starts formatting the phone number using the template.\r\n\t\tformatter.input('213')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('213 xxx xxxx')\r\n\t\tformatter.input('3734253')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('************')\r\n\t})\r\n\r\n\tit('should populate international number template (digit by digit)', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\tformatter.input('').should.equal('')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\tformatter.input('+').should.equal('+')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\tformatter.input('1').should.equal('+1')\r\n\t\t// formatter.formatter.template.should.equal('xx xxx xxx xxxx')\r\n\t\tformatter.formatter.template.should.equal('xx xxx xxxx')\r\n\t\t// Hasn't yet started formatting the phone number using the template.\r\n\t\t// formatter.formatter.populatedNationalNumberTemplate.should.equal('xxx xxx xxxx')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('xxx xxxx')\r\n\t\t// Has some national number digits, starts formatting the phone number using the template.\r\n\t\tformatter.input('213')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('213 xxx xxxx')\r\n\t\tformatter.input('3734253')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('************')\r\n\t})\r\n\r\n\tit('should populate national number template (attempt to format complete number)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\tformatter.input('1**********').should.equal('****************')\r\n\t\tformatter.formatter.template.should.equal('x (xxx) xxx-xxxx')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('****************')\r\n\t})\r\n\r\n\tit('should parse and format phone numbers as you type', () => {\r\n\t\t// International number test\r\n\t\tnew AsYouType().input('+12133734').should.equal('****** 373 4')\r\n\t\t// Local number test\r\n\t\tnew AsYouType('US').input('2133734').should.equal('(213) 373-4')\r\n\r\n\t\t// US national number retains national prefix.\r\n\t\tnew AsYouType('US').input('12133734').should.equal('1 (213) 373-4')\r\n\r\n\t\t// US national number retains national prefix (full number).\r\n\t\tnew AsYouType('US').input('1**********').should.equal('****************')\r\n\r\n\t\tlet formatter\r\n\r\n\t\t// // Should discard national prefix from a \"complete\" phone number.\r\n\t\t// new AsYouType('RU').input('8800555353').should.equal('880 055-53-53')\r\n\r\n\t\t// Shouldn't extract national prefix when inputting in international format.\r\n\t\tnew AsYouType('RU').input('+7800555353').should.equal('****** 555 35 3')\r\n\r\n\t\tnew AsYouType('CH').input('044-668-1').should.equal('044 668 1')\r\n\r\n\t\t// Test International phone number (international)\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\ttype(formatter.getCountry()).should.equal('undefined')\r\n\t\ttype(formatter.getCountryCallingCode()).should.equal('undefined')\r\n\t\tformatter.getTemplate().should.equal('')\r\n\r\n\t\tformatter.input('+').should.equal('+')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\ttype(formatter.getCountry()).should.equal('undefined')\r\n\t\ttype(formatter.getCountryCallingCode()).should.equal('undefined')\r\n\t\tformatter.getTemplate().should.equal('x')\r\n\r\n\t\tformatter.input('1').should.equal('+1')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\ttype(formatter.getCountry()).should.equal('undefined')\r\n\t\tformatter.getCountryCallingCode().should.equal('1')\r\n\t\tformatter.getTemplate().should.equal('xx')\r\n\r\n\t\tformatter.input('2').should.equal('****')\r\n\t\tformatter.getTemplate().should.equal('xx x')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\ttype(formatter.getCountry()).should.equal('undefined')\r\n\r\n\t\tformatter.input('1').should.equal('****1')\r\n\t\tformatter.input('3').should.equal('******')\r\n\t\tformatter.input(' ').should.equal('******')\r\n\t\tformatter.input('3').should.equal('****** 3')\r\n\t\tformatter.input('3').should.equal('****** 33')\r\n\t\tformatter.input('3').should.equal('****** 333')\r\n\t\tformatter.input('4').should.equal('****** 333 4')\r\n\t\tformatter.input('4').should.equal('****** 333 44')\r\n\t\tformatter.input('4').should.equal('****** 333 444')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\ttype(formatter.getCountry()).should.equal('undefined')\r\n\r\n\t\tformatter.input('4').should.equal('****** 333 4444')\r\n\r\n\t\t// formatter.valid.should.be.true\r\n\t\tformatter.getCountry().should.equal('US')\r\n\t\t// This one below contains \"punctuation spaces\"\r\n\t\t// along with the regular spaces\r\n\t\tformatter.getTemplate().should.equal('xx xxx xxx xxxx')\r\n\r\n\t\tformatter.input('5').should.equal('******33344445')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\tformatter.getCountryCallingCode().should.equal('1')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\r\n\t\t// Check that clearing an international formatter\r\n\t\t// also clears country metadata.\r\n\r\n\t\tformatter.reset()\r\n\r\n\t\tformatter.input('+').should.equal('+')\r\n\t\tformatter.input('7').should.equal('+7')\r\n\t\tformatter.input('9').should.equal('****')\r\n\t\tformatter.input('99 111 22 33').should.equal('****** 111 22 33')\r\n\r\n\t\t// Test Switzerland phone numbers\r\n\r\n\t\tformatter = new AsYouType('CH')\r\n\r\n\t\tformatter.input(' ').should.equal('')\r\n\t\tformatter.input('0').should.equal('0')\r\n\t\tformatter.input('4').should.equal('04')\r\n\t\tformatter.input(' ').should.equal('04')\r\n\t\tformatter.input('-').should.equal('04')\r\n\t\tformatter.input('4').should.equal('044')\r\n\t\tformatter.input('-').should.equal('044')\r\n\t\tformatter.input('6').should.equal('044 6')\r\n\t\tformatter.input('6').should.equal('044 66')\r\n\t\tformatter.input('8').should.equal('044 668')\r\n\t\tformatter.input('-').should.equal('044 668')\r\n\t\tformatter.input('1').should.equal('044 668 1')\r\n\t\tformatter.input('8').should.equal('044 668 18')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\tformatter.getCountry().should.equal('CH')\r\n\t\tformatter.formatter.template.should.equal('xxx xxx xx xx')\r\n\t\tformatter.getTemplate().should.equal('xxx xxx xx')\r\n\r\n\t\tformatter.input(' 00').should.equal('044 668 18 00')\r\n\r\n\t\t// formatter.valid.should.be.true\r\n\t\tformatter.getCountry().should.equal('CH')\r\n\t\tformatter.getTemplate().should.equal('xxx xxx xx xx')\r\n\r\n\t\tformatter.input('9').should.equal('04466818009')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\tformatter.getCountry().should.equal('CH')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\r\n\t\t// Kazakhstan (non-main country for +7 country phone code)\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\tformatter.input('+77172580659')\r\n\t\tformatter.getCountry().should.equal('KZ')\r\n\r\n\t\t// Brazil\r\n\r\n\t\tformatter = new AsYouType('BR')\r\n\t\tformatter.input('11987654321').should.equal('(11) 98765-4321')\r\n\r\n\t\t// UK (Jersey) (non-main country for +44 country phone code)\r\n\r\n\t\tformatter = new AsYouType()\r\n\t\tformatter.input('+447700300000').should.equal('+44 7700 300000')\r\n\t\tformatter.getTemplate().should.equal('xxx xxxx xxxxxx')\r\n\t\tformatter.getCountry().should.equal('JE')\r\n\r\n\t\t// Braces must be part of the template.\r\n\t\tformatter = new AsYouType('RU')\r\n\t\tformatter.input('88005553535').should.equal('8 (800) 555-35-35')\r\n\t\tformatter.getTemplate().should.equal('x (xxx) xxx-xx-xx')\r\n\r\n\t\t// Test Russian phone numbers\r\n\t\t// (with optional national prefix `8`)\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\tformatter.input('8').should.equal('8')\r\n\t\tformatter.input('9').should.equal('8 9')\r\n\t\tformatter.input('9').should.equal('8 99')\r\n\t\tformatter.input('9').should.equal('8 (999)')\r\n\t\tformatter.input('-').should.equal('8 (999)')\r\n\t\tformatter.input('1234').should.equal('8 (999) 123-4')\r\n\t\tformatter.input('567').should.equal('8 (999) 123-45-67')\r\n\t\tformatter.input('8').should.equal('899912345678')\r\n\r\n\t\t// Shouldn't strip national prefix if it is optional\r\n\t\t// and if it's a valid phone number (international).\r\n\t\tformatter = new AsYouType('RU')\r\n\t\t// formatter.input('8005553535').should.equal('(800) 555-35-35')\r\n\t\tformatter.input('+78005553535').should.equal('****** 555 35 35')\r\n\t\tformatter.getNationalNumber().should.equal('8005553535')\r\n\r\n\t\t// Check that clearing an national formatter:\r\n\t\t//  * doesn't clear country metadata\r\n\t\t//  * clears all other things\r\n\r\n\t\tformatter.reset()\r\n\r\n\t\tformatter.input('8').should.equal('8')\r\n\t\tformatter.input('9').should.equal('8 9')\r\n\t\tformatter.input('9').should.equal('8 99')\r\n\t\tformatter.input('9').should.equal('8 (999)')\r\n\t\tformatter.input('-').should.equal('8 (999)')\r\n\t\tformatter.input('1234').should.equal('8 (999) 123-4')\r\n\t\tformatter.input('567').should.equal('8 (999) 123-45-67')\r\n\t\tformatter.input('8').should.equal('899912345678')\r\n\r\n\t\t// National prefix should not be prepended\r\n\t\t// when formatting local NANPA phone numbers.\r\n\t\tnew AsYouType('US').input('1').should.equal('1')\r\n\t\tnew AsYouType('US').input('12').should.equal('1 2')\r\n\t\tnew AsYouType('US').input('123').should.equal('1 23')\r\n\r\n\t\t// Bulgaria\r\n\t\t// (should not prepend national prefix `0`)\r\n\t\tnew AsYouType('BG').input('111 222 3').should.equal('1112223')\r\n\r\n\t\t// Deutchland\r\n\t\tnew AsYouType().input('+4915539898001').should.equal('+49 15539 898001')\r\n\r\n\t\t// KZ detection\r\n\t\tformatter = new AsYouType()\r\n\t\tformatter.input('****** 211 1111')\r\n\t\tformatter.getCountry().should.equal('KZ')\r\n\t\t// formatter.valid.should.equal(true)\r\n\r\n\t\t// New Zealand formatting fix (issue #89)\r\n\t\tnew AsYouType('NZ').input('0212').should.equal('021 2')\r\n\r\n\t\t// South Korea\r\n\t\tformatter = new AsYouType()\r\n\t\tformatter.input('+82111111111').should.equal('+82 11 111 1111')\r\n\t\tformatter.getTemplate().should.equal('xxx xx xxx xxxx')\r\n\t})\r\n\r\n\tit('should filter out formats that require a national prefix and no national prefix has been input', () => {\r\n\t\t// Afghanistan.\r\n\t\tconst formatter = new AsYouType('AF')\r\n\r\n\t\t// No national prefix, and national prefix is required in the format.\r\n\t\t// (not `\"national_prefix_is_optional_when_formatting\": true`)\r\n\t\tformatter.input('44444444').should.equal('44444444')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\r\n\t\t// With national prefix\r\n\t\tformatter.reset().input('044444444').should.equal('044 444 444')\r\n\t\tformatter.formatter.template.should.equal('xxx xxx xxxx')\r\n\t})\r\n\r\n\tit('should work when a digit is not a national prefix but a part of a valid national number', () => {\r\n\t\t// In Russia, `8` could be both a valid national prefix\r\n\t\t// and a part of a valid national number.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\t// The formatter could try both variants:\r\n\t\t// with extracting national prefix\r\n\t\t// and without extracting it,\r\n\t\t// and then choose whichever way has `this.matchingFormats`.\r\n\t\t// Or there could be two instances of the formatter:\r\n\t\t// one that extracts national prefix and one that doesn't,\r\n\t\t// and then the one that has `this.matchingFormats` would be\r\n\t\t// used to format the phone number.\r\n\t\t// Something like an option `extractNationalPrefix: false`\r\n\t\t// and creating `this.withNationalPrefixFormatter = new AsYouType(this.defaultCountry || this.defaultCallingCode, { metadata, extractNationalPrefix: false })`\r\n\t\t// and something like `this.withNationalPrefixFormatter.input(nextDigits)` in `input(nextDigits)`.\r\n\t\t// But, for this specific case, it's not required:\r\n\t\t// in Russia, people are used to inputting `800` numbers with national prefix `8`:\r\n\t\t// `8 800 555 35 35`.\r\n\t\t// formatter.input('8005553535').should.equal('(800) 555-35-35')\r\n\t\tformatter.input('8005553535').should.equal('8005553535')\r\n\t\tformatter.reset()\r\n\t\tformatter.input('+78005553535').should.equal('****** 555 35 35')\r\n\t})\r\n\r\n\tit('should match formats that require a national prefix and no national prefix has been input (national prefix is mandatory for a format)', () => {\r\n\t\tconst formatter = new AsYouType('FR')\r\n\t\tformatter.input('612345678').should.equal('612345678')\r\n\t\tformatter.reset()\r\n\t\tformatter.input('0612345678').should.equal('06 12 34 56 78')\r\n\t})\r\n\r\n\tit('should match formats that require a national prefix and no national prefix has been input (national prefix is not mandatory for a format)', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\t// Without national prefix.\r\n\t\tformatter.input('9991234567').should.equal('999 123-45-67')\r\n\t\tformatter.reset()\r\n\t\t// With national prefix.\r\n\t\tformatter.input('89991234567').should.equal('8 (999) 123-45-67')\r\n\t})\r\n\r\n\tit('should not use `national_prefix_formatting_rule` when formatting international phone numbers', () => {\r\n\t\t// Brazil.\r\n\t\t// `national_prefix_formatting_rule` is `($1)`.\r\n\t\t// Should not add braces around `12` when being input in international format.\r\n\t\tnew AsYouType().input('+55123456789').should.equal('+55 12 3456 789')\r\n\t\tnew AsYouType('BR').input('+55123456789').should.equal('+55 12 3456 789')\r\n\t\tnew AsYouType('BR').input('123456789').should.equal('(12) 3456-789')\r\n\t})\r\n\r\n\tit('should support incorrectly entered international phone numbers (with a national prefix)', () => {\r\n\t\tlet formatter\r\n\r\n\t\tformatter = new AsYouType()\r\n\t\tformatter.input('**** ************').should.equal('**** ************')\r\n\t\t// formatter.input('**** ************').should.equal('**** 8772155230')\r\n\t\tformatter.getNationalNumber().should.equal('8772155230')\r\n\r\n\t\t// They've added another number format that has `8` leading digit\r\n\t\t// and 14 digits. Maybe it's something related to Kazakhstan.\r\n\t\t// formatter = new AsYouType()\r\n\t\t// formatter.input('+78800555353').should.equal('****80 055 53 53')\r\n\t\t// formatter.input('5').should.equal('**** 800 555 35 35')\r\n\t\t// formatter.getNationalNumber().should.equal('8005553535')\r\n\t})\r\n\r\n\tit('should return a partial template for current value', () => {\r\n\t\tconst asYouType = new AsYouType('US')\r\n\r\n\t\tasYouType.input('').should.equal('')\r\n\t\tasYouType.getTemplate().should.equal('')\r\n\r\n\t\tasYouType.input('2').should.equal('2')\r\n\t\t// asYouType.getTemplate().should.equal('x')\r\n\t\t// Doesn't format for a single digit.\r\n\t\tasYouType.getTemplate().should.equal('x')\r\n\r\n\t\tasYouType.input('1').should.equal('21')\r\n\t\tasYouType.getTemplate().should.equal('xx')\r\n\r\n\t\tasYouType.input('3').should.equal('(213)')\r\n\t\tasYouType.getTemplate().should.equal('(xxx)')\r\n\t})\r\n\r\n\tit(`should fall back to the default country`, () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\r\n\t\tformatter.input('8').should.equal('8')\r\n\t\tformatter.input('9').should.equal('8 9')\r\n\t\tformatter.input('9').should.equal('8 99')\r\n\t\tformatter.input('9').should.equal('8 (999)')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\tformatter.formatter.template.should.equal('x (xxx) xxx-xx-xx')\r\n\t\tformatter.getCountry().should.equal('RU')\r\n\t\t// formatter.getCountryCallingCode().should.equal('7')\r\n\r\n\t\tformatter.input('000000000000').should.equal('8999000000000000')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\tformatter.getCountry().should.equal('RU')\r\n\t\t// formatter.getCountryCallingCode().should.equal('7')\r\n\r\n\t\tformatter.reset()\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t// formatter.getCountryCallingCode().should.equal('7')\r\n\r\n\t\tformatter.input('******-373-4253').should.equal('****** 373 4253')\r\n\r\n\t\t// formatter.valid.should.be.true\r\n\t\tformatter.getTemplate().should.equal('xx xxx xxx xxxx')\r\n\t\tformatter.getCountry().should.equal('US')\r\n\t\tformatter.getCountryCallingCode().should.equal('1')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet formatter\r\n\t\tlet thrower\r\n\r\n\t\t// No metadata\r\n\t\tthrower = () => new AsYouType_('RU')\r\n\t\tthrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// Second '+' sign\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\tformatter.input('+').should.equal('+')\r\n\t\tformatter.input('7').should.equal('+7')\r\n\t\tformatter.input('+').should.equal('+7')\r\n\r\n\t\t// Out-of-position '+' sign\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\tformatter.input('8').should.equal('8')\r\n\t\tformatter.input('+').should.equal('8')\r\n\r\n\t\t// No format matched\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\tformatter.input('88005553535').should.equal('8 (800) 555-35-35')\r\n\t\tformatter.input('0').should.equal('880055535350')\r\n\r\n\t\t// Invalid country phone code\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\tformatter.input('+0123').should.equal('+0123')\r\n\r\n\t\t// No country specified and not an international number\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\tformatter.input('88005553535').should.equal('88005553535')\r\n\r\n\t\t// Extract national prefix when no `national_prefix` is set\r\n\r\n\t\tformatter = new AsYouType('AD')\r\n\r\n\t\tformatter.input('155555').should.equal('155 555')\r\n\r\n\t\t// Typing nonsense\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\tformatter.input('+1abc2').should.equal('+1')\r\n\r\n\t\t// Should reset default country when explicitly\r\n\t\t// typing in an international phone number\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\tformatter.input('+')\r\n\t\ttype(formatter.getCountry()).should.equal('undefined')\r\n\t\ttype(formatter.getCountryCallingCode()).should.equal('undefined')\r\n\r\n\t\t// Country not inferrable from the phone number,\r\n\t\t// while the phone number itself can already be formatted \"completely\".\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\tformatter.input('+12223333333')\r\n\t\ttype(formatter.getCountry()).should.equal('undefined')\r\n\t\tformatter.getCountryCallingCode().should.equal('1')\r\n\r\n\t\t// Reset a chosen format when it no longer applies given the new leading digits.\r\n\t\t// If Google changes metadata for England then this test might not cover the case.\r\n\t\tformatter = new AsYouType('GB')\r\n\t\tformatter.input('0845').should.equal('0845')\r\n\t\t// New leading digits don't match the format previously chosen.\r\n\t\t// Reset the format.\r\n\t\tformatter.input('0').should.equal('0845 0')\r\n\t})\r\n\r\n\tit('should choose between matching formats based on the absence or presence of a national prefix', () => {\r\n\t\t// The first matching format:\r\n\t\t// {\r\n\t\t//    \"pattern\": \"(\\\\d{2})(\\\\d{5,6})\",\r\n\t\t//    \"leading_digits_patterns\": [\r\n\t\t//       \"(?:10|2[0-57-9])[19]\",\r\n\t\t//       \"(?:10|2[0-57-9])(?:10|9[56])\",\r\n\t\t//       \"(?:10|2[0-57-9])(?:100|9[56])\"\r\n\t\t//    ],\r\n\t\t//    \"national_prefix_formatting_rule\": \"0$1\",\r\n\t\t//    \"format\": \"$1 $2\",\r\n\t\t//    \"domestic_carrier_code_formatting_rule\": \"$CC $FG\"\r\n\t\t// }\r\n\t\t//\r\n\t\t// The second matching format:\r\n\t\t// {\r\n\t\t//    \"pattern\": \"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\r\n\t\t//    \"leading_digits_patterns\": [\r\n\t\t//       \"10|2(?:[02-57-9]|1[1-9])\",\r\n\t\t//       \"10|2(?:[02-57-9]|1[1-9])\",\r\n\t\t//       \"10[0-79]|2(?:[02-57-9]|1[1-79])|(?:10|21)8(?:0[1-9]|[1-9])\"\r\n\t\t//    ],\r\n\t\t//    \"national_prefix_formatting_rule\": \"0$1\",\r\n\t\t//    \"national_prefix_is_optional_when_formatting\": true,\r\n\t\t//    \"format\": \"$1 $2 $3\",\r\n\t\t//    \"domestic_carrier_code_formatting_rule\": \"$CC $FG\"\r\n\t\t// }\r\n\t\t//\r\n\t\tconst formatter = new AsYouType('CN')\r\n\t\t// National prefix has been input.\r\n\t\t// Chooses the first format.\r\n\t\tformatter.input('01010000').should.equal('010 10000')\r\n\t\tformatter.reset()\r\n\t\t// No national prefix has been input,\r\n\t\t// and `national_prefix_for_parsing` not matched.\r\n\t\t// The first format won't match, because it doesn't have\r\n\t\t// `\"national_prefix_is_optional_when_formatting\": true`.\r\n\t\t// The second format will match, because it does have\r\n\t\t// `\"national_prefix_is_optional_when_formatting\": true`.\r\n\t\tformatter.input('1010000').should.equal('10 1000 0')\r\n\t})\r\n\r\n\tit('should not accept phone number extensions', () => {\r\n\t\tnew AsYouType().input('******-373-4253 ext. 123').should.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should parse non-European digits', () => {\r\n\t\tnew AsYouType().input('+١٢١٢٢٣٢٣٢٣٢').should.equal('****** 232 3232')\r\n\t})\r\n\r\n\tit('should return a PhoneNumber instance', () => {\r\n\t\tconst formatter = new AsYouType('BR')\r\n\r\n\t\t// No country calling code.\r\n\t\texpect(formatter.getNumber()).to.be.undefined\r\n\r\n\t\tformatter.input('+1')\r\n\t\t// No national number digits.\r\n\t\texpect(formatter.getNumber()).to.be.undefined\r\n\r\n\t\tformatter.input('************')\r\n\r\n\t\tlet phoneNumber = formatter.getNumber()\r\n\t\tphoneNumber.country.should.equal('US')\r\n\t\tphoneNumber.countryCallingCode.should.equal('1')\r\n\t\tphoneNumber.number.should.equal('+1**********')\r\n\t\tphoneNumber.nationalNumber.should.equal('**********')\r\n\r\n\t\tformatter.reset()\r\n\t\tformatter.input('******-373-4253')\r\n\r\n\t\tphoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.countryCallingCode.should.equal('1')\r\n\r\n\t\t// An incorrect NANPA international phone number.\r\n\t\t// (contains national prefix in an international phone number)\r\n\r\n\t\tformatter.reset()\r\n\t\tformatter.input('****')\r\n\r\n\t\t// Before leading digits < 3 matching was implemented:\r\n\t\t//\r\n\t\t// phoneNumber = formatter.getNumber()\r\n\t\t// expect(phoneNumber).to.not.be.undefined\r\n\t\t//\r\n\t\t// formatter.input('1')\r\n\t\t// phoneNumber = formatter.getNumber()\r\n\t\t// expect(phoneNumber.country).to.be.undefined\r\n\t\t// phoneNumber.countryCallingCode.should.equal('1')\r\n\t\t// phoneNumber.number.should.equal('+111')\r\n\r\n\t\t// After leading digits < 3 matching was implemented:\r\n\t\t//\r\n\t\tphoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber).to.be.undefined\r\n\t\t//\r\n\t\tformatter.input('1')\r\n\t\tphoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.countryCallingCode.should.equal('1')\r\n\t\tphoneNumber.number.should.equal('+11')\r\n\t})\r\n\r\n\tit('should work with countries that add digits to national (significant) number', () => {\r\n\t\t// When formatting Argentinian mobile numbers in international format,\r\n\t\t// a `9` is prepended, when compared to national format.\r\n\t\tconst asYouType = new AsYouType('AR')\r\n\t\tasYouType.input('+5493435551212').should.equal('+54 9 3435 55 1212')\r\n\t\tasYouType.reset()\r\n\t\t// Digits shouldn't be changed when formatting in national format.\r\n\t\t// (no `9` is prepended).\r\n\t\t// First parses national (significant) number by prepending `9` to it\r\n\t\t// and stripping `15` from it.\r\n\t\t// Then uses `$2 15-$3-$4` format that strips the leading `9`\r\n\t\t// and adds `15`.\r\n\t\tasYouType.input('0343515551212').should.equal('03435 15-55-1212')\r\n\t})\r\n\r\n\tit('should return non-formatted phone number when no format matches and national (significant) number has digits added', () => {\r\n\t\t// When formatting Argentinian mobile numbers in international format,\r\n\t\t// a `9` is prepended, when compared to national format.\r\n\t\tconst asYouType = new AsYouType('AR')\r\n\t\t// Digits shouldn't be changed when formatting in national format.\r\n\t\t// (no `9` is prepended).\r\n\t\t// First parses national (significant) number by prepending `9` to it\r\n\t\t// and stripping `15` from it.\r\n\t\t// Then uses `$2 15-$3-$4` format that strips the leading `9`\r\n\t\t// and adds `15`.\r\n\t\t// `this.nationalSignificantNumberMatchesInput` is `false` in this case,\r\n\t\t// so `getNonFormattedNumber()` returns `getFullNumber(getNationalDigits())`.\r\n\t\tasYouType.input('0343515551212999').should.equal('0343515551212999')\r\n\t})\r\n\r\n\tit('should format Argentina numbers (starting with 011) (digit by digit)', () => {\r\n\t\t// Inputting a number digit-by-digit and as a whole a two different cases\r\n\t\t// in case of this library compared to Google's `libphonenumber`\r\n\t\t// that always inputs a number digit-by-digit.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/23\r\n\t\t// nextDigits 0111523456789\r\n\t\t// nationalNumber 91123456789\r\n\t\tconst formatter = new AsYouType('AR')\r\n\t\tformatter.input('0').should.equal('0')\r\n\t\tformatter.getTemplate().should.equal('x')\r\n\t\tformatter.input('1').should.equal('01')\r\n\t\tformatter.getTemplate().should.equal('xx')\r\n\t\tformatter.input('1').should.equal('011')\r\n\t\tformatter.getTemplate().should.equal('xxx')\r\n\t\tformatter.input('1').should.equal('011 1')\r\n\t\tformatter.getTemplate().should.equal('xxx x')\r\n\t\tformatter.input('5').should.equal('011 15')\r\n\t\tformatter.getTemplate().should.equal('xxx xx')\r\n\t\tformatter.input('2').should.equal('011 152')\r\n\t\tformatter.getTemplate().should.equal('xxx xxx')\r\n\t\tformatter.input('3').should.equal('011 1523')\r\n\t\tformatter.getTemplate().should.equal('xxx xxxx')\r\n\t\tformatter.input('4').should.equal('011 1523-4')\r\n\t\tformatter.getTemplate().should.equal('xxx xxxx-x')\r\n\t\tformatter.input('5').should.equal('011 1523-45')\r\n\t\tformatter.getTemplate().should.equal('xxx xxxx-xx')\r\n\t\tformatter.input('6').should.equal('011 1523-456')\r\n\t\tformatter.getTemplate().should.equal('xxx xxxx-xxx')\r\n\t\tformatter.input('7').should.equal('011 1523-4567')\r\n\t\tformatter.getTemplate().should.equal('xxx xxxx-xxxx')\r\n\t\tformatter.input('8').should.equal('011152345678')\r\n\t\tformatter.getTemplate().should.equal('xxxxxxxxxxxx')\r\n\t\tformatter.input('9').should.equal('011 15-2345-6789')\r\n\t\tformatter.getTemplate().should.equal('xxx xx-xxxx-xxxx')\r\n\t\t// Private property (not public API).\r\n\t\tformatter.state.nationalSignificantNumber.should.equal('91123456789')\r\n\t\t// Private property (not public API).\r\n\t\t// `formatter.digits` is not always `formatter.nationalPrefix`\r\n\t\t// plus `formatter.nationalNumberDigits`.\r\n\t\tformatter.state.nationalPrefix.should.equal('0')\r\n\t\tformatter.isPossible().should.equal(true)\r\n\t\tformatter.isValid().should.equal(true)\r\n\t})\r\n\r\n\tit('should format Argentina numbers (starting with 011)', () => {\r\n\t\t// Inputting a number digit-by-digit and as a whole a two different cases\r\n\t\t// in case of this library compared to Google's `libphonenumber`\r\n\t\t// that always inputs a number digit-by-digit.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/23\r\n\t\t// nextDigits 0111523456789\r\n\t\t// nationalNumber 91123456789\r\n\t\tconst formatter = new AsYouType('AR')\r\n\t\tformatter.input('0111523456789').should.equal('011 15-2345-6789')\r\n\t\t// Private property (not public API).\r\n\t\tformatter.state.nationalSignificantNumber.should.equal('91123456789')\r\n\t\t// Private property (not public API).\r\n\t\t// `formatter.digits` is not always `formatter.nationalPrefix`\r\n\t\t// plus `formatter.nationalNumberDigits`.\r\n\t\texpect(formatter.state.nationalPrefix).to.equal('0')\r\n\t\t// expect(formatter.nationalPrefix).to.be.undefined\r\n\t\tformatter.isPossible().should.equal(true)\r\n\t\tformatter.isValid().should.equal(true)\r\n\t})\r\n\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/93\r\n\tit('should format Indonesian numbers', () => {\r\n\t\tconst formatter = new AsYouType('ID')\r\n\t\tformatter.getChars().should.equal('')\r\n\t\t// Before leading digits < 3 matching was implemented:\r\n\t\t// formatter.input('081').should.equal('(081)')\r\n\t\t// After leading digits < 3 matching was implemented:\r\n\t\tformatter.input('081').should.equal('081')\r\n\t})\r\n\r\n\tit('should prepend `complexPrefixBeforeNationalSignificantNumber` (not a complete number)', () => {\r\n\t\t// A country having `national_prefix_for_parsing` with a \"capturing group\".\r\n\t\t// National prefix is either not used in a format or is optional.\r\n\t\t// Input phone number without a national prefix.\r\n\t\tconst formatter = new AsYouType('AU')\r\n\t\tformatter.input('1831130345678').should.equal('1831 1303 456 78')\r\n\t\t// Private property (not public API).\r\n\t\tformatter.state.nationalSignificantNumber.should.equal('130345678')\r\n\t\t// Private property (not public API).\r\n\t\t// `formatter.digits` is not always `formatter.nationalPrefix`\r\n\t\t// plus `formatter.nationalNumberDigits`.\r\n\t\texpect(formatter.state.nationalPrefix).to.be.undefined\r\n\t\tformatter.state.complexPrefixBeforeNationalSignificantNumber.should.equal('1831')\r\n\t})\r\n\r\n\tit('should prepend `complexPrefixBeforeNationalSignificantNumber` (complete number)', () => {\r\n\t\t// A country having `national_prefix_for_parsing` with a \"capturing group\".\r\n\t\t// National prefix is either not used in a format or is optional.\r\n\t\t// Input phone number without a national prefix.\r\n\t\tconst formatter = new AsYouType('AU')\r\n\t\tformatter.input('18311303456789').should.equal('1831 1303 456 789')\r\n\t\t// Private property (not public API).\r\n\t\tformatter.state.nationalSignificantNumber.should.equal('1303456789')\r\n\t\t// Private property (not public API).\r\n\t\t// `formatter.digits` is not always `formatter.nationalPrefix`\r\n\t\t// plus `formatter.nationalNumberDigits`.\r\n\t\texpect(formatter.state.nationalPrefix).to.be.undefined\r\n\t\tformatter.state.complexPrefixBeforeNationalSignificantNumber.should.equal('1831')\r\n\t})\r\n\r\n\tit('should work with Mexico numbers', () => {\r\n\t\tconst asYouType = new AsYouType('MX')\r\n\r\n\t\t// Fixed line. International.\r\n\t\tasYouType.input('+52(449)978-000').should.equal('+52 449 978 000')\r\n\t\tasYouType.input('1').should.equal('+52 ************')\r\n\t\tasYouType.reset()\r\n\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t// // Fixed line. National. With national prefix \"01\".\r\n\t\t// asYouType.input('01449978000').should.equal('01449 978 000')\r\n\t\t// asYouType.getTemplate().should.equal('xxxxx xxx xxx')\r\n\t\t// asYouType.input('1').should.equal('01************')\r\n\t\t// asYouType.getTemplate().should.equal('xxxxx xxx xxxx')\r\n\t\t// asYouType.reset()\r\n\r\n\t\t// Fixed line. National. Without national prefix.\r\n\t\tasYouType.input('(449)978-000').should.equal('449 978 000')\r\n\t\tasYouType.getTemplate().should.equal('xxx xxx xxx')\r\n\t\tasYouType.input('1').should.equal('************')\r\n\t\tasYouType.getTemplate().should.equal('xxx xxx xxxx')\r\n\t\tasYouType.reset()\r\n\r\n\t\t// Mobile.\r\n\t\tasYouType.input('+52331234567').should.equal('+52 33 1234 567')\r\n\t\tasYouType.input('8').should.equal('+52 33 1234 5678')\r\n\t\tasYouType.reset()\r\n\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t// // Mobile.\r\n\t\t// // With `1` prepended before area code to mobile numbers in international format.\r\n\t\t// asYouType.input('+521331234567').should.equal('+52 133 1234 567')\r\n\t\t// asYouType.getTemplate().should.equal('xxx xxx xxxx xxx')\r\n\t\t// // Google's `libphonenumber` seems to not able to format this type of number.\r\n\t\t// // https://issuetracker.google.com/issues/147938979\r\n\t\t// asYouType.input('8').should.equal('+52 133 1234 5678')\r\n\t\t// asYouType.getTemplate().should.equal('xxx xxx xxxx xxxx')\r\n\t\t// asYouType.reset()\r\n\t\t//\r\n\t\t// // Mobile. National. With \"044\" prefix.\r\n\t\t// asYouType.input('044331234567').should.equal('04433 1234 567')\r\n\t\t// asYouType.input('8').should.equal('04433 1234 5678')\r\n\t\t// asYouType.reset()\r\n\t\t//\r\n\t\t// // Mobile. National. With \"045\" prefix.\r\n\t\t// asYouType.input('045331234567').should.equal('04533 1234 567')\r\n\t\t// asYouType.input('8').should.equal('04533 1234 5678')\r\n\t})\r\n\r\n\tit('should just prepend national prefix if national_prefix_formatting_rule does not produce a suitable number', () => {\r\n\t\t// \"national_prefix\": \"8\"\r\n\t\t// \"national_prefix_for_parsing\": \"0|80?\"\r\n\t\tconst formatter = new AsYouType('BY')\r\n\t\t// \"national_prefix_formatting_rule\": \"8 $1\"\r\n\t\t// That `national_prefix_formatting_rule` isn't used\r\n\t\t// because the user didn't input national prefix `8`.\r\n\t\tformatter.input('0800123').should.equal('0 800 123')\r\n\t\tformatter.getTemplate().should.equal('x xxx xxx')\r\n\t})\r\n\r\n\tit('should not duplicate area code for certain countries', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/318\r\n\t\tconst asYouType = new AsYouType('VI')\r\n\t\t// Even though `parse(\"3406934\")` would return a\r\n\t\t// \"(*************\" national number, still\r\n\t\t// \"As You Type\" formatter should leave it as \"(340) 6934\".\r\n\t\tasYouType.input('340693').should.equal('(340) 693')\r\n\t\tasYouType.input('4').should.equal('(340) 693-4')\r\n\t\tasYouType.input('123').should.equal('(*************')\r\n\t})\r\n\r\n\tit('shouldn\\'t throw when passed a non-existent default country', () => {\r\n\t\tnew AsYouType('XX').input('+78005553535').should.equal('****** 555 35 35')\r\n\t\tnew AsYouType('XX').input('88005553535').should.equal('88005553535')\r\n\t})\r\n\r\n\tit('should parse carrier codes', () => {\r\n\t\tconst formatter = new AsYouType('BR')\r\n\r\n\t\tformatter.input('0 15 21 5555-5555')\r\n\t\tlet phoneNumber = formatter.getNumber()\r\n\t\tphoneNumber.carrierCode.should.equal('15')\r\n\r\n\t\tformatter.reset()\r\n\t\tformatter.input('******-373-4253')\r\n\t\tphoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber.carrierCode).to.be.undefined\r\n\t})\r\n\r\n\tit('should format when default country calling code is configured', () => {\r\n\t\tconst formatter = new AsYouType({ defaultCallingCode: '7' })\r\n\t\tformatter.input('88005553535').should.equal('8 (800) 555-35-35')\r\n\t\tformatter.getNumber().countryCallingCode.should.equal('7')\r\n\t\tformatter.getNumber().country.should.equal('RU')\r\n\t})\r\n\r\n\tit('shouldn\\'t return PhoneNumber if country calling code hasn\\'t been input yet', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\tformatter.input('+80')\r\n\t\texpect(formatter.getNumber()).to.be.undefined\r\n\t})\r\n\r\n\tit('should format non-geographic numbering plan phone numbers', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\tformatter.input('+').should.equal('+')\r\n\t\tformatter.input('8').should.equal('+8')\r\n\t\tformatter.input('7').should.equal('+87')\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\tformatter.input('0').should.equal('+870')\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tformatter.getCountry().should.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t}\r\n\t\tformatter.input('7').should.equal('+870 7')\r\n\t\tformatter.input('7').should.equal('+870 77')\r\n\t\tformatter.input('3').should.equal('+870 773')\r\n\t\tformatter.input('1').should.equal('+870 773 1')\r\n\t\tformatter.input('1').should.equal('+870 773 11')\r\n\t\tformatter.input('1').should.equal('+870 773 111')\r\n\t\tformatter.input('6').should.equal('+870 773 111 6')\r\n\t\tformatter.input('3').should.equal('+870 773 111 63')\r\n\t\tformatter.input('2').should.equal('+870 773 111 632')\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tformatter.getNumber().country.should.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t}\r\n\t\tformatter.getNumber().countryCallingCode.should.equal('870')\r\n\t})\r\n\r\n\tit('should format non-geographic numbering plan phone numbers (default country calling code)', () => {\r\n\t\tconst formatter = new AsYouType({ defaultCallingCode: '870' })\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tformatter.getNumber().country.should.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t}\r\n\t\tformatter.input('7').should.equal('7')\r\n\t\tformatter.input('7').should.equal('77')\r\n\t\tformatter.input('3').should.equal('773')\r\n\t\tformatter.input('1').should.equal('773 1')\r\n\t\tformatter.input('1').should.equal('773 11')\r\n\t\tformatter.input('1').should.equal('773 111')\r\n\t\tformatter.input('6').should.equal('773 111 6')\r\n\t\tformatter.input('3').should.equal('773 111 63')\r\n\t\tformatter.input('2').should.equal('773 111 632')\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tformatter.getNumber().country.should.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t}\r\n\t\tformatter.getNumber().countryCallingCode.should.equal('870')\r\n\t})\r\n\r\n\tit('should not format non-geographic numbering plan phone numbers (default country 001)', () => {\r\n\t\tconst formatter = new AsYouType('001')\r\n\t\texpect(formatter.defaultCountry).to.be.undefined\r\n\t\texpect(formatter.defaultCallingCode).to.be.undefined\r\n\t\tformatter.input('7').should.equal('7')\r\n\t\tformatter.input('7').should.equal('77')\r\n\t\tformatter.input('3').should.equal('773')\r\n\t\tformatter.input('1').should.equal('7731')\r\n\t\tformatter.input('1').should.equal('77311')\r\n\t\tformatter.input('1').should.equal('773111')\r\n\t\tformatter.input('6').should.equal('7731116')\r\n\t\tformatter.input('3').should.equal('77311163')\r\n\t\tformatter.input('2').should.equal('773111632')\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\texpect(formatter.getNumber()).to.be.undefined\r\n\t})\r\n\r\n\tit('should return PhoneNumber (should strip national prefix `1` in E.164 value)', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tformatter.input('+1111')\r\n\t\tformatter.getNumber().number.should.equal('+111')\r\n\t})\r\n\r\n\tit('should return PhoneNumber with autocorrected international numbers without leading +', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/316\r\n\t\tconst formatter = new AsYouType('FR')\r\n\t\tformatter.input('33612902554').should.equal('33 6 12 90 25 54')\r\n\t\tformatter.getNumber().country.should.equal('FR')\r\n\t\tformatter.getNumber().nationalNumber.should.equal('612902554')\r\n\t\tformatter.getNumber().number.should.equal('+33612902554')\r\n\t\t// Should also strip national prefix.\r\n\t\tformatter.reset()\r\n\t\tformatter.input('330612902554').should.equal('33 06 12 90 25 54')\r\n\t\tformatter.getNumber().country.should.equal('FR')\r\n\t\tformatter.getNumber().nationalNumber.should.equal('612902554')\r\n\t\tformatter.getNumber().number.should.equal('+33612902554')\r\n\t\t// On second thought, this \"prepend default area code\" feature won't be added,\r\n\t\t// because when a user selects \"British Virgin Islands\" and inputs\r\n\t\t// \"2291234\", then they see \"(229) 123-4\" which clearly indicates that\r\n\t\t// they should input the complete phone number (with area code).\r\n\t\t// So, unless a user completely doesn't understand what they're doing,\r\n\t\t// they'd input the complete phone number (with area code).\r\n\t\t// // Should prepend the default area code in British Virgin Islands.\r\n\t\t// // https://github.com/catamphetamine/react-phone-number-input/issues/335\r\n\t\t// const formatter2 = new AsYouType('VG')\r\n\t\t// formatter2.input('2291234').should.equal('(229) 123-4')\r\n\t\t// formatter2.getNumber().country.should.equal('VG')\r\n\t\t// formatter2.getNumber().nationalNumber.should.equal('2842291234')\r\n\t\t// formatter2.getNumber().number.should.equal('+12842291234')\r\n\t})\r\n\r\n\tit('should work with out-of-country dialing prefix (like 00)', () => {\r\n\t\tconst formatter = new AsYouType('DE')\r\n\t\tformatter.input('00498911196611').should.equal('00 49 89 11196611')\r\n\t\tformatter.getCountry().should.equal('DE')\r\n\t\tformatter.formatter.template.should.equal('xx xx xx xxxxxxxx')\r\n\t\tformatter.formatter.populatedNationalNumberTemplate.should.equal('89 11196611')\r\n\t\tformatter.getTemplate().should.equal('xx xx xx xxxxxxxx')\r\n\t\tformatter.getNumber().country.should.equal('DE')\r\n\t\tformatter.getNumber().nationalNumber.should.equal('8911196611')\r\n\t\tformatter.getNumber().number.should.equal('+498911196611')\r\n\t})\r\n\r\n\tit('shouldn\\'t choose a format when there\\'re too many digits for any of them', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tformatter.input('89991112233')\r\n\t\tformatter.formatter.chosenFormat.format().should.equal('$1 $2-$3-$4')\r\n\t\tformatter.reset()\r\n\t\tformatter.input('899911122334')\r\n\t\texpect(formatter.formatter.chosenFormat).to.be.undefined\r\n\t})\r\n\r\n\tit('should get separator after national prefix', () => {\r\n\t\t// Russia.\r\n\t\t// Has separator after national prefix.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tconst format = formatter.metadata.formats()[0]\r\n\t\tformat.nationalPrefixFormattingRule().should.equal('8 ($1)')\r\n\t\tformatter.formatter.getSeparatorAfterNationalPrefix(format).should.equal(' ')\r\n\t\t// Britain.\r\n\t\t// Has no separator after national prefix.\r\n\t\tconst formatter2 = new AsYouType('GB')\r\n\t\tconst format2 = formatter2.metadata.formats()[0]\r\n\t\tformat2.nationalPrefixFormattingRule().should.equal('0$1')\r\n\t\tformatter2.formatter.getSeparatorAfterNationalPrefix(format2).should.equal('')\r\n\t})\r\n\r\n\tit('should return if the number is possible', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tformatter.isPossible().should.equal(false)\r\n\t\tformatter.input('8')\r\n\t\tformatter.isPossible().should.equal(false)\r\n\t\tformatter.input('8005553535')\r\n\t\tformatter.isPossible().should.equal(true)\r\n\t\tformatter.input('5')\r\n\t\tformatter.isPossible().should.equal(false)\r\n\t})\r\n\r\n\tit('should return if the number is valid', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tformatter.isValid().should.equal(false)\r\n\t\tformatter.input('88005553535')\r\n\t\tformatter.isValid().should.equal(true)\r\n\t\tformatter.input('5')\r\n\t\tformatter.isValid().should.equal(false)\r\n\t})\r\n\r\n\tit('should return if the number is international', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tformatter.isInternational().should.equal(false)\r\n\t\tformatter.input('88005553535')\r\n\t\tformatter.isInternational().should.equal(false)\r\n\t\t// International. Russia.\r\n\t\tconst formatterInt = new AsYouType()\r\n\t\tformatterInt.isInternational().should.equal(false)\r\n\t\tformatterInt.input('+')\r\n\t\tformatterInt.isInternational().should.equal(true)\r\n\t\tformatterInt.input('78005553535')\r\n\t\tformatterInt.isInternational().should.equal(true)\r\n\t})\r\n\r\n\tit('should return country calling code part of the number', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.getCountryCallingCode()).to.be.undefined\r\n\t\tformatter.input('88005553535')\r\n\t\texpect(formatter.getCountryCallingCode()).to.be.undefined\r\n\t\t// International. Russia.\r\n\t\tconst formatterInt = new AsYouType()\r\n\t\texpect(formatterInt.getCountryCallingCode()).to.be.undefined\r\n\t\tformatterInt.input('+')\r\n\t\texpect(formatterInt.getCountryCallingCode()).to.be.undefined\r\n\t\tformatterInt.input('7')\r\n\t\texpect(formatterInt.getCountryCallingCode()).to.equal('7')\r\n\t\tformatterInt.input('8005553535')\r\n\t\texpect(formatterInt.getCountryCallingCode()).to.equal('7')\r\n\t})\r\n\r\n\tit('should return the country of the number', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\tformatter.input('8')\r\n\t\texpect(formatter.getCountry()).to.equal('RU')\r\n\t\tformatter.input('8005553535')\r\n\t\texpect(formatter.getCountry()).to.equal('RU')\r\n\t\t// International. Austria.\r\n\t\tconst formatterInt = new AsYouType()\r\n\t\texpect(formatterInt.getCountry()).to.be.undefined\r\n\t\tformatterInt.input('+')\r\n\t\texpect(formatterInt.getCountry()).to.be.undefined\r\n\t\tformatterInt.input('43')\r\n\t\texpect(formatterInt.getCountry()).to.equal('AT')\r\n\t\t// International. USA.\r\n\t\tconst formatterIntRu = new AsYouType()\r\n\t\texpect(formatterIntRu.getCountry()).to.be.undefined\r\n\t\tformatterIntRu.input('+')\r\n\t\texpect(formatterIntRu.getCountry()).to.be.undefined\r\n\t\tformatterIntRu.input('1')\r\n\t\texpect(formatterIntRu.getCountry()).to.be.undefined\r\n\t\tformatterIntRu.input('**********')\r\n\t\texpect(formatterIntRu.getCountry()).to.equal('US')\r\n\t\tformatterIntRu.input('1')\r\n\t\texpect(formatterIntRu.getCountry()).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse a long IDD prefix', () => {\r\n\t\tconst formatter = new AsYouType('AU')\r\n\t\t// `14880011` is a long IDD prefix in Australia.\r\n\t\tformatter.input('1').should.equal('1')\r\n\t\tformatter.input('4').should.equal('14')\r\n\t\tformatter.input('8').should.equal('148')\r\n\t\tformatter.input('8').should.equal('1488')\r\n\t\tformatter.input('0').should.equal('14880')\r\n\t\tformatter.input('0').should.equal('148800')\r\n\t\tformatter.input('1').should.equal('1488001')\r\n\t\tformatter.input('1').should.equal('14880011')\r\n\t\t// As if were calling US using `14880011` IDD prefix,\r\n\t\t// though that prefix could mean something else.\r\n\t\tformatter.input('1').should.equal('14880011 1')\r\n\t\tformatter.input('2').should.equal('14880011 1 2')\r\n\t\tformatter.input('1').should.equal('14880011 1 21')\r\n\t\tformatter.input('3').should.equal('14880011 1 213')\r\n\t})\r\n\r\n\tit('should return the phone number characters entered by the user', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tformatter.getChars().should.equal('')\r\n\t\tformatter.input('+123')\r\n\t\tformatter.getChars().should.equal('+123')\r\n\t\tformatter.reset()\r\n\t\tformatter.input('123')\r\n\t\tformatter.getChars().should.equal('123')\r\n\t})\r\n\r\n\t// A test confirming the case when input `\"11\"` for country `\"US\"`\r\n\t// produces `value` `\"+11\"`.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/113\r\n\tit('should determine the national (significant) part correctly when input with national prefix in US', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\t// As soon as the user has input `\"11\"`, no `format` matches\r\n\t\t// those \"national number\" digits in the `\"US\"` country metadata.\r\n\t\t// Since no `format` matches, the number doesn't seem like a valid one,\r\n\t\t// so it attempts to see if the user \"forgot\" to input a `\"+\"` at the start.\r\n\t\t// And it looks like they might've to.\r\n\t\t// So it acts as if the leading `\"+\"` is there,\r\n\t\t// as if the user's input is `\"+11\"`.\r\n\t\t// See `AsYouType.fixMissingPlus()` function.\r\n\t\tformatter.input('************** 3').should.equal('1 **************')\r\n\t\tformatter.getNumber().nationalNumber.should.equal('2222222223')\r\n\t})\r\n})\r\n\r\ndescribe('AsYouType.getNumberValue()', () => {\r\n\tit('should return E.164 number value (national number, with national prefix, default country: US)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (national number, with national prefix, default calling code: 1)', () => {\r\n\t\tconst formatter = new AsYouType({ defaultCallingCode: '1' })\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (national number, default country: US)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (national number, default calling code: 1)', () => {\r\n\t\tconst formatter = new AsYouType({ defaultCallingCode: '1' })\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, not a valid calling code)', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('2150')\r\n\t\tformatter.getNumberValue().should.equal('+2150')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, default country: US)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, other default country: RU)', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, default calling code: 1)', () => {\r\n\t\tconst formatter = new AsYouType('US', { defaultCallingCode: '1' })\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, other default calling code: 7)', () => {\r\n\t\tconst formatter = new AsYouType('US', { defaultCallingCode: '7' })\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number)', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\tformatter.getNumberValue().should.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\tformatter.getNumberValue().should.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\tformatter.getNumberValue().should.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\tformatter.getNumberValue().should.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\tformatter.getNumberValue().should.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (national number) (no default country or calling code)', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('12')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t})\r\n\r\n\tit('should not drop any input digits', () => {\r\n\t\t// Test \"+529011234567\" number, proactively ensuring that no formatting is applied,\r\n\t\t// where a format is chosen that would otherwise have led to some digits being dropped.\r\n\t\tconst formatter = new AsYouType('MX')\r\n\t\tformatter.input('9').should.equal('9')\r\n\t\tformatter.input('0').should.equal('90')\r\n\t\tformatter.input('1').should.equal('901')\r\n\t\tformatter.input('1').should.equal('901 1')\r\n\t\tformatter.input('2').should.equal('901 12')\r\n\t\tformatter.input('3').should.equal('901 123')\r\n\t\tformatter.input('4').should.equal('901 123 4')\r\n\t\tformatter.input('5').should.equal('901 123 45')\r\n\t\tformatter.input('6').should.equal('901 123 456')\r\n\t\tformatter.input('7').should.equal('************')\r\n\t})\r\n\r\n\tit('should work for formats with no leading digits (`leadingDigitsPatternsCount === 0`)', function() {\r\n\t\tconst formatter = new AsYouType({\r\n\t\t\tdefaultCallingCode: 888\r\n\t\t})\r\n\t\tformatter.input('1').should.equal('1')\r\n\t})\r\n\r\n\tit('should work for SK phone numbers', function() {\r\n\t\t// There was a bug: \"leading digits\" `\"2\"` matched \"leading digits pattern\" `\"90\"`.\r\n\t\t// The incorrect `.match()` function result was `{ oveflow: true }`\r\n\t\t// while it should've been `undefined`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/66\r\n\t\tconst formatter = new AsYouType('SK')\r\n\t\tformatter.input('090').should.equal('090')\r\n\t\tformatter.reset()\r\n\t\tformatter.input('080').should.equal('080')\r\n\t\tformatter.reset()\r\n\t\tformatter.input('059').should.equal('059')\r\n\t})\r\n\r\n\tit('should work for SK phone numbers (2)', function() {\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/69\r\n\t\tconst formatter = new AsYouType('SK')\r\n\t\tformatter.input('421901222333').should.equal('421 901 222 333')\r\n\t\tformatter.getTemplate().should.equal('xxx xxx xxx xxx')\r\n\t})\r\n\r\n\tit('should not choose `defaultCountry` over the \"main\" one when both the `defaultCountry` and the \"main\" one match the phone number', function() {\r\n\t\t// This phone number matches both US and CA because they have the same\r\n\t\t// regular expression for some weird reason.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/103\r\n\t\tconst formatter = new AsYouType('CA')\r\n\t\tformatter.input('8004001000')\r\n\t\tformatter.getNumber().country.should.not.equal('CA')\r\n\t\tformatter.getNumber().country.should.equal('US')\r\n\r\n\t\t// This phone number is specific to CA.\r\n\t\tconst formatter2 = new AsYouType('US')\r\n\t\tformatter2.input('4389999999')\r\n\t\tformatter2.getNumber().country.should.equal('CA')\r\n\r\n\t\t// This phone number doesn't belong neither to CA nor to US.\r\n\t\t// In fact, it doesn't belong to any country from the \"NANPA\" zone.\r\n\t\tconst formatter3 = new AsYouType('US')\r\n\t\tformatter3.input('1111111111')\r\n\t\tformatter3.getNumber().country.should.equal('US')\r\n\t})\r\n})\r\n\r\nfunction type(something) {\r\n\treturn typeof something\r\n}"], "mappings": ";;AAAA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;IAEMA,S;;;;;EACL,mBAAYC,YAAZ,EAA0B;IAAA;;IAAA,yBACnBA,YADmB,EACLC,uBADK;EAEzB;;;EAHsBC,qB;;AAMxB,IAAMC,+BAA+B,GAAG,KAAxC;AAEAC,QAAQ,CAAC,WAAD,EAAc,YAAM;EAC3BC,EAAE,CAAC,8CAAD,EAAiD,YAAM;IACxD;IACA,IAAIN,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,aAA1B,EAAyCC,MAAzC,CAAgDC,KAAhD,CAAsD,mBAAtD,EAFwD,CAGxD;;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,WAA1B,EAAuCC,MAAvC,CAA8CC,KAA9C,CAAoD,gBAApD;EACA,CALC,CAAF;EAOAH,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAFqE,CAGrE;;IACAG,SAAS,CAACA,SAAV,CAAoBC,QAApB,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,YAA1C,EAJqE,CAKrE;;IACAC,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,YAAjE;IACAC,SAAS,CAACH,KAAV,CAAgB,KAAhB;IACAG,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,kBAAjE;IACAC,SAAS,CAACH,KAAV,CAAgB,SAAhB;IACAG,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,kBAAjE;EACA,CAXC,CAAF;EAaAH,EAAE,CAAC,kFAAD,EAAqF,YAAM;IAC5F,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAa,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAH,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBE,+BAArB,CAAN,CAA4DE,EAA5D,CAA+DC,EAA/D,CAAkEC,SAAlE;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB,EAAoBC,MAApB,CAA2BC,KAA3B,CAAiC,EAAjC;IACAI,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAH,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBE,+BAArB,CAAN,CAA4DE,EAA5D,CAA+DC,EAA/D,CAAkEC,SAAlE;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,GAArC;IACAI,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAH,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBE,+BAArB,CAAN,CAA4DE,EAA5D,CAA+DC,EAA/D,CAAkEC,SAAlE;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC,EAZ4F,CAa5F;IACA;;IACAC,SAAS,CAACA,SAAV,CAAoBC,QAApB,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,aAA1C,EAf4F,CAgB5F;;IACAC,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,UAAjE,EAjB4F,CAkB5F;;IACAC,SAAS,CAACH,KAAV,CAAgB,KAAhB;IACAG,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,cAAjE;IACAC,SAAS,CAACH,KAAV,CAAgB,SAAhB;IACAG,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,cAAjE;EACA,CAvBC,CAAF;EAyBAH,EAAE,CAAC,gEAAD,EAAmE,YAAM;IAC1E,IAAMI,SAAS,GAAG,IAAIV,SAAJ,EAAlB;IACAa,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAH,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBE,+BAArB,CAAN,CAA4DE,EAA5D,CAA+DC,EAA/D,CAAkEC,SAAlE;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB,EAAoBC,MAApB,CAA2BC,KAA3B,CAAiC,EAAjC;IACAI,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAH,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBE,+BAArB,CAAN,CAA4DE,EAA5D,CAA+DC,EAA/D,CAAkEC,SAAlE;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAI,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAH,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBE,+BAArB,CAAN,CAA4DE,EAA5D,CAA+DC,EAA/D,CAAkEC,SAAlE;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC,EAV0E,CAW1E;;IACAC,SAAS,CAACA,SAAV,CAAoBC,QAApB,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,aAA1C,EAZ0E,CAa1E;IACA;;IACAC,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,UAAjE,EAf0E,CAgB1E;;IACAC,SAAS,CAACH,KAAV,CAAgB,KAAhB;IACAG,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,cAAjE;IACAC,SAAS,CAACH,KAAV,CAAgB,SAAhB;IACAG,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,cAAjE;EACA,CArBC,CAAF;EAuBAH,EAAE,CAAC,8EAAD,EAAiF,YAAM;IACxF,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,aAAhB,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,kBAA5C;IACAC,SAAS,CAACA,SAAV,CAAoBC,QAApB,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,kBAA1C;IACAC,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,kBAAjE;EACA,CALC,CAAF;EAOAH,EAAE,CAAC,mDAAD,EAAsD,YAAM;IAC7D;IACA,IAAIN,SAAJ,GAAgBO,KAAhB,CAAsB,WAAtB,EAAmCC,MAAnC,CAA0CC,KAA1C,CAAgD,cAAhD,EAF6D,CAG7D;;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,SAA1B,EAAqCC,MAArC,CAA4CC,KAA5C,CAAkD,aAAlD,EAJ6D,CAM7D;;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,UAA1B,EAAsCC,MAAtC,CAA6CC,KAA7C,CAAmD,eAAnD,EAP6D,CAS7D;;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,aAA1B,EAAyCC,MAAzC,CAAgDC,KAAhD,CAAsD,kBAAtD;IAEA,IAAIC,SAAJ,CAZ6D,CAc7D;IACA;IAEA;;IACA,IAAIV,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,aAA1B,EAAyCC,MAAzC,CAAgDC,KAAhD,CAAsD,iBAAtD;IAEA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,WAA1B,EAAuCC,MAAvC,CAA8CC,KAA9C,CAAoD,WAApD,EApB6D,CAsB7D;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,EAAZ,CAxB6D,CA0B7D;;IACAkB,IAAI,CAACR,SAAS,CAACS,UAAV,EAAD,CAAJ,CAA6BX,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IACAS,IAAI,CAACR,SAAS,CAACU,qBAAV,EAAD,CAAJ,CAAwCZ,MAAxC,CAA+CC,KAA/C,CAAqD,WAArD;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,EAArC;IAEAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC,EA/B6D,CAiC7D;;IACAS,IAAI,CAACR,SAAS,CAACS,UAAV,EAAD,CAAJ,CAA6BX,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IACAS,IAAI,CAACR,SAAS,CAACU,qBAAV,EAAD,CAAJ,CAAwCZ,MAAxC,CAA+CC,KAA/C,CAAqD,WAArD;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,GAArC;IAEAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC,EAtC6D,CAwC7D;;IACAS,IAAI,CAACR,SAAS,CAACS,UAAV,EAAD,CAAJ,CAA6BX,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IACAC,SAAS,CAACU,qBAAV,GAAkCZ,MAAlC,CAAyCC,KAAzC,CAA+C,GAA/C;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IAEAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,MAArC,EA9C6D,CAgD7D;;IACAS,IAAI,CAACR,SAAS,CAACS,UAAV,EAAD,CAAJ,CAA6BX,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IAEAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,OAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,WAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,YAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,eAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,gBAAlC,EA3D6D,CA6D7D;;IACAS,IAAI,CAACR,SAAS,CAACS,UAAV,EAAD,CAAJ,CAA6BX,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IAEAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,iBAAlC,EAhE6D,CAkE7D;;IACAC,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC,EAnE6D,CAoE7D;IACA;;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,iBAArC;IAEAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,gBAAlC,EAxE6D,CA0E7D;;IACAI,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC;IACAN,SAAS,CAACU,qBAAV,GAAkCZ,MAAlC,CAAyCC,KAAzC,CAA+C,GAA/C;IACAI,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C,CA7E6D,CA+E7D;IACA;;IAEAN,SAAS,CAACW,KAAV;IAEAX,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,kBAA7C,EAvF6D,CAyF7D;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,EAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,OAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,WAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,YAAlC,EAzG6D,CA2G7D;;IACAC,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC;IACAC,SAAS,CAACA,SAAV,CAAoBC,QAApB,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,eAA1C;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC;IAEAC,SAAS,CAACH,KAAV,CAAgB,KAAhB,EAAuBC,MAAvB,CAA8BC,KAA9B,CAAoC,eAApC,EAhH6D,CAkH7D;;IACAC,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,eAArC;IAEAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,aAAlC,EAtH6D,CAwH7D;;IACAC,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC;IACAI,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C,CA1H6D,CA4H7D;;IAEAN,SAAS,GAAG,IAAIV,SAAJ,EAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,cAAhB;IACAG,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC,EAjI6D,CAmI7D;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IACAU,SAAS,CAACH,KAAV,CAAgB,aAAhB,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,iBAA5C,EAtI6D,CAwI7D;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,EAAZ;IACAU,SAAS,CAACH,KAAV,CAAgB,eAAhB,EAAiCC,MAAjC,CAAwCC,KAAxC,CAA8C,iBAA9C;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,iBAArC;IACAC,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC,EA7I6D,CA+I7D;;IACAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IACAU,SAAS,CAACH,KAAV,CAAgB,aAAhB,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,mBAA5C;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,mBAArC,EAlJ6D,CAoJ7D;IACA;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,MAAhB,EAAwBC,MAAxB,CAA+BC,KAA/B,CAAqC,eAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,KAAhB,EAAuBC,MAAvB,CAA8BC,KAA9B,CAAoC,mBAApC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC,EAhK6D,CAkK7D;IACA;;IACAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ,CApK6D,CAqK7D;;IACAU,SAAS,CAACH,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,kBAA7C;IACAC,SAAS,CAACY,iBAAV,GAA8Bd,MAA9B,CAAqCC,KAArC,CAA2C,YAA3C,EAvK6D,CAyK7D;IACA;IACA;;IAEAC,SAAS,CAACW,KAAV;IAEAX,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,MAAhB,EAAwBC,MAAxB,CAA+BC,KAA/B,CAAqC,eAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,KAAhB,EAAuBC,MAAvB,CAA8BC,KAA9B,CAAoC,mBAApC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC,EAtL6D,CAwL7D;IACA;;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,GAA1B,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,IAA1B,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,KAA7C;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,KAA1B,EAAiCC,MAAjC,CAAwCC,KAAxC,CAA8C,MAA9C,EA5L6D,CA8L7D;IACA;;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,WAA1B,EAAuCC,MAAvC,CAA8CC,KAA9C,CAAoD,SAApD,EAhM6D,CAkM7D;;IACA,IAAIT,SAAJ,GAAgBO,KAAhB,CAAsB,gBAAtB,EAAwCC,MAAxC,CAA+CC,KAA/C,CAAqD,kBAArD,EAnM6D,CAqM7D;;IACAC,SAAS,GAAG,IAAIV,SAAJ,EAAZ;IACAU,SAAS,CAACH,KAAV,CAAgB,iBAAhB;IACAG,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC,EAxM6D,CAyM7D;IAEA;;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,MAA1B,EAAkCC,MAAlC,CAAyCC,KAAzC,CAA+C,OAA/C,EA5M6D,CA8M7D;;IACAC,SAAS,GAAG,IAAIV,SAAJ,EAAZ;IACAU,SAAS,CAACH,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,iBAA7C;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,iBAArC;EACA,CAlNC,CAAF;EAoNAH,EAAE,CAAC,gGAAD,EAAmG,YAAM;IAC1G;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB,CAF0G,CAI1G;IACA;;IACAU,SAAS,CAACH,KAAV,CAAgB,UAAhB,EAA4BC,MAA5B,CAAmCC,KAAnC,CAAyC,UAAzC;IACAI,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C,CAP0G,CAS1G;;IACAN,SAAS,CAACW,KAAV,GAAkBd,KAAlB,CAAwB,WAAxB,EAAqCC,MAArC,CAA4CC,KAA5C,CAAkD,aAAlD;IACAC,SAAS,CAACA,SAAV,CAAoBC,QAApB,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,cAA1C;EACA,CAZC,CAAF;EAcAH,EAAE,CAAC,yFAAD,EAA4F,YAAM;IACnG;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB,CAHmG,CAInG;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACAU,SAAS,CAACH,KAAV,CAAgB,YAAhB,EAA8BC,MAA9B,CAAqCC,KAArC,CAA2C,YAA3C;IACAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,kBAA7C;EACA,CAtBC,CAAF;EAwBAH,EAAE,CAAC,uIAAD,EAA0I,YAAM;IACjJ,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,WAAhB,EAA6BC,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IACAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,YAAhB,EAA8BC,MAA9B,CAAqCC,KAArC,CAA2C,gBAA3C;EACA,CALC,CAAF;EAOAH,EAAE,CAAC,2IAAD,EAA8I,YAAM;IACrJ,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB,CADqJ,CAErJ;;IACAU,SAAS,CAACH,KAAV,CAAgB,YAAhB,EAA8BC,MAA9B,CAAqCC,KAArC,CAA2C,eAA3C;IACAC,SAAS,CAACW,KAAV,GAJqJ,CAKrJ;;IACAX,SAAS,CAACH,KAAV,CAAgB,aAAhB,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,mBAA5C;EACA,CAPC,CAAF;EASAH,EAAE,CAAC,8FAAD,EAAiG,YAAM;IACxG;IACA;IACA;IACA,IAAIN,SAAJ,GAAgBO,KAAhB,CAAsB,cAAtB,EAAsCC,MAAtC,CAA6CC,KAA7C,CAAmD,iBAAnD;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,cAA1B,EAA0CC,MAA1C,CAAiDC,KAAjD,CAAuD,iBAAvD;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,WAA1B,EAAuCC,MAAvC,CAA8CC,KAA9C,CAAoD,eAApD;EACA,CAPC,CAAF;EASAH,EAAE,CAAC,yFAAD,EAA4F,YAAM;IACnG,IAAII,SAAJ;IAEAA,SAAS,GAAG,IAAIV,SAAJ,EAAZ;IACAU,SAAS,CAACH,KAAV,CAAgB,mBAAhB,EAAqCC,MAArC,CAA4CC,KAA5C,CAAkD,mBAAlD,EAJmG,CAKnG;;IACAC,SAAS,CAACY,iBAAV,GAA8Bd,MAA9B,CAAqCC,KAArC,CAA2C,YAA3C,EANmG,CAQnG;IACA;IACA;IACA;IACA;IACA;EACA,CAdC,CAAF;EAgBAH,EAAE,CAAC,oDAAD,EAAuD,YAAM;IAC9D,IAAMiB,SAAS,GAAG,IAAIvB,SAAJ,CAAc,IAAd,CAAlB;IAEAuB,SAAS,CAAChB,KAAV,CAAgB,EAAhB,EAAoBC,MAApB,CAA2BC,KAA3B,CAAiC,EAAjC;IACAc,SAAS,CAACN,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,EAArC;IAEAc,SAAS,CAAChB,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC,EAN8D,CAO9D;IACA;;IACAc,SAAS,CAACN,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,GAArC;IAEAc,SAAS,CAAChB,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAc,SAAS,CAACN,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IAEAc,SAAS,CAAChB,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,OAAlC;IACAc,SAAS,CAACN,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,OAArC;EACA,CAhBC,CAAF;EAkBAH,EAAE,4CAA4C,YAAM;IACnD,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IAEAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC,EANmD,CAQnD;;IACAC,SAAS,CAACA,SAAV,CAAoBC,QAApB,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,mBAA1C;IACAC,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC,EAVmD,CAWnD;;IAEAC,SAAS,CAACH,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,kBAA7C,EAbmD,CAenD;;IACAI,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAN,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC,EAjBmD,CAkBnD;;IAEAC,SAAS,CAACW,KAAV,GApBmD,CAsBnD;;IACAR,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoBC,QAArB,CAAN,CAAqCG,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAH,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC,CAxBmD,CAyBnD;;IAEAN,SAAS,CAACH,KAAV,CAAgB,iBAAhB,EAAmCC,MAAnC,CAA0CC,KAA1C,CAAgD,iBAAhD,EA3BmD,CA6BnD;;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,iBAArC;IACAC,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC;IACAC,SAAS,CAACU,qBAAV,GAAkCZ,MAAlC,CAAyCC,KAAzC,CAA+C,GAA/C;EACA,CAjCC,CAAF;EAmCAH,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAII,SAAJ;IACA,IAAIc,OAAJ,CAFqC,CAIrC;;IACAA,OAAO,GAAG;MAAA,OAAM,IAAIrB,qBAAJ,CAAe,IAAf,CAAN;IAAA,CAAV;;IACAqB,OAAO,CAAChB,MAAR,UAAqB,gCAArB,EANqC,CAQrC;;IAEAE,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC,EAdqC,CAgBrC;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC,EArBqC,CAuBrC;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,aAAhB,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,mBAA5C;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC,EA5BqC,CA8BrC;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,EAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,OAAhB,EAAyBC,MAAzB,CAAgCC,KAAhC,CAAsC,OAAtC,EAlCqC,CAoCrC;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,EAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,aAAhB,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,aAA5C,EAxCqC,CA0CrC;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,QAAhB,EAA0BC,MAA1B,CAAiCC,KAAjC,CAAuC,SAAvC,EA9CqC,CAgDrC;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,QAAhB,EAA0BC,MAA1B,CAAiCC,KAAjC,CAAuC,IAAvC,EApDqC,CAsDrC;IACA;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAW,IAAI,CAACR,SAAS,CAACS,UAAV,EAAD,CAAJ,CAA6BX,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IACAS,IAAI,CAACR,SAAS,CAACU,qBAAV,EAAD,CAAJ,CAAwCZ,MAAxC,CAA+CC,KAA/C,CAAqD,WAArD,EA7DqC,CA+DrC;IACA;;IAEAC,SAAS,GAAG,IAAIV,SAAJ,EAAZ;IAEAU,SAAS,CAACH,KAAV,CAAgB,cAAhB;IACAW,IAAI,CAACR,SAAS,CAACS,UAAV,EAAD,CAAJ,CAA6BX,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IACAC,SAAS,CAACU,qBAAV,GAAkCZ,MAAlC,CAAyCC,KAAzC,CAA+C,GAA/C,EAtEqC,CAwErC;IACA;;IACAC,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAZ;IACAU,SAAS,CAACH,KAAV,CAAgB,MAAhB,EAAwBC,MAAxB,CAA+BC,KAA/B,CAAqC,MAArC,EA3EqC,CA4ErC;IACA;;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;EACA,CA/EC,CAAF;EAiFAH,EAAE,CAAC,8FAAD,EAAiG,YAAM;IACxG;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB,CA5BwG,CA6BxG;IACA;;IACAU,SAAS,CAACH,KAAV,CAAgB,UAAhB,EAA4BC,MAA5B,CAAmCC,KAAnC,CAAyC,WAAzC;IACAC,SAAS,CAACW,KAAV,GAhCwG,CAiCxG;IACA;IACA;IACA;IACA;IACA;;IACAX,SAAS,CAACH,KAAV,CAAgB,SAAhB,EAA2BC,MAA3B,CAAkCC,KAAlC,CAAwC,WAAxC;EACA,CAxCC,CAAF;EA0CAH,EAAE,CAAC,2CAAD,EAA8C,YAAM;IACrD,IAAIN,SAAJ,GAAgBO,KAAhB,CAAsB,0BAAtB,EAAkDC,MAAlD,CAAyDC,KAAzD,CAA+D,iBAA/D;EACA,CAFC,CAAF;EAIAH,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5C,IAAIN,SAAJ,GAAgBO,KAAhB,CAAsB,cAAtB,EAAsCC,MAAtC,CAA6CC,KAA7C,CAAmD,iBAAnD;EACA,CAFC,CAAF;EAIAH,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB,CADgD,CAGhD;;IACAa,MAAM,CAACH,SAAS,CAACe,SAAV,EAAD,CAAN,CAA8BX,EAA9B,CAAiCC,EAAjC,CAAoCC,SAApC;IAEAN,SAAS,CAACH,KAAV,CAAgB,IAAhB,EANgD,CAOhD;;IACAM,MAAM,CAACH,SAAS,CAACe,SAAV,EAAD,CAAN,CAA8BX,EAA9B,CAAiCC,EAAjC,CAAoCC,SAApC;IAEAN,SAAS,CAACH,KAAV,CAAgB,cAAhB;IAEA,IAAImB,WAAW,GAAGhB,SAAS,CAACe,SAAV,EAAlB;IACAC,WAAW,CAACC,OAAZ,CAAoBnB,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;IACAiB,WAAW,CAACE,kBAAZ,CAA+BpB,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAiB,WAAW,CAACG,MAAZ,CAAmBrB,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;IACAiB,WAAW,CAACI,cAAZ,CAA2BtB,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IAEAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,iBAAhB;IAEAmB,WAAW,GAAGhB,SAAS,CAACe,SAAV,EAAd;IACAZ,MAAM,CAACa,WAAW,CAACC,OAAb,CAAN,CAA4Bb,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAU,WAAW,CAACE,kBAAZ,CAA+BpB,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C,EAvBgD,CAyBhD;IACA;;IAEAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,MAAhB,EA7BgD,CA+BhD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;;IACAmB,WAAW,GAAGhB,SAAS,CAACe,SAAV,EAAd;IACAZ,MAAM,CAACa,WAAD,CAAN,CAAoBZ,EAApB,CAAuBC,EAAvB,CAA0BC,SAA1B,CA7CgD,CA8ChD;;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAmB,WAAW,GAAGhB,SAAS,CAACe,SAAV,EAAd;IACAZ,MAAM,CAACa,WAAW,CAACC,OAAb,CAAN,CAA4Bb,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAU,WAAW,CAACE,kBAAZ,CAA+BpB,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAiB,WAAW,CAACG,MAAZ,CAAmBrB,MAAnB,CAA0BC,KAA1B,CAAgC,KAAhC;EACA,CApDC,CAAF;EAsDAH,EAAE,CAAC,6EAAD,EAAgF,YAAM;IACvF;IACA;IACA,IAAMiB,SAAS,GAAG,IAAIvB,SAAJ,CAAc,IAAd,CAAlB;IACAuB,SAAS,CAAChB,KAAV,CAAgB,gBAAhB,EAAkCC,MAAlC,CAAyCC,KAAzC,CAA+C,oBAA/C;IACAc,SAAS,CAACF,KAAV,GALuF,CAMvF;IACA;IACA;IACA;IACA;IACA;;IACAE,SAAS,CAAChB,KAAV,CAAgB,eAAhB,EAAiCC,MAAjC,CAAwCC,KAAxC,CAA8C,kBAA9C;EACA,CAbC,CAAF;EAeAH,EAAE,CAAC,oHAAD,EAAuH,YAAM;IAC9H;IACA;IACA,IAAMiB,SAAS,GAAG,IAAIvB,SAAJ,CAAc,IAAd,CAAlB,CAH8H,CAI9H;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACAuB,SAAS,CAAChB,KAAV,CAAgB,kBAAhB,EAAoCC,MAApC,CAA2CC,KAA3C,CAAiD,kBAAjD;EACA,CAbC,CAAF;EAeAH,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF;IACA;IACA;IACA;IACA;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,GAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,KAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,OAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,OAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,QAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,SAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,UAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,YAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,aAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,aAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,cAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,eAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,eAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,cAArC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,kBAAlC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,kBAArC,EAjCgF,CAkChF;;IACAC,SAAS,CAACqB,KAAV,CAAgBC,yBAAhB,CAA0CxB,MAA1C,CAAiDC,KAAjD,CAAuD,aAAvD,EAnCgF,CAoChF;IACA;IACA;;IACAC,SAAS,CAACqB,KAAV,CAAgBE,cAAhB,CAA+BzB,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAC,SAAS,CAACwB,UAAV,GAAuB1B,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC;IACAC,SAAS,CAACyB,OAAV,GAAoB3B,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;EACA,CA1CC,CAAF;EA4CAH,EAAE,CAAC,qDAAD,EAAwD,YAAM;IAC/D;IACA;IACA;IACA;IACA;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,eAAhB,EAAiCC,MAAjC,CAAwCC,KAAxC,CAA8C,kBAA9C,EAR+D,CAS/D;;IACAC,SAAS,CAACqB,KAAV,CAAgBC,yBAAhB,CAA0CxB,MAA1C,CAAiDC,KAAjD,CAAuD,aAAvD,EAV+D,CAW/D;IACA;IACA;;IACAI,MAAM,CAACH,SAAS,CAACqB,KAAV,CAAgBE,cAAjB,CAAN,CAAuCnB,EAAvC,CAA0CL,KAA1C,CAAgD,GAAhD,EAd+D,CAe/D;;IACAC,SAAS,CAACwB,UAAV,GAAuB1B,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC;IACAC,SAAS,CAACyB,OAAV,GAAoB3B,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;EACA,CAlBC,CAAF,CAvqB2B,CA2rB3B;;EACAH,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5C,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAAC0B,QAAV,GAAqB5B,MAArB,CAA4BC,KAA5B,CAAkC,EAAlC,EAF4C,CAG5C;IACA;IACA;;IACAC,SAAS,CAACH,KAAV,CAAgB,KAAhB,EAAuBC,MAAvB,CAA8BC,KAA9B,CAAoC,KAApC;EACA,CAPC,CAAF;EASAH,EAAE,CAAC,uFAAD,EAA0F,YAAM;IACjG;IACA;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,eAAhB,EAAiCC,MAAjC,CAAwCC,KAAxC,CAA8C,kBAA9C,EALiG,CAMjG;;IACAC,SAAS,CAACqB,KAAV,CAAgBC,yBAAhB,CAA0CxB,MAA1C,CAAiDC,KAAjD,CAAuD,WAAvD,EAPiG,CAQjG;IACA;IACA;;IACAI,MAAM,CAACH,SAAS,CAACqB,KAAV,CAAgBE,cAAjB,CAAN,CAAuCnB,EAAvC,CAA0CC,EAA1C,CAA6CC,SAA7C;IACAN,SAAS,CAACqB,KAAV,CAAgBM,4CAAhB,CAA6D7B,MAA7D,CAAoEC,KAApE,CAA0E,MAA1E;EACA,CAbC,CAAF;EAeAH,EAAE,CAAC,iFAAD,EAAoF,YAAM;IAC3F;IACA;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,gBAAhB,EAAkCC,MAAlC,CAAyCC,KAAzC,CAA+C,mBAA/C,EAL2F,CAM3F;;IACAC,SAAS,CAACqB,KAAV,CAAgBC,yBAAhB,CAA0CxB,MAA1C,CAAiDC,KAAjD,CAAuD,YAAvD,EAP2F,CAQ3F;IACA;IACA;;IACAI,MAAM,CAACH,SAAS,CAACqB,KAAV,CAAgBE,cAAjB,CAAN,CAAuCnB,EAAvC,CAA0CC,EAA1C,CAA6CC,SAA7C;IACAN,SAAS,CAACqB,KAAV,CAAgBM,4CAAhB,CAA6D7B,MAA7D,CAAoEC,KAApE,CAA0E,MAA1E;EACA,CAbC,CAAF;EAeAH,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C,IAAMiB,SAAS,GAAG,IAAIvB,SAAJ,CAAc,IAAd,CAAlB,CAD2C,CAG3C;;IACAuB,SAAS,CAAChB,KAAV,CAAgB,iBAAhB,EAAmCC,MAAnC,CAA0CC,KAA1C,CAAgD,iBAAhD;IACAc,SAAS,CAAChB,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,kBAAlC;IACAc,SAAS,CAACF,KAAV,GAN2C,CAQ3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;;IACAE,SAAS,CAAChB,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,aAA7C;IACAc,SAAS,CAACN,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,aAArC;IACAc,SAAS,CAAChB,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC;IACAc,SAAS,CAACN,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,cAArC;IACAc,SAAS,CAACF,KAAV,GAtB2C,CAwB3C;;IACAE,SAAS,CAAChB,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,iBAA7C;IACAc,SAAS,CAAChB,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,kBAAlC;IACAc,SAAS,CAACF,KAAV,GA3B2C,CA6B3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA,CAjDC,CAAF;EAmDAf,EAAE,CAAC,2GAAD,EAA8G,YAAM;IACrH;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB,CAHqH,CAIrH;IACA;IACA;;IACAU,SAAS,CAACH,KAAV,CAAgB,SAAhB,EAA2BC,MAA3B,CAAkCC,KAAlC,CAAwC,WAAxC;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,WAArC;EACA,CATC,CAAF;EAWAH,EAAE,CAAC,sDAAD,EAAyD,YAAM;IAChE;IACA,IAAMiB,SAAS,GAAG,IAAIvB,SAAJ,CAAc,IAAd,CAAlB,CAFgE,CAGhE;IACA;IACA;;IACAuB,SAAS,CAAChB,KAAV,CAAgB,QAAhB,EAA0BC,MAA1B,CAAiCC,KAAjC,CAAuC,WAAvC;IACAc,SAAS,CAAChB,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,aAAlC;IACAc,SAAS,CAAChB,KAAV,CAAgB,KAAhB,EAAuBC,MAAvB,CAA8BC,KAA9B,CAAoC,gBAApC;EACA,CATC,CAAF;EAWAH,EAAE,CAAC,6DAAD,EAAgE,YAAM;IACvE,IAAIN,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,cAA1B,EAA0CC,MAA1C,CAAiDC,KAAjD,CAAuD,kBAAvD;IACA,IAAIT,SAAJ,CAAc,IAAd,EAAoBO,KAApB,CAA0B,aAA1B,EAAyCC,MAAzC,CAAgDC,KAAhD,CAAsD,aAAtD;EACA,CAHC,CAAF;EAKAH,EAAE,CAAC,4BAAD,EAA+B,YAAM;IACtC,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IAEAU,SAAS,CAACH,KAAV,CAAgB,mBAAhB;IACA,IAAImB,WAAW,GAAGhB,SAAS,CAACe,SAAV,EAAlB;IACAC,WAAW,CAACY,WAAZ,CAAwB9B,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IAEAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,iBAAhB;IACAmB,WAAW,GAAGhB,SAAS,CAACe,SAAV,EAAd;IACAZ,MAAM,CAACa,WAAW,CAACY,WAAb,CAAN,CAAgCxB,EAAhC,CAAmCC,EAAnC,CAAsCC,SAAtC;EACA,CAXC,CAAF;EAaAV,EAAE,CAAC,+DAAD,EAAkE,YAAM;IACzE,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc;MAAEuC,kBAAkB,EAAE;IAAtB,CAAd,CAAlB;IACA7B,SAAS,CAACH,KAAV,CAAgB,aAAhB,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,mBAA5C;IACAC,SAAS,CAACe,SAAV,GAAsBG,kBAAtB,CAAyCpB,MAAzC,CAAgDC,KAAhD,CAAsD,GAAtD;IACAC,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C;EACA,CALC,CAAF;EAOAH,EAAE,CAAC,8EAAD,EAAiF,YAAM;IACxF,IAAMI,SAAS,GAAG,IAAIV,SAAJ,EAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,KAAhB;IACAM,MAAM,CAACH,SAAS,CAACe,SAAV,EAAD,CAAN,CAA8BX,EAA9B,CAAiCC,EAAjC,CAAoCC,SAApC;EACA,CAJC,CAAF;EAMAV,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE,IAAMI,SAAS,GAAG,IAAIV,SAAJ,EAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAI,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;;IACA,IAAIL,+BAAJ,EAAqC;MACpCM,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,KAApC;IACA,CAFD,MAEO;MACNI,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC;IACA;;IACDN,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,YAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,aAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,gBAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,iBAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,kBAAlC;;IACA,IAAIL,+BAAJ,EAAqC;MACpCM,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqCC,KAArC,CAA2C,KAA3C;IACA,CAFD,MAEO;MACNI,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC;IACA;;IACDN,SAAS,CAACe,SAAV,GAAsBG,kBAAtB,CAAyCpB,MAAzC,CAAgDC,KAAhD,CAAsD,KAAtD;EACA,CA3BC,CAAF;EA6BAH,EAAE,CAAC,0FAAD,EAA6F,YAAM;IACpG,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc;MAAEuC,kBAAkB,EAAE;IAAtB,CAAd,CAAlB;;IACA,IAAInC,+BAAJ,EAAqC;MACpCM,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqCC,KAArC,CAA2C,KAA3C;IACA,CAFD,MAEO;MACNI,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC;IACA;;IACDN,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,OAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,WAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,YAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,aAAlC;;IACA,IAAIL,+BAAJ,EAAqC;MACpCM,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqCC,KAArC,CAA2C,KAA3C;IACA,CAFD,MAEO;MACNI,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC;IACA;;IACDN,SAAS,CAACe,SAAV,GAAsBG,kBAAtB,CAAyCpB,MAAzC,CAAgDC,KAAhD,CAAsD,KAAtD;EACA,CAtBC,CAAF;EAwBAH,EAAE,CAAC,qFAAD,EAAwF,YAAM;IAC/F,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,KAAd,CAAlB;IACAa,MAAM,CAACH,SAAS,CAAC8B,cAAX,CAAN,CAAiC1B,EAAjC,CAAoCC,EAApC,CAAuCC,SAAvC;IACAH,MAAM,CAACH,SAAS,CAAC6B,kBAAX,CAAN,CAAqCzB,EAArC,CAAwCC,EAAxC,CAA2CC,SAA3C;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,OAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,WAAlC;IACAI,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC;IACAH,MAAM,CAACH,SAAS,CAACe,SAAV,EAAD,CAAN,CAA8BX,EAA9B,CAAiCC,EAAjC,CAAoCC,SAApC;EACA,CAfC,CAAF;EAiBAV,EAAE,CAAC,6EAAD,EAAgF,YAAM;IACvF,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,OAAhB;IACAG,SAAS,CAACe,SAAV,GAAsBI,MAAtB,CAA6BrB,MAA7B,CAAoCC,KAApC,CAA0C,MAA1C;EACA,CAJC,CAAF;EAMAH,EAAE,CAAC,sFAAD,EAAyF,YAAM;IAChG;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,aAAhB,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,kBAA5C;IACAC,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C;IACAC,SAAS,CAACe,SAAV,GAAsBK,cAAtB,CAAqCtB,MAArC,CAA4CC,KAA5C,CAAkD,WAAlD;IACAC,SAAS,CAACe,SAAV,GAAsBI,MAAtB,CAA6BrB,MAA7B,CAAoCC,KAApC,CAA0C,cAA1C,EANgG,CAOhG;;IACAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,mBAA7C;IACAC,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C;IACAC,SAAS,CAACe,SAAV,GAAsBK,cAAtB,CAAqCtB,MAArC,CAA4CC,KAA5C,CAAkD,WAAlD;IACAC,SAAS,CAACe,SAAV,GAAsBI,MAAtB,CAA6BrB,MAA7B,CAAoCC,KAApC,CAA0C,cAA1C,EAZgG,CAahG;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA,CA1BC,CAAF;EA4BAH,EAAE,CAAC,0DAAD,EAA6D,YAAM;IACpE,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,gBAAhB,EAAkCC,MAAlC,CAAyCC,KAAzC,CAA+C,mBAA/C;IACAC,SAAS,CAACS,UAAV,GAAuBX,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC;IACAC,SAAS,CAACA,SAAV,CAAoBC,QAApB,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,mBAA1C;IACAC,SAAS,CAACA,SAAV,CAAoBE,+BAApB,CAAoDJ,MAApD,CAA2DC,KAA3D,CAAiE,aAAjE;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,mBAArC;IACAC,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C;IACAC,SAAS,CAACe,SAAV,GAAsBK,cAAtB,CAAqCtB,MAArC,CAA4CC,KAA5C,CAAkD,YAAlD;IACAC,SAAS,CAACe,SAAV,GAAsBI,MAAtB,CAA6BrB,MAA7B,CAAoCC,KAApC,CAA0C,eAA1C;EACA,CAVC,CAAF;EAYAH,EAAE,CAAC,2EAAD,EAA8E,YAAM;IACrF,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,aAAhB;IACAG,SAAS,CAACA,SAAV,CAAoB+B,YAApB,CAAiCC,MAAjC,GAA0ClC,MAA1C,CAAiDC,KAAjD,CAAuD,aAAvD;IACAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,cAAhB;IACAM,MAAM,CAACH,SAAS,CAACA,SAAV,CAAoB+B,YAArB,CAAN,CAAyC3B,EAAzC,CAA4CC,EAA5C,CAA+CC,SAA/C;EACA,CAPC,CAAF;EASAV,EAAE,CAAC,4CAAD,EAA+C,YAAM;IACtD;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACA,IAAM0C,MAAM,GAAGhC,SAAS,CAACR,QAAV,CAAmByC,OAAnB,GAA6B,CAA7B,CAAf;IACAD,MAAM,CAACE,4BAAP,GAAsCpC,MAAtC,CAA6CC,KAA7C,CAAmD,QAAnD;IACAC,SAAS,CAACA,SAAV,CAAoBmC,+BAApB,CAAoDH,MAApD,EAA4DlC,MAA5D,CAAmEC,KAAnE,CAAyE,GAAzE,EANsD,CAOtD;IACA;;IACA,IAAMqC,UAAU,GAAG,IAAI9C,SAAJ,CAAc,IAAd,CAAnB;IACA,IAAM+C,OAAO,GAAGD,UAAU,CAAC5C,QAAX,CAAoByC,OAApB,GAA8B,CAA9B,CAAhB;IACAI,OAAO,CAACH,4BAAR,GAAuCpC,MAAvC,CAA8CC,KAA9C,CAAoD,KAApD;IACAqC,UAAU,CAACpC,SAAX,CAAqBmC,+BAArB,CAAqDE,OAArD,EAA8DvC,MAA9D,CAAqEC,KAArE,CAA2E,EAA3E;EACA,CAbC,CAAF;EAeAH,EAAE,CAAC,yCAAD,EAA4C,YAAM;IACnD;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACwB,UAAV,GAAuB1B,MAAvB,CAA8BC,KAA9B,CAAoC,KAApC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACwB,UAAV,GAAuB1B,MAAvB,CAA8BC,KAA9B,CAAoC,KAApC;IACAC,SAAS,CAACH,KAAV,CAAgB,YAAhB;IACAG,SAAS,CAACwB,UAAV,GAAuB1B,MAAvB,CAA8BC,KAA9B,CAAoC,IAApC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACwB,UAAV,GAAuB1B,MAAvB,CAA8BC,KAA9B,CAAoC,KAApC;EACA,CAVC,CAAF;EAYAH,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACyB,OAAV,GAAoB3B,MAApB,CAA2BC,KAA3B,CAAiC,KAAjC;IACAC,SAAS,CAACH,KAAV,CAAgB,aAAhB;IACAG,SAAS,CAACyB,OAAV,GAAoB3B,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyB,OAAV,GAAoB3B,MAApB,CAA2BC,KAA3B,CAAiC,KAAjC;EACA,CARC,CAAF;EAUAH,EAAE,CAAC,8CAAD,EAAiD,YAAM;IACxD;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACsC,eAAV,GAA4BxC,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IACAC,SAAS,CAACH,KAAV,CAAgB,aAAhB;IACAG,SAAS,CAACsC,eAAV,GAA4BxC,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC,EALwD,CAMxD;;IACA,IAAMwC,YAAY,GAAG,IAAIjD,SAAJ,EAArB;IACAiD,YAAY,CAACD,eAAb,GAA+BxC,MAA/B,CAAsCC,KAAtC,CAA4C,KAA5C;IACAwC,YAAY,CAAC1C,KAAb,CAAmB,GAAnB;IACA0C,YAAY,CAACD,eAAb,GAA+BxC,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C;IACAwC,YAAY,CAAC1C,KAAb,CAAmB,aAAnB;IACA0C,YAAY,CAACD,eAAb,GAA+BxC,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C;EACA,CAbC,CAAF;EAeAH,EAAE,CAAC,uDAAD,EAA0D,YAAM;IACjE;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAa,MAAM,CAACH,SAAS,CAACU,qBAAV,EAAD,CAAN,CAA0CN,EAA1C,CAA6CC,EAA7C,CAAgDC,SAAhD;IACAN,SAAS,CAACH,KAAV,CAAgB,aAAhB;IACAM,MAAM,CAACH,SAAS,CAACU,qBAAV,EAAD,CAAN,CAA0CN,EAA1C,CAA6CC,EAA7C,CAAgDC,SAAhD,CALiE,CAMjE;;IACA,IAAMiC,YAAY,GAAG,IAAIjD,SAAJ,EAArB;IACAa,MAAM,CAACoC,YAAY,CAAC7B,qBAAb,EAAD,CAAN,CAA6CN,EAA7C,CAAgDC,EAAhD,CAAmDC,SAAnD;IACAiC,YAAY,CAAC1C,KAAb,CAAmB,GAAnB;IACAM,MAAM,CAACoC,YAAY,CAAC7B,qBAAb,EAAD,CAAN,CAA6CN,EAA7C,CAAgDC,EAAhD,CAAmDC,SAAnD;IACAiC,YAAY,CAAC1C,KAAb,CAAmB,GAAnB;IACAM,MAAM,CAACoC,YAAY,CAAC7B,qBAAb,EAAD,CAAN,CAA6CN,EAA7C,CAAgDL,KAAhD,CAAsD,GAAtD;IACAwC,YAAY,CAAC1C,KAAb,CAAmB,YAAnB;IACAM,MAAM,CAACoC,YAAY,CAAC7B,qBAAb,EAAD,CAAN,CAA6CN,EAA7C,CAAgDL,KAAhD,CAAsD,GAAtD;EACA,CAfC,CAAF;EAiBAH,EAAE,CAAC,yCAAD,EAA4C,YAAM;IACnD;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAa,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCC,EAAlC,CAAqCC,SAArC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCL,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,YAAhB;IACAM,MAAM,CAACH,SAAS,CAACS,UAAV,EAAD,CAAN,CAA+BL,EAA/B,CAAkCL,KAAlC,CAAwC,IAAxC,EAPmD,CAQnD;;IACA,IAAMwC,YAAY,GAAG,IAAIjD,SAAJ,EAArB;IACAa,MAAM,CAACoC,YAAY,CAAC9B,UAAb,EAAD,CAAN,CAAkCL,EAAlC,CAAqCC,EAArC,CAAwCC,SAAxC;IACAiC,YAAY,CAAC1C,KAAb,CAAmB,GAAnB;IACAM,MAAM,CAACoC,YAAY,CAAC9B,UAAb,EAAD,CAAN,CAAkCL,EAAlC,CAAqCC,EAArC,CAAwCC,SAAxC;IACAiC,YAAY,CAAC1C,KAAb,CAAmB,IAAnB;IACAM,MAAM,CAACoC,YAAY,CAAC9B,UAAb,EAAD,CAAN,CAAkCL,EAAlC,CAAqCL,KAArC,CAA2C,IAA3C,EAdmD,CAenD;;IACA,IAAMyC,cAAc,GAAG,IAAIlD,SAAJ,EAAvB;IACAa,MAAM,CAACqC,cAAc,CAAC/B,UAAf,EAAD,CAAN,CAAoCL,EAApC,CAAuCC,EAAvC,CAA0CC,SAA1C;IACAkC,cAAc,CAAC3C,KAAf,CAAqB,GAArB;IACAM,MAAM,CAACqC,cAAc,CAAC/B,UAAf,EAAD,CAAN,CAAoCL,EAApC,CAAuCC,EAAvC,CAA0CC,SAA1C;IACAkC,cAAc,CAAC3C,KAAf,CAAqB,GAArB;IACAM,MAAM,CAACqC,cAAc,CAAC/B,UAAf,EAAD,CAAN,CAAoCL,EAApC,CAAuCC,EAAvC,CAA0CC,SAA1C;IACAkC,cAAc,CAAC3C,KAAf,CAAqB,YAArB;IACAM,MAAM,CAACqC,cAAc,CAAC/B,UAAf,EAAD,CAAN,CAAoCL,EAApC,CAAuCL,KAAvC,CAA6C,IAA7C;IACAyC,cAAc,CAAC3C,KAAf,CAAqB,GAArB;IACAM,MAAM,CAACqC,cAAc,CAAC/B,UAAf,EAAD,CAAN,CAAoCL,EAApC,CAAuCC,EAAvC,CAA0CC,SAA1C;EACA,CA1BC,CAAF;EA4BAV,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1C,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB,CAD0C,CAE1C;;IACAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,OAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC,EAV0C,CAW1C;IACA;;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,YAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,eAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,gBAAlC;EACA,CAjBC,CAAF;EAmBAH,EAAE,CAAC,+DAAD,EAAkE,YAAM;IACzE,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAAC0B,QAAV,GAAqB5B,MAArB,CAA4BC,KAA5B,CAAkC,EAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,MAAhB;IACAG,SAAS,CAAC0B,QAAV,GAAqB5B,MAArB,CAA4BC,KAA5B,CAAkC,MAAlC;IACAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,KAAhB;IACAG,SAAS,CAAC0B,QAAV,GAAqB5B,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;EACA,CARC,CAAF,CA5jC2B,CAskC3B;EACA;EACA;;EACAH,EAAE,CAAC,kGAAD,EAAqG,YAAM;IAC5G,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB,CAD4G,CAE5G;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACAU,SAAS,CAACH,KAAV,CAAgB,kBAAhB,EAAoCC,MAApC,CAA2CC,KAA3C,CAAiD,kBAAjD;IACAC,SAAS,CAACe,SAAV,GAAsBK,cAAtB,CAAqCtB,MAArC,CAA4CC,KAA5C,CAAkD,YAAlD;EACA,CAZC,CAAF;AAaA,CAtlCO,CAAR;AAwlCAJ,QAAQ,CAAC,4BAAD,EAA+B,YAAM;EAC5CC,EAAE,CAAC,+FAAD,EAAkG,YAAM;IACzG,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAa,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CAjBC,CAAF;EAmBAH,EAAE,CAAC,mGAAD,EAAsG,YAAM;IAC7G,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc;MAAEuC,kBAAkB,EAAE;IAAtB,CAAd,CAAlB;IACA1B,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CAjBC,CAAF;EAmBAH,EAAE,CAAC,yEAAD,EAA4E,YAAM;IACnF,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAa,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CAfC,CAAF;EAiBAH,EAAE,CAAC,6EAAD,EAAgF,YAAM;IACvF,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc;MAAEuC,kBAAkB,EAAE;IAAtB,CAAd,CAAlB;IACA1B,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CAfC,CAAF;EAiBAH,EAAE,CAAC,mFAAD,EAAsF,YAAM;IAC7F,IAAMI,SAAS,GAAG,IAAIV,SAAJ,EAAlB;IACAa,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,MAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;EACA,CATC,CAAF;EAWAH,EAAE,CAAC,8EAAD,EAAiF,YAAM;IACxF,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAa,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CArBC,CAAF;EAuBAH,EAAE,CAAC,oFAAD,EAAuF,YAAM;IAC9F,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAa,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CArBC,CAAF;EAuBAH,EAAE,CAAC,kFAAD,EAAqF,YAAM;IAC5F,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,EAAoB;MAAEuC,kBAAkB,EAAE;IAAtB,CAApB,CAAlB;IACA1B,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CArBC,CAAF;EAuBAH,EAAE,CAAC,wFAAD,EAA2F,YAAM;IAClG,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,EAAoB;MAAEuC,kBAAkB,EAAE;IAAtB,CAApB,CAAlB;IACA1B,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CArBC,CAAF;EAuBAH,EAAE,CAAC,yDAAD,EAA4D,YAAM;IACnE,IAAMI,SAAS,GAAG,IAAIV,SAAJ,EAAlB;IACAa,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,OAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,cAAxC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAG,SAAS,CAACyC,cAAV,GAA2B3C,MAA3B,CAAkCC,KAAlC,CAAwC,eAAxC;EACA,CArBC,CAAF;EAuBAH,EAAE,CAAC,yFAAD,EAA4F,YAAM;IACnG,IAAMI,SAAS,GAAG,IAAIV,SAAJ,EAAlB;IACAa,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,EAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,IAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,UAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;IACAN,SAAS,CAACH,KAAV,CAAgB,GAAhB;IACAM,MAAM,CAACH,SAAS,CAACyC,cAAV,EAAD,CAAN,CAAmCrC,EAAnC,CAAsCC,EAAtC,CAAyCC,SAAzC;EACA,CAfC,CAAF;EAiBAV,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5C;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,OAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,QAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,SAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,WAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,YAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,aAAlC;IACAC,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC;EACA,CAdC,CAAF;EAgBAH,EAAE,CAAC,qFAAD,EAAwF,YAAW;IACpG,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc;MAC/BuC,kBAAkB,EAAE;IADW,CAAd,CAAlB;IAGA7B,SAAS,CAACH,KAAV,CAAgB,GAAhB,EAAqBC,MAArB,CAA4BC,KAA5B,CAAkC,GAAlC;EACA,CALC,CAAF;EAOAH,EAAE,CAAC,kCAAD,EAAqC,YAAW;IACjD;IACA;IACA;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,KAAhB,EAAuBC,MAAvB,CAA8BC,KAA9B,CAAoC,KAApC;IACAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,KAAhB,EAAuBC,MAAvB,CAA8BC,KAA9B,CAAoC,KAApC;IACAC,SAAS,CAACW,KAAV;IACAX,SAAS,CAACH,KAAV,CAAgB,KAAhB,EAAuBC,MAAvB,CAA8BC,KAA9B,CAAoC,KAApC;EACA,CAXC,CAAF;EAaAH,EAAE,CAAC,sCAAD,EAAyC,YAAW;IACrD;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,cAAhB,EAAgCC,MAAhC,CAAuCC,KAAvC,CAA6C,iBAA7C;IACAC,SAAS,CAACO,WAAV,GAAwBT,MAAxB,CAA+BC,KAA/B,CAAqC,iBAArC;EACA,CALC,CAAF;EAOAH,EAAE,CAAC,iIAAD,EAAoI,YAAW;IAChJ;IACA;IACA;IACA,IAAMI,SAAS,GAAG,IAAIV,SAAJ,CAAc,IAAd,CAAlB;IACAU,SAAS,CAACH,KAAV,CAAgB,YAAhB;IACAG,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqC4C,GAArC,CAAyC3C,KAAzC,CAA+C,IAA/C;IACAC,SAAS,CAACe,SAAV,GAAsBE,OAAtB,CAA8BnB,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C,EAPgJ,CAShJ;;IACA,IAAMqC,UAAU,GAAG,IAAI9C,SAAJ,CAAc,IAAd,CAAnB;IACA8C,UAAU,CAACvC,KAAX,CAAiB,YAAjB;IACAuC,UAAU,CAACrB,SAAX,GAAuBE,OAAvB,CAA+BnB,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C,EAZgJ,CAchJ;IACA;;IACA,IAAM4C,UAAU,GAAG,IAAIrD,SAAJ,CAAc,IAAd,CAAnB;IACAqD,UAAU,CAAC9C,KAAX,CAAiB,YAAjB;IACA8C,UAAU,CAAC5B,SAAX,GAAuBE,OAAvB,CAA+BnB,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C;EACA,CAnBC,CAAF;AAoBA,CAvRO,CAAR;;AAyRA,SAASS,IAAT,CAAcoC,SAAd,EAAyB;EACxB,eAAcA,SAAd;AACA"}