{"version": 3, "file": "AsYouTypeFormatter.util.test.js", "names": ["describe", "it", "closeNonPairedParens", "should", "equal", "stripNonPairedParens", "repeat"], "sources": ["../source/AsYouTypeFormatter.util.test.js"], "sourcesContent": ["import { closeNonPairedParens, stripNonPairedParens, repeat } from './AsYouTypeFormatter.util.js'\r\n\r\ndescribe('closeNonPairedParens', () => {\r\n\tit('should close non-paired braces', () => {\r\n\t\tcloseNonPairedParens('(000) 123-45 (9  )', 15).should.equal('(000) 123-45 (9  )')\r\n\t})\r\n})\r\n\r\ndescribe('stripNonPairedParens', () => {\r\n\tit('should strip non-paired braces', () => {\r\n\t\tstripNonPairedParens('(000) 123-45 (9').should.equal('(000) 123-45 9')\r\n\t\tstripNonPairedParens('(000) 123-45 (9)').should.equal('(000) 123-45 (9)')\r\n\t})\r\n})\r\n\r\ndescribe('repeat', () => {\r\n\tit('should repeat string N times', () => {\r\n\t\trepeat('a', 0).should.equal('')\r\n\t\trepeat('a', 3).should.equal('aaa')\r\n\t\trepeat('a', 4).should.equal('aaaa')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEAA,QAAQ,CAAC,sBAAD,EAAyB,YAAM;EACtCC,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1C,IAAAC,4CAAA,EAAqB,oBAArB,EAA2C,EAA3C,EAA+CC,MAA/C,CAAsDC,KAAtD,CAA4D,oBAA5D;EACA,CAFC,CAAF;AAGA,CAJO,CAAR;AAMAJ,QAAQ,CAAC,sBAAD,EAAyB,YAAM;EACtCC,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1C,IAAAI,4CAAA,EAAqB,iBAArB,EAAwCF,MAAxC,CAA+CC,KAA/C,CAAqD,gBAArD;IACA,IAAAC,4CAAA,EAAqB,kBAArB,EAAyCF,MAAzC,CAAgDC,KAAhD,CAAsD,kBAAtD;EACA,CAHC,CAAF;AAIA,CALO,CAAR;AAOAJ,QAAQ,CAAC,QAAD,EAAW,YAAM;EACxBC,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxC,IAAAK,8BAAA,EAAO,GAAP,EAAY,CAAZ,EAAeH,MAAf,CAAsBC,KAAtB,CAA4B,EAA5B;IACA,IAAAE,8BAAA,EAAO,GAAP,EAAY,CAAZ,EAAeH,MAAf,CAAsBC,KAAtB,CAA4B,KAA5B;IACA,IAAAE,8BAAA,EAAO,GAAP,EAAY,CAAZ,EAAeH,MAAf,CAAsBC,KAAtB,CAA4B,MAA5B;EACA,CAJC,CAAF;AAKA,CANO,CAAR"}