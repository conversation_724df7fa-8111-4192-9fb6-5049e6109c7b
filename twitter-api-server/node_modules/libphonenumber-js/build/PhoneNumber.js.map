{"version": 3, "file": "PhoneNumber.js", "names": ["USE_NON_GEOGRAPHIC_COUNTRY_CODE", "PhoneNumber", "countryOrCountryCallingCode", "nationalNumber", "metadata", "TypeError", "isObject", "countries", "e164Number", "E164_NUMBER_REGEXP", "test", "Error", "extractCountryCallingCode", "undefined", "countryCallingCode", "number", "validateMetadata", "getCountryAndCountryCallingCode", "country", "getMetadata", "ext", "getPossibleCountriesForNumber", "isPossibleNumber", "v2", "isValidNumber", "<PERSON><PERSON><PERSON>", "isNonGeographicCallingCode", "phoneNumber", "getNumberType", "format", "options", "formatNumber", "isCountryCode", "value", "metadataJson", "selectNumberingPlan"], "sources": ["../source/PhoneNumber.js"], "sourcesContent": ["import Metadata, { validateMetadata } from './metadata.js'\r\nimport isPossibleNumber from './isPossible.js'\r\nimport isValidNumber from './isValid.js'\r\n// import checkNumberLength from './helpers/checkNumberLength.js'\r\nimport getNumberType from './helpers/getNumberType.js'\r\nimport getPossibleCountriesForNumber from './helpers/getPossibleCountriesForNumber.js'\r\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js'\r\nimport isObject from './helpers/isObject.js'\r\nimport formatNumber from './format.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nexport default class PhoneNumber {\r\n\t/**\r\n\t * @param  {string} countryOrCountryCallingCode\r\n\t * @param  {string} nationalNumber\r\n\t * @param  {object} metadata — Metadata JSON\r\n\t * @return {PhoneNumber}\r\n\t */\r\n\tconstructor(countryOrCountryCallingCode, nationalNumber, metadata) {\r\n\t\t// Validate `countryOrCountryCallingCode` argument.\r\n\t\tif (!countryOrCountryCallingCode) {\r\n\t\t\tthrow new TypeError('First argument is required')\r\n\t\t}\r\n\t\tif (typeof countryOrCountryCallingCode !== 'string') {\r\n\t\t\tthrow new TypeError('First argument must be a string')\r\n\t\t}\r\n\r\n\t\t// In case of public API use: `constructor(number, metadata)`.\r\n\t\t// Transform the arguments from `constructor(number, metadata)` to\r\n\t\t// `constructor(countryOrCountryCallingCode, nationalNumber, metadata)`.\r\n\t\tif (typeof countryOrCountryCallingCode === 'string') {\r\n\t\t\tif (countryOrCountryCallingCode[0] === '+' && !nationalNumber) {\r\n\t\t\t\tthrow new TypeError('`metadata` argument not passed')\r\n\t\t\t}\r\n\t\t\tif (isObject(nationalNumber) && isObject(nationalNumber.countries)) {\r\n\t\t\t\tmetadata = nationalNumber\r\n\t\t\t\tconst e164Number = countryOrCountryCallingCode\r\n\t\t\t\tif (!E164_NUMBER_REGEXP.test(e164Number)) {\r\n\t\t\t\t\tthrow new Error('Invalid `number` argument passed: must consist of a \"+\" followed by digits')\r\n\t\t\t\t}\r\n\t\t\t\tconst { countryCallingCode, number } = extractCountryCallingCode(e164Number, undefined, undefined, metadata)\r\n\t\t\t\tnationalNumber = number\r\n\t\t\t\tcountryOrCountryCallingCode = countryCallingCode\r\n\t\t\t\tif (!nationalNumber) {\r\n\t\t\t\t\tthrow new Error('Invalid `number` argument passed: too short')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Validate `nationalNumber` argument.\r\n\t\tif (!nationalNumber) {\r\n\t\t\tthrow new TypeError('`nationalNumber` argument is required')\r\n\t\t}\r\n\t\tif (typeof nationalNumber !== 'string') {\r\n\t\t\tthrow new TypeError('`nationalNumber` argument must be a string')\r\n\t\t}\r\n\r\n\t\t// Validate `metadata` argument.\r\n\t\tvalidateMetadata(metadata)\r\n\r\n\t\t// Initialize properties.\r\n\t\tconst { country, countryCallingCode } = getCountryAndCountryCallingCode(\r\n\t\t\tcountryOrCountryCallingCode,\r\n\t\t\tmetadata\r\n\t\t)\r\n\t\tthis.country = country\r\n\t\tthis.countryCallingCode = countryCallingCode\r\n\t\tthis.nationalNumber = nationalNumber\r\n\t\tthis.number = '+' + this.countryCallingCode + this.nationalNumber\r\n\t\t// Exclude `metadata` property output from `PhoneNumber.toString()`\r\n\t\t// so that it doesn't clutter the console output of Node.js.\r\n\t\t// Previously, when Node.js did `console.log(new PhoneNumber(...))`,\r\n\t\t// it would output the whole internal structure of the `metadata` object.\r\n\t\tthis.getMetadata = () => metadata\r\n\t}\r\n\r\n\tsetExt(ext) {\r\n\t\tthis.ext = ext\r\n\t}\r\n\r\n\tgetPossibleCountries() {\r\n\t\tif (this.country) {\r\n\t\t\treturn [this.country]\r\n\t\t}\r\n\t\treturn getPossibleCountriesForNumber(\r\n\t\t\tthis.countryCallingCode,\r\n\t\t\tthis.nationalNumber,\r\n\t\t\tthis.getMetadata()\r\n\t\t)\r\n\t}\r\n\r\n\tisPossible() {\r\n\t\treturn isPossibleNumber(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tisValid() {\r\n\t\treturn isValidNumber(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tisNonGeographic() {\r\n\t\tconst metadata = new Metadata(this.getMetadata())\r\n\t\treturn metadata.isNonGeographicCallingCode(this.countryCallingCode)\r\n\t}\r\n\r\n\tisEqual(phoneNumber) {\r\n\t\treturn this.number === phoneNumber.number && this.ext === phoneNumber.ext\r\n\t}\r\n\r\n\t// This function was originally meant to be an equivalent for `validatePhoneNumberLength()`,\r\n\t// but later it was found out that it doesn't include the possible `TOO_SHORT` result\r\n\t// returned from `parsePhoneNumberWithError()` in the original `validatePhoneNumberLength()`,\r\n\t// so eventually I simply commented out this method from the `PhoneNumber` class\r\n\t// and just left the `validatePhoneNumberLength()` function, even though that one would require\r\n\t// and additional step to also validate the actual country / calling code of the phone number.\r\n\t// validateLength() {\r\n\t// \tconst metadata = new Metadata(this.getMetadata())\r\n\t// \tmetadata.selectNumberingPlan(this.countryCallingCode)\r\n\t// \tconst result = checkNumberLength(this.nationalNumber, metadata)\r\n\t// \tif (result !== 'IS_POSSIBLE') {\r\n\t// \t\treturn result\r\n\t// \t}\r\n\t// }\r\n\r\n\tgetType() {\r\n\t\treturn getNumberType(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tformat(format, options) {\r\n\t\treturn formatNumber(\r\n\t\t\tthis,\r\n\t\t\tformat,\r\n\t\t\toptions ? { ...options, v2: true } : { v2: true },\r\n\t\t\tthis.getMetadata()\r\n\t\t)\r\n\t}\r\n\r\n\tformatNational(options) {\r\n\t\treturn this.format('NATIONAL', options)\r\n\t}\r\n\r\n\tformatInternational(options) {\r\n\t\treturn this.format('INTERNATIONAL', options)\r\n\t}\r\n\r\n\tgetURI(options) {\r\n\t\treturn this.format('RFC3966', options)\r\n\t}\r\n}\r\n\r\nconst isCountryCode = (value) => /^[A-Z]{2}$/.test(value)\r\n\r\nfunction getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadataJson) {\r\n\tlet country\r\n\tlet countryCallingCode\r\n\r\n\tconst metadata = new Metadata(metadataJson)\r\n\t// If country code is passed then derive `countryCallingCode` from it.\r\n\t// Also store the country code as `.country`.\r\n\tif (isCountryCode(countryOrCountryCallingCode)) {\r\n\t\tcountry = countryOrCountryCallingCode\r\n\t\tmetadata.selectNumberingPlan(country)\r\n\t\tcountryCallingCode = metadata.countryCallingCode()\r\n\t} else {\r\n\t\tcountryCallingCode = countryOrCountryCallingCode\r\n\t\t/* istanbul ignore if */\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tif (metadata.isNonGeographicCallingCode(countryCallingCode)) {\r\n\t\t\t\tcountry = '001'\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn {\r\n\t\tcountry,\r\n\t\tcountryCallingCode\r\n\t}\r\n}\r\n\r\nconst E164_NUMBER_REGEXP = /^\\+\\d+$/"], "mappings": ";;;;;;;;;AAAA;;AACA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,+BAA+B,GAAG,KAAxC;;IAEqBC,W;EACpB;AACD;AACA;AACA;AACA;AACA;EACC,qBAAYC,2BAAZ,EAAyCC,cAAzC,EAAyDC,QAAzD,EAAmE;IAAA;;IAClE;IACA,IAAI,CAACF,2BAAL,EAAkC;MACjC,MAAM,IAAIG,SAAJ,CAAc,4BAAd,CAAN;IACA;;IACD,IAAI,OAAOH,2BAAP,KAAuC,QAA3C,EAAqD;MACpD,MAAM,IAAIG,SAAJ,CAAc,iCAAd,CAAN;IACA,CAPiE,CASlE;IACA;IACA;;;IACA,IAAI,OAAOH,2BAAP,KAAuC,QAA3C,EAAqD;MACpD,IAAIA,2BAA2B,CAAC,CAAD,CAA3B,KAAmC,GAAnC,IAA0C,CAACC,cAA/C,EAA+D;QAC9D,MAAM,IAAIE,SAAJ,CAAc,gCAAd,CAAN;MACA;;MACD,IAAI,IAAAC,oBAAA,EAASH,cAAT,KAA4B,IAAAG,oBAAA,EAASH,cAAc,CAACI,SAAxB,CAAhC,EAAoE;QACnEH,QAAQ,GAAGD,cAAX;QACA,IAAMK,UAAU,GAAGN,2BAAnB;;QACA,IAAI,CAACO,kBAAkB,CAACC,IAAnB,CAAwBF,UAAxB,CAAL,EAA0C;UACzC,MAAM,IAAIG,KAAJ,CAAU,4EAAV,CAAN;QACA;;QACD,4BAAuC,IAAAC,qCAAA,EAA0BJ,UAA1B,EAAsCK,SAAtC,EAAiDA,SAAjD,EAA4DT,QAA5D,CAAvC;QAAA,IAAQU,mBAAR,yBAAQA,kBAAR;QAAA,IAA4BC,MAA5B,yBAA4BA,MAA5B;;QACAZ,cAAc,GAAGY,MAAjB;QACAb,2BAA2B,GAAGY,mBAA9B;;QACA,IAAI,CAACX,cAAL,EAAqB;UACpB,MAAM,IAAIQ,KAAJ,CAAU,6CAAV,CAAN;QACA;MACD;IACD,CA7BiE,CA+BlE;;;IACA,IAAI,CAACR,cAAL,EAAqB;MACpB,MAAM,IAAIE,SAAJ,CAAc,uCAAd,CAAN;IACA;;IACD,IAAI,OAAOF,cAAP,KAA0B,QAA9B,EAAwC;MACvC,MAAM,IAAIE,SAAJ,CAAc,4CAAd,CAAN;IACA,CArCiE,CAuClE;;;IACA,IAAAW,0BAAA,EAAiBZ,QAAjB,EAxCkE,CA0ClE;;IACA,4BAAwCa,+BAA+B,CACtEf,2BADsE,EAEtEE,QAFsE,CAAvE;IAAA,IAAQc,OAAR,yBAAQA,OAAR;IAAA,IAAiBJ,kBAAjB,yBAAiBA,kBAAjB;;IAIA,KAAKI,OAAL,GAAeA,OAAf;IACA,KAAKJ,kBAAL,GAA0BA,kBAA1B;IACA,KAAKX,cAAL,GAAsBA,cAAtB;IACA,KAAKY,MAAL,GAAc,MAAM,KAAKD,kBAAX,GAAgC,KAAKX,cAAnD,CAlDkE,CAmDlE;IACA;IACA;IACA;;IACA,KAAKgB,WAAL,GAAmB;MAAA,OAAMf,QAAN;IAAA,CAAnB;EACA;;;;WAED,gBAAOgB,GAAP,EAAY;MACX,KAAKA,GAAL,GAAWA,GAAX;IACA;;;WAED,gCAAuB;MACtB,IAAI,KAAKF,OAAT,EAAkB;QACjB,OAAO,CAAC,KAAKA,OAAN,CAAP;MACA;;MACD,OAAO,IAAAG,yCAAA,EACN,KAAKP,kBADC,EAEN,KAAKX,cAFC,EAGN,KAAKgB,WAAL,EAHM,CAAP;IAKA;;;WAED,sBAAa;MACZ,OAAO,IAAAG,sBAAA,EAAiB,IAAjB,EAAuB;QAAEC,EAAE,EAAE;MAAN,CAAvB,EAAqC,KAAKJ,WAAL,EAArC,CAAP;IACA;;;WAED,mBAAU;MACT,OAAO,IAAAK,mBAAA,EAAc,IAAd,EAAoB;QAAED,EAAE,EAAE;MAAN,CAApB,EAAkC,KAAKJ,WAAL,EAAlC,CAAP;IACA;;;WAED,2BAAkB;MACjB,IAAMf,QAAQ,GAAG,IAAIqB,oBAAJ,CAAa,KAAKN,WAAL,EAAb,CAAjB;MACA,OAAOf,QAAQ,CAACsB,0BAAT,CAAoC,KAAKZ,kBAAzC,CAAP;IACA;;;WAED,iBAAQa,WAAR,EAAqB;MACpB,OAAO,KAAKZ,MAAL,KAAgBY,WAAW,CAACZ,MAA5B,IAAsC,KAAKK,GAAL,KAAaO,WAAW,CAACP,GAAtE;IACA,C,CAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;;WAEA,mBAAU;MACT,OAAO,IAAAQ,yBAAA,EAAc,IAAd,EAAoB;QAAEL,EAAE,EAAE;MAAN,CAApB,EAAkC,KAAKJ,WAAL,EAAlC,CAAP;IACA;;;WAED,gBAAOU,OAAP,EAAeC,OAAf,EAAwB;MACvB,OAAO,IAAAC,mBAAA,EACN,IADM,EAENF,OAFM,EAGNC,OAAO,mCAAQA,OAAR;QAAiBP,EAAE,EAAE;MAArB,KAA8B;QAAEA,EAAE,EAAE;MAAN,CAH/B,EAIN,KAAKJ,WAAL,EAJM,CAAP;IAMA;;;WAED,wBAAeW,OAAf,EAAwB;MACvB,OAAO,KAAKD,MAAL,CAAY,UAAZ,EAAwBC,OAAxB,CAAP;IACA;;;WAED,6BAAoBA,OAApB,EAA6B;MAC5B,OAAO,KAAKD,MAAL,CAAY,eAAZ,EAA6BC,OAA7B,CAAP;IACA;;;WAED,gBAAOA,OAAP,EAAgB;MACf,OAAO,KAAKD,MAAL,CAAY,SAAZ,EAAuBC,OAAvB,CAAP;IACA;;;;;;;;AAGF,IAAME,aAAa,GAAG,SAAhBA,aAAgB,CAACC,KAAD;EAAA,OAAW,aAAavB,IAAb,CAAkBuB,KAAlB,CAAX;AAAA,CAAtB;;AAEA,SAAShB,+BAAT,CAAyCf,2BAAzC,EAAsEgC,YAAtE,EAAoF;EACnF,IAAIhB,OAAJ;EACA,IAAIJ,kBAAJ;EAEA,IAAMV,QAAQ,GAAG,IAAIqB,oBAAJ,CAAaS,YAAb,CAAjB,CAJmF,CAKnF;EACA;;EACA,IAAIF,aAAa,CAAC9B,2BAAD,CAAjB,EAAgD;IAC/CgB,OAAO,GAAGhB,2BAAV;IACAE,QAAQ,CAAC+B,mBAAT,CAA6BjB,OAA7B;IACAJ,kBAAkB,GAAGV,QAAQ,CAACU,kBAAT,EAArB;EACA,CAJD,MAIO;IACNA,kBAAkB,GAAGZ,2BAArB;IACA;;IACA,IAAIF,+BAAJ,EAAqC;MACpC,IAAII,QAAQ,CAACsB,0BAAT,CAAoCZ,kBAApC,CAAJ,EAA6D;QAC5DI,OAAO,GAAG,KAAV;MACA;IACD;EACD;;EAED,OAAO;IACNA,OAAO,EAAPA,OADM;IAENJ,kBAAkB,EAAlBA;EAFM,CAAP;AAIA;;AAED,IAAML,kBAAkB,GAAG,SAA3B"}