{"version": 3, "file": "AsYouType.js", "names": ["USE_NON_GEOGRAPHIC_COUNTRY_CODE", "AsYouType", "optionsOrDefaultCountry", "metadata", "<PERSON><PERSON><PERSON>", "getCountryAndCallingCode", "defaultCountry", "defaultCallingCode", "reset", "isObject", "hasCountry", "undefined", "isNonGeographicCallingCode", "text", "parser", "input", "state", "digits", "justLeadingPlus", "formattedOutput", "determineTheCountryIfNeeded", "nationalSignificantNumber", "formatter", "narrowDownMatchingFormats", "formattedNationalNumber", "hasSelectedNumberingPlan", "format", "reExtractNationalSignificantNumber", "nationalDigits", "getNationalDigits", "getFullNumber", "getNonFormattedNumber", "AsYouTypeState", "onCountryChange", "country", "onCallingCodeChange", "callingCode", "selectNumberingPlan", "numberingPlan", "AsYouTypeFormatter", "AsYouType<PERSON><PERSON><PERSON>", "onNationalSignificantNumberChange", "international", "isInternational", "getCallingCode", "_getCountry", "isCountryCallingCodeAmbiguous", "determineTheCountry", "prefix", "getInternationalPrefixBeforeCountryCallingCode", "spacing", "getDigitsWithoutInternationalPrefix", "complexPrefixBeforeNationalSignificantNumber", "nationalPrefix", "number", "nationalSignificantNumberMatchesInput", "getNonFormattedNationalNumberWithPrefix", "replace", "DIGIT_PLACEHOLDER", "countryCodes", "getCountryCodesForCallingCode", "length", "setCountry", "getCountryByCallingCode", "nationalNumber", "callingCode_", "countryCallingCode", "carrierCode", "ambiguousCountries", "exactCountry", "getCountryByNationalNumber", "countries", "phoneNumber", "PhoneNumber", "getNumber", "isPossible", "<PERSON><PERSON><PERSON><PERSON>", "getTemplate", "getNonFormattedTemplate"], "sources": ["../source/AsYouType.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\nimport PhoneNumber from './PhoneNumber.js'\r\nimport AsYouTypeState from './AsYouTypeState.js'\r\nimport AsYouTypeFormatter, { DIGIT_PLACEHOLDER } from './AsYouTypeFormatter.js'\r\nimport AsYouTypeParser, { extractFormattedDigitsAndPlus } from './AsYouTypeParser.js'\r\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js'\r\nimport getCountryByNationalNumber from './helpers/getCountryByNationalNumber.js'\r\nimport isObject from './helpers/isObject.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nexport default class AsYouType {\r\n\t/**\r\n\t * @param {(string|object)?} [optionsOrDefaultCountry] - The default country used for parsing non-international phone numbers. Can also be an `options` object.\r\n\t * @param {Object} metadata\r\n\t */\r\n\tconstructor(optionsOrDefaultCountry, metadata) {\r\n\t\tthis.metadata = new Metadata(metadata)\r\n\t\tconst [defaultCountry, defaultCallingCode] = this.getCountryAndCallingCode(optionsOrDefaultCountry)\r\n\t\t// `this.defaultCountry` and `this.defaultCallingCode` aren't required to be in sync.\r\n\t\t// For example, `this.defaultCountry` could be `\"AR\"` and `this.defaultCallingCode` could be `undefined`.\r\n\t\t// So `this.defaultCountry` and `this.defaultCallingCode` are totally independent.\r\n\t\tthis.defaultCountry = defaultCountry\r\n\t\tthis.defaultCallingCode = defaultCallingCode\r\n\t\tthis.reset()\r\n\t}\r\n\r\n\tgetCountryAndCallingCode(optionsOrDefaultCountry) {\r\n\t\t// Set `defaultCountry` and `defaultCallingCode` options.\r\n\t\tlet defaultCountry\r\n\t\tlet defaultCallingCode\r\n\t\t// Turns out `null` also has type \"object\". Weird.\r\n\t\tif (optionsOrDefaultCountry) {\r\n\t\t\tif (isObject(optionsOrDefaultCountry)) {\r\n\t\t\t\tdefaultCountry = optionsOrDefaultCountry.defaultCountry\r\n\t\t\t\tdefaultCallingCode = optionsOrDefaultCountry.defaultCallingCode\r\n\t\t\t} else {\r\n\t\t\t\tdefaultCountry = optionsOrDefaultCountry\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (defaultCountry && !this.metadata.hasCountry(defaultCountry)) {\r\n\t\t\tdefaultCountry = undefined\r\n\t\t}\r\n\t\tif (defaultCallingCode) {\r\n\t\t\t/* istanbul ignore if */\r\n\t\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\t\tif (this.metadata.isNonGeographicCallingCode(defaultCallingCode)) {\r\n\t\t\t\t\tdefaultCountry = '001'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn [defaultCountry, defaultCallingCode]\r\n\t}\r\n\r\n\t/**\r\n\t * Inputs \"next\" phone number characters.\r\n\t * @param  {string} text\r\n\t * @return {string} Formatted phone number characters that have been input so far.\r\n\t */\r\n\tinput(text) {\r\n\t\tconst {\r\n\t\t\tdigits,\r\n\t\t\tjustLeadingPlus\r\n\t\t} = this.parser.input(text, this.state)\r\n\t\tif (justLeadingPlus) {\r\n\t\t\tthis.formattedOutput = '+'\r\n\t\t} else if (digits) {\r\n\t\t\tthis.determineTheCountryIfNeeded()\r\n\t\t\t// Match the available formats by the currently available leading digits.\r\n\t\t\tif (this.state.nationalSignificantNumber) {\r\n\t\t\t\tthis.formatter.narrowDownMatchingFormats(this.state)\r\n\t\t\t}\r\n\t\t\tlet formattedNationalNumber\r\n\t\t\tif (this.metadata.hasSelectedNumberingPlan()) {\r\n\t\t\t\tformattedNationalNumber = this.formatter.format(digits, this.state)\r\n\t\t\t}\r\n\t\t\tif (formattedNationalNumber === undefined) {\r\n\t\t\t\t// See if another national (significant) number could be re-extracted.\r\n\t\t\t\tif (this.parser.reExtractNationalSignificantNumber(this.state)) {\r\n\t\t\t\t\tthis.determineTheCountryIfNeeded()\r\n\t\t\t\t\t// If it could, then re-try formatting the new national (significant) number.\r\n\t\t\t\t\tconst nationalDigits = this.state.getNationalDigits()\r\n\t\t\t\t\tif (nationalDigits) {\r\n\t\t\t\t\t\tformattedNationalNumber = this.formatter.format(nationalDigits, this.state)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.formattedOutput = formattedNationalNumber\r\n\t\t\t\t? this.getFullNumber(formattedNationalNumber)\r\n\t\t\t\t: this.getNonFormattedNumber()\r\n\t\t}\r\n\t\treturn this.formattedOutput\r\n\t}\r\n\r\n\treset() {\r\n\t\tthis.state = new AsYouTypeState({\r\n\t\t\tonCountryChange: (country) => {\r\n\t\t\t\t// Before version `1.6.0`, the official `AsYouType` formatter API\r\n\t\t\t\t// included the `.country` property of an `AsYouType` instance.\r\n\t\t\t\t// Since that property (along with the others) have been moved to\r\n\t\t\t\t// `this.state`, `this.country` property is emulated for compatibility\r\n\t\t\t\t// with the old versions.\r\n\t\t\t\tthis.country = country\r\n\t\t\t},\r\n\t\t\tonCallingCodeChange: (callingCode, country) => {\r\n\t\t\t\tthis.metadata.selectNumberingPlan(country, callingCode)\r\n\t\t\t\tthis.formatter.reset(this.metadata.numberingPlan, this.state)\r\n\t\t\t\tthis.parser.reset(this.metadata.numberingPlan)\r\n\t\t\t}\r\n\t\t})\r\n\t\tthis.formatter = new AsYouTypeFormatter({\r\n\t\t\tstate: this.state,\r\n\t\t\tmetadata: this.metadata\r\n\t\t})\r\n\t\tthis.parser = new AsYouTypeParser({\r\n\t\t\tdefaultCountry: this.defaultCountry,\r\n\t\t\tdefaultCallingCode: this.defaultCallingCode,\r\n\t\t\tmetadata: this.metadata,\r\n\t\t\tstate: this.state,\r\n\t\t\tonNationalSignificantNumberChange: () => {\r\n\t\t\t\tthis.determineTheCountryIfNeeded()\r\n\t\t\t\tthis.formatter.reset(this.metadata.numberingPlan, this.state)\r\n\t\t\t}\r\n\t\t})\r\n\t\tthis.state.reset({\r\n\t\t\tcountry: this.defaultCountry,\r\n\t\t\tcallingCode: this.defaultCallingCode\r\n\t\t})\r\n\t\tthis.formattedOutput = ''\r\n\t\treturn this\r\n\t}\r\n\r\n\t/**\r\n\t * Returns `true` if the phone number is being input in international format.\r\n\t * In other words, returns `true` if and only if the parsed phone number starts with a `\"+\"`.\r\n\t * @return {boolean}\r\n\t */\r\n\tisInternational() {\r\n\t\treturn this.state.international\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the \"calling code\" part of the phone number when it's being input\r\n\t * in an international format.\r\n\t * If no valid calling code has been entered so far, returns `undefined`.\r\n\t * @return {string} [callingCode]\r\n\t */\r\n\tgetCallingCode() {\r\n\t\t // If the number is being input in national format and some \"default calling code\"\r\n\t\t // has been passed to `AsYouType` constructor, then `this.state.callingCode`\r\n\t\t // is equal to that \"default calling code\".\r\n\t\t //\r\n\t\t // If the number is being input in national format and no \"default calling code\"\r\n\t\t // has been passed to `AsYouType` constructor, then returns `undefined`,\r\n\t\t // even if a \"default country\" has been passed to `AsYouType` constructor.\r\n\t\t //\r\n\t\tif (this.isInternational()) {\r\n\t\t\treturn this.state.callingCode\r\n\t\t}\r\n\t}\r\n\r\n\t// A legacy alias.\r\n\tgetCountryCallingCode() {\r\n\t\treturn this.getCallingCode()\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a two-letter country code of the phone number.\r\n\t * Returns `undefined` for \"non-geographic\" phone numbering plans.\r\n\t * Returns `undefined` if no phone number has been input yet.\r\n\t * @return {string} [country]\r\n\t */\r\n\tgetCountry() {\r\n\t\tconst { digits } = this.state\r\n\t\t// Return `undefined` if no digits have been input yet.\r\n\t\tif (digits) {\r\n\t\t\treturn this._getCountry()\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a two-letter country code of the phone number.\r\n\t * Returns `undefined` for \"non-geographic\" phone numbering plans.\r\n\t * @return {string} [country]\r\n\t */\r\n\t_getCountry() {\r\n\t\tconst { country } = this.state\r\n\t\t/* istanbul ignore if */\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\t// `AsYouType.getCountry()` returns `undefined`\r\n\t\t\t// for \"non-geographic\" phone numbering plans.\r\n\t\t\tif (country === '001') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn country\r\n\t}\r\n\r\n\tdetermineTheCountryIfNeeded() {\r\n\t\t// Suppose a user enters a phone number in international format,\r\n\t\t// and there're several countries corresponding to that country calling code,\r\n\t\t// and a country has been derived from the number, and then\r\n\t\t// a user enters one more digit and the number is no longer\r\n\t\t// valid for the derived country, so the country should be re-derived\r\n\t\t// on every new digit in those cases.\r\n\t\t//\r\n\t\t// If the phone number is being input in national format,\r\n\t\t// then it could be a case when `defaultCountry` wasn't specified\r\n\t\t// when creating `AsYouType` instance, and just `defaultCallingCode` was specified,\r\n\t\t// and that \"calling code\" could correspond to a \"non-geographic entity\",\r\n\t\t// or there could be several countries corresponding to that country calling code.\r\n\t\t// In those cases, `this.country` is `undefined` and should be derived\r\n\t\t// from the number. Again, if country calling code is ambiguous, then\r\n\t\t// `this.country` should be re-derived with each new digit.\r\n\t\t//\r\n\t\tif (!this.state.country || this.isCountryCallingCodeAmbiguous()) {\r\n\t\t\tthis.determineTheCountry()\r\n\t\t}\r\n\t}\r\n\r\n\t// Prepends `+CountryCode ` in case of an international phone number\r\n\tgetFullNumber(formattedNationalNumber) {\r\n\t\tif (this.isInternational()) {\r\n\t\t\tconst prefix = (text) => this.formatter.getInternationalPrefixBeforeCountryCallingCode(this.state, {\r\n\t\t\t\tspacing: text ? true : false\r\n\t\t\t}) + text\r\n\t\t\tconst { callingCode } = this.state\r\n\t\t\tif (!callingCode) {\r\n\t\t\t\treturn prefix(`${this.state.getDigitsWithoutInternationalPrefix()}`)\r\n\t\t\t}\r\n\t\t\tif (!formattedNationalNumber) {\r\n\t\t\t\treturn prefix(callingCode)\r\n\t\t\t}\r\n\t\t\treturn prefix(`${callingCode} ${formattedNationalNumber}`)\r\n\t\t}\r\n\t\treturn formattedNationalNumber\r\n\t}\r\n\r\n\tgetNonFormattedNationalNumberWithPrefix() {\r\n\t\tconst {\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tcomplexPrefixBeforeNationalSignificantNumber,\r\n\t\t\tnationalPrefix\r\n\t\t} = this.state\r\n\t\tlet number = nationalSignificantNumber\r\n\t\tconst prefix = complexPrefixBeforeNationalSignificantNumber || nationalPrefix\r\n\t\tif (prefix) {\r\n\t\t\tnumber = prefix + number\r\n\t\t}\r\n\t\treturn number\r\n\t}\r\n\r\n\tgetNonFormattedNumber() {\r\n\t\tconst { nationalSignificantNumberMatchesInput } = this.state\r\n\t\treturn this.getFullNumber(\r\n\t\t\tnationalSignificantNumberMatchesInput\r\n\t\t\t\t? this.getNonFormattedNationalNumberWithPrefix()\r\n\t\t\t\t: this.state.getNationalDigits()\r\n\t\t)\r\n\t}\r\n\r\n\tgetNonFormattedTemplate() {\r\n\t\tconst number = this.getNonFormattedNumber()\r\n\t\tif (number) {\r\n\t\t\treturn number.replace(/[\\+\\d]/g, DIGIT_PLACEHOLDER)\r\n\t\t}\r\n\t}\r\n\r\n\tisCountryCallingCodeAmbiguous() {\r\n\t\tconst { callingCode } = this.state\r\n\t\tconst countryCodes = this.metadata.getCountryCodesForCallingCode(callingCode)\r\n\t\treturn countryCodes && countryCodes.length > 1\r\n\t}\r\n\r\n\t// Determines the country of the phone number\r\n\t// entered so far based on the country phone code\r\n\t// and the national phone number.\r\n\tdetermineTheCountry() {\r\n\t\tthis.state.setCountry(getCountryByCallingCode(\r\n\t\t\tthis.isInternational() ? this.state.callingCode : this.defaultCallingCode,\r\n\t\t\t{\r\n\t\t\t\tnationalNumber: this.state.nationalSignificantNumber,\r\n\t\t\t\tdefaultCountry: this.defaultCountry,\r\n\t\t\t\tmetadata: this.metadata\r\n\t\t\t}\r\n\t\t))\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a E.164 phone number value for the user's input.\r\n\t *\r\n\t * For example, for country `\"US\"` and input `\"(*************\"`\r\n\t * it will return `\"+12223334444\"`.\r\n\t *\r\n\t * For international phone number input, it will also auto-correct\r\n\t * some minor errors such as using a national prefix when writing\r\n\t * an international phone number. For example, if the user inputs\r\n\t * `\"+44 0 7400 000000\"` then it will return an auto-corrected\r\n\t * `\"+447400000000\"` phone number value.\r\n\t *\r\n\t * Will return `undefined` if no digits have been input,\r\n\t * or when inputting a phone number in national format and no\r\n\t * default country or default \"country calling code\" have been set.\r\n\t *\r\n\t * @return {string} [value]\r\n\t */\r\n\tgetNumberValue() {\r\n\t\tconst {\r\n\t\t\tdigits,\r\n\t\t\tcallingCode,\r\n\t\t\tcountry,\r\n\t\t\tnationalSignificantNumber\r\n\t\t} = this.state\r\n\r\n\t \t// Will return `undefined` if no digits have been input.\r\n\t\tif (!digits) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tif (this.isInternational()) {\r\n\t\t\tif (callingCode) {\r\n\t\t\t\treturn '+' + callingCode + nationalSignificantNumber\r\n\t\t\t} else {\r\n\t\t\t\treturn '+' + digits\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (country || callingCode) {\r\n\t\t\t\tconst callingCode_ = country ? this.metadata.countryCallingCode() : callingCode\r\n\t\t\t\treturn '+' + callingCode_ + nationalSignificantNumber\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Returns an instance of `PhoneNumber` class.\r\n\t * Will return `undefined` if no national (significant) number\r\n\t * digits have been entered so far, or if no `defaultCountry` has been\r\n\t * set and the user enters a phone number not in international format.\r\n\t */\r\n\tgetNumber() {\r\n\t\tconst {\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tcarrierCode,\r\n\t\t\tcallingCode\r\n\t\t} = this.state\r\n\r\n\t\t// `this._getCountry()` is basically same as `this.state.country`\r\n\t\t// with the only change that it return `undefined` in case of a\r\n\t\t// \"non-geographic\" numbering plan instead of `\"001\"` \"internal use\" value.\r\n\t\tlet country = this._getCountry()\r\n\r\n\t\tif (!nationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// `state.country` and `state.callingCode` aren't required to be in sync.\r\n\t\t// For example, `country` could be `\"AR\"` and `callingCode` could be `undefined`.\r\n\t\t// So `country` and `callingCode` are totally independent.\r\n\r\n\t\tif (!country && !callingCode) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// By default, if `defaultCountry` parameter was passed when\r\n\t\t// creating `AsYouType` instance, `state.country` is gonna be\r\n\t\t// that `defaultCountry`, which doesn't entirely conform with\r\n\t\t// `parsePhoneNumber()`'s behavior where it attempts to determine\r\n\t\t// the country more precisely in cases when multiple countries\r\n\t\t// could correspond to the same `countryCallingCode`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/103#note_1417192969\r\n\t\t//\r\n\t\t// Because `AsYouType.getNumber()` method is supposed to be a 1:1\r\n\t\t// equivalent for `parsePhoneNumber(AsYouType.getNumberValue())`,\r\n\t\t// then it should also behave accordingly in cases of `country` ambiguity.\r\n\t\t// That's how users of this library would expect it to behave anyway.\r\n\t\t//\r\n\t\tif (country) {\r\n\t\t\tif (country === this.defaultCountry) {\r\n\t\t\t\t// `state.country` and `state.callingCode` aren't required to be in sync.\r\n\t\t\t\t// For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\r\n\t\t\t\t// So `state.country` and `state.callingCode` are totally independent.\r\n\t\t\t\tconst metadata = new Metadata(this.metadata.metadata)\r\n\t\t\t\tmetadata.selectNumberingPlan(country)\r\n\t\t\t\tconst callingCode = metadata.numberingPlan.callingCode()\r\n\t\t\t\tconst ambiguousCountries = this.metadata.getCountryCodesForCallingCode(callingCode)\r\n\t\t\t\tif (ambiguousCountries.length > 1) {\r\n\t\t\t\t\tconst exactCountry = getCountryByNationalNumber(nationalSignificantNumber, {\r\n\t\t\t\t\t\tcountries: ambiguousCountries,\r\n\t\t\t\t\t\tdefaultCountry: this.defaultCountry,\r\n\t\t\t\t\t\tmetadata: this.metadata.metadata\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (exactCountry) {\r\n\t\t\t\t\t\tcountry = exactCountry\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst phoneNumber = new PhoneNumber(\r\n\t\t\tcountry || callingCode,\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (carrierCode) {\r\n\t\t\tphoneNumber.carrierCode = carrierCode\r\n\t\t}\r\n\t\t// Phone number extensions are not supported by \"As You Type\" formatter.\r\n\t\treturn phoneNumber\r\n\t}\r\n\r\n\t/**\r\n\t * Returns `true` if the phone number is \"possible\".\r\n\t * Is just a shortcut for `PhoneNumber.isPossible()`.\r\n\t * @return {boolean}\r\n\t */\r\n\tisPossible() {\r\n\t\tconst phoneNumber = this.getNumber()\r\n\t\tif (!phoneNumber) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\treturn phoneNumber.isPossible()\r\n\t}\r\n\r\n\t/**\r\n\t * Returns `true` if the phone number is \"valid\".\r\n\t * Is just a shortcut for `PhoneNumber.isValid()`.\r\n\t * @return {boolean}\r\n\t */\r\n\tisValid() {\r\n\t\tconst phoneNumber = this.getNumber()\r\n\t\tif (!phoneNumber) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\treturn phoneNumber.isValid()\r\n\t}\r\n\r\n\t/**\r\n\t * @deprecated\r\n\t * This method is used in `react-phone-number-input/source/input-control.js`\r\n\t * in versions before `3.0.16`.\r\n\t */\r\n\tgetNationalNumber() {\r\n\t\treturn this.state.nationalSignificantNumber\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the phone number characters entered by the user.\r\n\t * @return {string}\r\n\t */\r\n\tgetChars() {\r\n\t\treturn (this.state.international ? '+' : '') + this.state.digits\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the template for the formatted phone number.\r\n\t * @return {string}\r\n\t */\r\n\tgetTemplate() {\r\n\t\treturn this.formatter.getTemplate(this.state) || this.getNonFormattedTemplate() || ''\r\n\t}\r\n}"], "mappings": ";;;;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,+BAA+B,GAAG,KAAxC;;IAEqBC,S;EACpB;AACD;AACA;AACA;EACC,mBAAYC,uBAAZ,EAAqCC,QAArC,EAA+C;IAAA;;IAC9C,KAAKA,QAAL,GAAgB,IAAIC,oBAAJ,CAAaD,QAAb,CAAhB;;IACA,4BAA6C,KAAKE,wBAAL,CAA8BH,uBAA9B,CAA7C;IAAA;IAAA,IAAOI,cAAP;IAAA,IAAuBC,kBAAvB,6BAF8C,CAG9C;IACA;IACA;;;IACA,KAAKD,cAAL,GAAsBA,cAAtB;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,KAAL;EACA;;;;WAED,kCAAyBN,uBAAzB,EAAkD;MACjD;MACA,IAAII,cAAJ;MACA,IAAIC,kBAAJ,CAHiD,CAIjD;;MACA,IAAIL,uBAAJ,EAA6B;QAC5B,IAAI,IAAAO,oBAAA,EAASP,uBAAT,CAAJ,EAAuC;UACtCI,cAAc,GAAGJ,uBAAuB,CAACI,cAAzC;UACAC,kBAAkB,GAAGL,uBAAuB,CAACK,kBAA7C;QACA,CAHD,MAGO;UACND,cAAc,GAAGJ,uBAAjB;QACA;MACD;;MACD,IAAII,cAAc,IAAI,CAAC,KAAKH,QAAL,CAAcO,UAAd,CAAyBJ,cAAzB,CAAvB,EAAiE;QAChEA,cAAc,GAAGK,SAAjB;MACA;;MACD,IAAIJ,kBAAJ,EAAwB;QACvB;QACA,IAAIP,+BAAJ,EAAqC;UACpC,IAAI,KAAKG,QAAL,CAAcS,0BAAd,CAAyCL,kBAAzC,CAAJ,EAAkE;YACjED,cAAc,GAAG,KAAjB;UACA;QACD;MACD;;MACD,OAAO,CAACA,cAAD,EAAiBC,kBAAjB,CAAP;IACA;IAED;AACD;AACA;AACA;AACA;;;;WACC,eAAMM,IAAN,EAAY;MACX,yBAGI,KAAKC,MAAL,CAAYC,KAAZ,CAAkBF,IAAlB,EAAwB,KAAKG,KAA7B,CAHJ;MAAA,IACCC,MADD,sBACCA,MADD;MAAA,IAECC,eAFD,sBAECA,eAFD;;MAIA,IAAIA,eAAJ,EAAqB;QACpB,KAAKC,eAAL,GAAuB,GAAvB;MACA,CAFD,MAEO,IAAIF,MAAJ,EAAY;QAClB,KAAKG,2BAAL,GADkB,CAElB;;QACA,IAAI,KAAKJ,KAAL,CAAWK,yBAAf,EAA0C;UACzC,KAAKC,SAAL,CAAeC,yBAAf,CAAyC,KAAKP,KAA9C;QACA;;QACD,IAAIQ,uBAAJ;;QACA,IAAI,KAAKrB,QAAL,CAAcsB,wBAAd,EAAJ,EAA8C;UAC7CD,uBAAuB,GAAG,KAAKF,SAAL,CAAeI,MAAf,CAAsBT,MAAtB,EAA8B,KAAKD,KAAnC,CAA1B;QACA;;QACD,IAAIQ,uBAAuB,KAAKb,SAAhC,EAA2C;UAC1C;UACA,IAAI,KAAKG,MAAL,CAAYa,kCAAZ,CAA+C,KAAKX,KAApD,CAAJ,EAAgE;YAC/D,KAAKI,2BAAL,GAD+D,CAE/D;;YACA,IAAMQ,cAAc,GAAG,KAAKZ,KAAL,CAAWa,iBAAX,EAAvB;;YACA,IAAID,cAAJ,EAAoB;cACnBJ,uBAAuB,GAAG,KAAKF,SAAL,CAAeI,MAAf,CAAsBE,cAAtB,EAAsC,KAAKZ,KAA3C,CAA1B;YACA;UACD;QACD;;QACD,KAAKG,eAAL,GAAuBK,uBAAuB,GAC3C,KAAKM,aAAL,CAAmBN,uBAAnB,CAD2C,GAE3C,KAAKO,qBAAL,EAFH;MAGA;;MACD,OAAO,KAAKZ,eAAZ;IACA;;;WAED,iBAAQ;MAAA;;MACP,KAAKH,KAAL,GAAa,IAAIgB,0BAAJ,CAAmB;QAC/BC,eAAe,EAAE,yBAACC,OAAD,EAAa;UAC7B;UACA;UACA;UACA;UACA;UACA,KAAI,CAACA,OAAL,GAAeA,OAAf;QACA,CAR8B;QAS/BC,mBAAmB,EAAE,6BAACC,WAAD,EAAcF,OAAd,EAA0B;UAC9C,KAAI,CAAC/B,QAAL,CAAckC,mBAAd,CAAkCH,OAAlC,EAA2CE,WAA3C;;UACA,KAAI,CAACd,SAAL,CAAed,KAAf,CAAqB,KAAI,CAACL,QAAL,CAAcmC,aAAnC,EAAkD,KAAI,CAACtB,KAAvD;;UACA,KAAI,CAACF,MAAL,CAAYN,KAAZ,CAAkB,KAAI,CAACL,QAAL,CAAcmC,aAAhC;QACA;MAb8B,CAAnB,CAAb;MAeA,KAAKhB,SAAL,GAAiB,IAAIiB,8BAAJ,CAAuB;QACvCvB,KAAK,EAAE,KAAKA,KAD2B;QAEvCb,QAAQ,EAAE,KAAKA;MAFwB,CAAvB,CAAjB;MAIA,KAAKW,MAAL,GAAc,IAAI0B,2BAAJ,CAAoB;QACjClC,cAAc,EAAE,KAAKA,cADY;QAEjCC,kBAAkB,EAAE,KAAKA,kBAFQ;QAGjCJ,QAAQ,EAAE,KAAKA,QAHkB;QAIjCa,KAAK,EAAE,KAAKA,KAJqB;QAKjCyB,iCAAiC,EAAE,6CAAM;UACxC,KAAI,CAACrB,2BAAL;;UACA,KAAI,CAACE,SAAL,CAAed,KAAf,CAAqB,KAAI,CAACL,QAAL,CAAcmC,aAAnC,EAAkD,KAAI,CAACtB,KAAvD;QACA;MARgC,CAApB,CAAd;MAUA,KAAKA,KAAL,CAAWR,KAAX,CAAiB;QAChB0B,OAAO,EAAE,KAAK5B,cADE;QAEhB8B,WAAW,EAAE,KAAK7B;MAFF,CAAjB;MAIA,KAAKY,eAAL,GAAuB,EAAvB;MACA,OAAO,IAAP;IACA;IAED;AACD;AACA;AACA;AACA;;;;WACC,2BAAkB;MACjB,OAAO,KAAKH,KAAL,CAAW0B,aAAlB;IACA;IAED;AACD;AACA;AACA;AACA;AACA;;;;WACC,0BAAiB;MACf;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACD,IAAI,KAAKC,eAAL,EAAJ,EAA4B;QAC3B,OAAO,KAAK3B,KAAL,CAAWoB,WAAlB;MACA;IACD,C,CAED;;;;WACA,iCAAwB;MACvB,OAAO,KAAKQ,cAAL,EAAP;IACA;IAED;AACD;AACA;AACA;AACA;AACA;;;;WACC,sBAAa;MACZ,IAAQ3B,MAAR,GAAmB,KAAKD,KAAxB,CAAQC,MAAR,CADY,CAEZ;;MACA,IAAIA,MAAJ,EAAY;QACX,OAAO,KAAK4B,WAAL,EAAP;MACA;IACD;IAED;AACD;AACA;AACA;AACA;;;;WACC,uBAAc;MACb,IAAQX,OAAR,GAAoB,KAAKlB,KAAzB,CAAQkB,OAAR;MACA;;MACA,IAAIlC,+BAAJ,EAAqC;QACpC;QACA;QACA,IAAIkC,OAAO,KAAK,KAAhB,EAAuB;UACtB;QACA;MACD;;MACD,OAAOA,OAAP;IACA;;;WAED,uCAA8B;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC,KAAKlB,KAAL,CAAWkB,OAAZ,IAAuB,KAAKY,6BAAL,EAA3B,EAAiE;QAChE,KAAKC,mBAAL;MACA;IACD,C,CAED;;;;WACA,uBAAcvB,uBAAd,EAAuC;MAAA;;MACtC,IAAI,KAAKmB,eAAL,EAAJ,EAA4B;QAC3B,IAAMK,MAAM,GAAG,SAATA,MAAS,CAACnC,IAAD;UAAA,OAAU,MAAI,CAACS,SAAL,CAAe2B,8CAAf,CAA8D,MAAI,CAACjC,KAAnE,EAA0E;YAClGkC,OAAO,EAAErC,IAAI,GAAG,IAAH,GAAU;UAD2E,CAA1E,IAEpBA,IAFU;QAAA,CAAf;;QAGA,IAAQuB,WAAR,GAAwB,KAAKpB,KAA7B,CAAQoB,WAAR;;QACA,IAAI,CAACA,WAAL,EAAkB;UACjB,OAAOY,MAAM,WAAI,KAAKhC,KAAL,CAAWmC,mCAAX,EAAJ,EAAb;QACA;;QACD,IAAI,CAAC3B,uBAAL,EAA8B;UAC7B,OAAOwB,MAAM,CAACZ,WAAD,CAAb;QACA;;QACD,OAAOY,MAAM,WAAIZ,WAAJ,cAAmBZ,uBAAnB,EAAb;MACA;;MACD,OAAOA,uBAAP;IACA;;;WAED,mDAA0C;MACzC,kBAII,KAAKR,KAJT;MAAA,IACCK,yBADD,eACCA,yBADD;MAAA,IAEC+B,4CAFD,eAECA,4CAFD;MAAA,IAGCC,cAHD,eAGCA,cAHD;MAKA,IAAIC,MAAM,GAAGjC,yBAAb;MACA,IAAM2B,MAAM,GAAGI,4CAA4C,IAAIC,cAA/D;;MACA,IAAIL,MAAJ,EAAY;QACXM,MAAM,GAAGN,MAAM,GAAGM,MAAlB;MACA;;MACD,OAAOA,MAAP;IACA;;;WAED,iCAAwB;MACvB,IAAQC,qCAAR,GAAkD,KAAKvC,KAAvD,CAAQuC,qCAAR;MACA,OAAO,KAAKzB,aAAL,CACNyB,qCAAqC,GAClC,KAAKC,uCAAL,EADkC,GAElC,KAAKxC,KAAL,CAAWa,iBAAX,EAHG,CAAP;IAKA;;;WAED,mCAA0B;MACzB,IAAMyB,MAAM,GAAG,KAAKvB,qBAAL,EAAf;;MACA,IAAIuB,MAAJ,EAAY;QACX,OAAOA,MAAM,CAACG,OAAP,CAAe,SAAf,EAA0BC,qCAA1B,CAAP;MACA;IACD;;;WAED,yCAAgC;MAC/B,IAAQtB,WAAR,GAAwB,KAAKpB,KAA7B,CAAQoB,WAAR;MACA,IAAMuB,YAAY,GAAG,KAAKxD,QAAL,CAAcyD,6BAAd,CAA4CxB,WAA5C,CAArB;MACA,OAAOuB,YAAY,IAAIA,YAAY,CAACE,MAAb,GAAsB,CAA7C;IACA,C,CAED;IACA;IACA;;;;WACA,+BAAsB;MACrB,KAAK7C,KAAL,CAAW8C,UAAX,CAAsB,IAAAC,mCAAA,EACrB,KAAKpB,eAAL,KAAyB,KAAK3B,KAAL,CAAWoB,WAApC,GAAkD,KAAK7B,kBADlC,EAErB;QACCyD,cAAc,EAAE,KAAKhD,KAAL,CAAWK,yBAD5B;QAECf,cAAc,EAAE,KAAKA,cAFtB;QAGCH,QAAQ,EAAE,KAAKA;MAHhB,CAFqB,CAAtB;IAQA;IAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;WACC,0BAAiB;MAChB,mBAKI,KAAKa,KALT;MAAA,IACCC,MADD,gBACCA,MADD;MAAA,IAECmB,WAFD,gBAECA,WAFD;MAAA,IAGCF,OAHD,gBAGCA,OAHD;MAAA,IAICb,yBAJD,gBAICA,yBAJD,CADgB,CAQf;;MACD,IAAI,CAACJ,MAAL,EAAa;QACZ;MACA;;MAED,IAAI,KAAK0B,eAAL,EAAJ,EAA4B;QAC3B,IAAIP,WAAJ,EAAiB;UAChB,OAAO,MAAMA,WAAN,GAAoBf,yBAA3B;QACA,CAFD,MAEO;UACN,OAAO,MAAMJ,MAAb;QACA;MACD,CAND,MAMO;QACN,IAAIiB,OAAO,IAAIE,WAAf,EAA4B;UAC3B,IAAM6B,YAAY,GAAG/B,OAAO,GAAG,KAAK/B,QAAL,CAAc+D,kBAAd,EAAH,GAAwC9B,WAApE;UACA,OAAO,MAAM6B,YAAN,GAAqB5C,yBAA5B;QACA;MACD;IACD;IAED;AACD;AACA;AACA;AACA;AACA;;;;WACC,qBAAY;MACX,mBAII,KAAKL,KAJT;MAAA,IACCK,yBADD,gBACCA,yBADD;MAAA,IAEC8C,WAFD,gBAECA,WAFD;MAAA,IAGC/B,WAHD,gBAGCA,WAHD,CADW,CAOX;MACA;MACA;;MACA,IAAIF,OAAO,GAAG,KAAKW,WAAL,EAAd;;MAEA,IAAI,CAACxB,yBAAL,EAAgC;QAC/B;MACA,CAdU,CAgBX;MACA;MACA;;;MAEA,IAAI,CAACa,OAAD,IAAY,CAACE,WAAjB,EAA8B;QAC7B;MACA,CAtBU,CAwBX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;MACA,IAAIF,OAAJ,EAAa;QACZ,IAAIA,OAAO,KAAK,KAAK5B,cAArB,EAAqC;UACpC;UACA;UACA;UACA,IAAMH,QAAQ,GAAG,IAAIC,oBAAJ,CAAa,KAAKD,QAAL,CAAcA,QAA3B,CAAjB;UACAA,QAAQ,CAACkC,mBAAT,CAA6BH,OAA7B;;UACA,IAAME,YAAW,GAAGjC,QAAQ,CAACmC,aAAT,CAAuBF,WAAvB,EAApB;;UACA,IAAMgC,kBAAkB,GAAG,KAAKjE,QAAL,CAAcyD,6BAAd,CAA4CxB,YAA5C,CAA3B;;UACA,IAAIgC,kBAAkB,CAACP,MAAnB,GAA4B,CAAhC,EAAmC;YAClC,IAAMQ,YAAY,GAAG,IAAAC,sCAAA,EAA2BjD,yBAA3B,EAAsD;cAC1EkD,SAAS,EAAEH,kBAD+D;cAE1E9D,cAAc,EAAE,KAAKA,cAFqD;cAG1EH,QAAQ,EAAE,KAAKA,QAAL,CAAcA;YAHkD,CAAtD,CAArB;;YAKA,IAAIkE,YAAJ,EAAkB;cACjBnC,OAAO,GAAGmC,YAAV;YACA;UACD;QACD;MACD;;MAED,IAAMG,WAAW,GAAG,IAAIC,uBAAJ,CACnBvC,OAAO,IAAIE,WADQ,EAEnBf,yBAFmB,EAGnB,KAAKlB,QAAL,CAAcA,QAHK,CAApB;;MAKA,IAAIgE,WAAJ,EAAiB;QAChBK,WAAW,CAACL,WAAZ,GAA0BA,WAA1B;MACA,CAlEU,CAmEX;;;MACA,OAAOK,WAAP;IACA;IAED;AACD;AACA;AACA;AACA;;;;WACC,sBAAa;MACZ,IAAMA,WAAW,GAAG,KAAKE,SAAL,EAApB;;MACA,IAAI,CAACF,WAAL,EAAkB;QACjB,OAAO,KAAP;MACA;;MACD,OAAOA,WAAW,CAACG,UAAZ,EAAP;IACA;IAED;AACD;AACA;AACA;AACA;;;;WACC,mBAAU;MACT,IAAMH,WAAW,GAAG,KAAKE,SAAL,EAApB;;MACA,IAAI,CAACF,WAAL,EAAkB;QACjB,OAAO,KAAP;MACA;;MACD,OAAOA,WAAW,CAACI,OAAZ,EAAP;IACA;IAED;AACD;AACA;AACA;AACA;;;;WACC,6BAAoB;MACnB,OAAO,KAAK5D,KAAL,CAAWK,yBAAlB;IACA;IAED;AACD;AACA;AACA;;;;WACC,oBAAW;MACV,OAAO,CAAC,KAAKL,KAAL,CAAW0B,aAAX,GAA2B,GAA3B,GAAiC,EAAlC,IAAwC,KAAK1B,KAAL,CAAWC,MAA1D;IACA;IAED;AACD;AACA;AACA;;;;WACC,uBAAc;MACb,OAAO,KAAKK,SAAL,CAAeuD,WAAf,CAA2B,KAAK7D,KAAhC,KAA0C,KAAK8D,uBAAL,EAA1C,IAA4E,EAAnF;IACA"}