{"version": 3, "file": "RFC3966.test.js", "names": ["parseRFC3966", "formatRFC3966", "describe", "it", "expect", "number", "to", "should", "equal", "ext", "deep"], "sources": ["../../source/helpers/RFC3966.test.js"], "sourcesContent": ["import { parseRFC3966, formatRFC3966 } from './RFC3966.js'\r\n\r\ndescribe('RFC3966', () => {\r\n\tit('should format', () => {\r\n\t\texpect(() => formatRFC3966({ number: '123' })).to.throw('expects \"number\" to be in E.164 format')\r\n\t\tformatRFC3966({}).should.equal('')\r\n\t\tformatRFC3966({ number: '+78005553535' }).should.equal('tel:+78005553535')\r\n\t\tformatRFC3966({ number: '+78005553535', ext: '123' }).should.equal('tel:+78005553535;ext=123')\r\n\t})\r\n\r\n\tit('should parse', () => {\r\n\t\tparseRFC3966('tel:+78005553535').should.deep.equal({\r\n\t\t\tnumber : '+78005553535'\r\n\t\t})\r\n\r\n\t\tparseRFC3966('tel:+78005553535;ext=123').should.deep.equal({\r\n\t\t\tnumber : '+78005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// With `phone-context`\r\n\t\tparseRFC3966('tel:8005553535;ext=123;phone-context=+7').should.deep.equal({\r\n\t\t\tnumber : '+78005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// \"Domain contexts\" are ignored\r\n\t\tparseRFC3966('tel:8005553535;ext=123;phone-context=www.leningrad.spb.ru').should.deep.equal({\r\n\t\t\tnumber : '8005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// Not a viable phone number.\r\n\t\tparseRFC3966('tel:3').should.deep.equal({})\r\n\t})\r\n})\r\n"], "mappings": "AAAA,SAASA,YAAT,EAAuBC,aAAvB,QAA4C,cAA5C;AAEAC,QAAQ,CAAC,SAAD,EAAY,YAAM;EACzBC,EAAE,CAAC,eAAD,EAAkB,YAAM;IACzBC,MAAM,CAAC;MAAA,OAAMH,aAAa,CAAC;QAAEI,MAAM,EAAE;MAAV,CAAD,CAAnB;IAAA,CAAD,CAAN,CAA+CC,EAA/C,UAAwD,wCAAxD;IACAL,aAAa,CAAC,EAAD,CAAb,CAAkBM,MAAlB,CAAyBC,KAAzB,CAA+B,EAA/B;IACAP,aAAa,CAAC;MAAEI,MAAM,EAAE;IAAV,CAAD,CAAb,CAA0CE,MAA1C,CAAiDC,KAAjD,CAAuD,kBAAvD;IACAP,aAAa,CAAC;MAAEI,MAAM,EAAE,cAAV;MAA0BI,GAAG,EAAE;IAA/B,CAAD,CAAb,CAAsDF,MAAtD,CAA6DC,KAA7D,CAAmE,0BAAnE;EACA,CALC,CAAF;EAOAL,EAAE,CAAC,cAAD,EAAiB,YAAM;IACxBH,YAAY,CAAC,kBAAD,CAAZ,CAAiCO,MAAjC,CAAwCG,IAAxC,CAA6CF,KAA7C,CAAmD;MAClDH,MAAM,EAAG;IADyC,CAAnD;IAIAL,YAAY,CAAC,0BAAD,CAAZ,CAAyCO,MAAzC,CAAgDG,IAAhD,CAAqDF,KAArD,CAA2D;MAC1DH,MAAM,EAAG,cADiD;MAE1DI,GAAG,EAAM;IAFiD,CAA3D,EALwB,CAUxB;;IACAT,YAAY,CAAC,yCAAD,CAAZ,CAAwDO,MAAxD,CAA+DG,IAA/D,CAAoEF,KAApE,CAA0E;MACzEH,MAAM,EAAG,cADgE;MAEzEI,GAAG,EAAM;IAFgE,CAA1E,EAXwB,CAgBxB;;IACAT,YAAY,CAAC,2DAAD,CAAZ,CAA0EO,MAA1E,CAAiFG,IAAjF,CAAsFF,KAAtF,CAA4F;MAC3FH,MAAM,EAAG,YADkF;MAE3FI,GAAG,EAAM;IAFkF,CAA5F,EAjBwB,CAsBxB;;IACAT,YAAY,CAAC,OAAD,CAAZ,CAAsBO,MAAtB,CAA6BG,IAA7B,CAAkCF,KAAlC,CAAwC,EAAxC;EACA,CAxBC,CAAF;AAyBA,CAjCO,CAAR"}