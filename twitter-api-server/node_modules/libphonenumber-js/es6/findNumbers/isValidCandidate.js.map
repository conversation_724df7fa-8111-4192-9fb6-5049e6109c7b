{"version": 3, "file": "isValidCandidate.js", "names": ["PLUS_CHARS", "limit", "isLatinLetter", "isInvalidPunctuationSymbol", "OPENING_PARENS", "CLOSING_PARENS", "NON_PARENS", "LEAD_CLASS", "LEAD_CLASS_LEADING", "RegExp", "BRACKET_PAIR_LIMIT", "MATCHING_BRACKETS_ENTIRE", "PUB_PAGES", "isValidCandidate", "candidate", "offset", "text", "leniency", "test", "previousChar", "lastCharIndex", "length", "nextChar"], "sources": ["../../source/findNumbers/isValidCandidate.js"], "sourcesContent": ["// Copy-pasted from `PhoneNumberMatcher.js`.\r\n\r\nimport { PLUS_CHARS } from '../constants.js'\r\nimport { limit } from './util.js'\r\n\r\nimport {\r\n\tisLatinLetter,\r\n\tisInvalidPunctuationSymbol\r\n} from './utf-8.js'\r\n\r\nconst OPENING_PARENS = '(\\\\[\\uFF08\\uFF3B'\r\nconst CLOSING_PARENS = ')\\\\]\\uFF09\\uFF3D'\r\nconst NON_PARENS = `[^${OPENING_PARENS}${CLOSING_PARENS}]`\r\n\r\nexport const LEAD_CLASS = `[${OPENING_PARENS}${PLUS_CHARS}]`\r\n\r\n// Punctuation that may be at the start of a phone number - brackets and plus signs.\r\nconst LEAD_CLASS_LEADING = new RegExp('^' + LEAD_CLASS)\r\n\r\n// Limit on the number of pairs of brackets in a phone number.\r\nconst BRACKET_PAIR_LIMIT = limit(0, 3)\r\n\r\n/**\r\n * Pattern to check that brackets match. Opening brackets should be closed within a phone number.\r\n * This also checks that there is something inside the brackets. Having no brackets at all is also\r\n * fine.\r\n *\r\n * An opening bracket at the beginning may not be closed, but subsequent ones should be.  It's\r\n * also possible that the leading bracket was dropped, so we shouldn't be surprised if we see a\r\n * closing bracket first. We limit the sets of brackets in a phone number to four.\r\n */\r\nconst MATCHING_BRACKETS_ENTIRE = new RegExp\r\n(\r\n\t'^'\r\n\t+ \"(?:[\" + OPENING_PARENS + \"])?\" + \"(?:\" + NON_PARENS + \"+\" + \"[\" + CLOSING_PARENS + \"])?\"\r\n\t+ NON_PARENS + \"+\"\r\n\t+ \"(?:[\" + OPENING_PARENS + \"]\" + NON_PARENS + \"+[\" + CLOSING_PARENS + \"])\" + BRACKET_PAIR_LIMIT\r\n\t+ NON_PARENS + \"*\"\r\n\t+ '$'\r\n)\r\n\r\n/**\r\n * Matches strings that look like publication pages. Example:\r\n * <pre>Computing Complete Answers to Queries in the Presence of Limited Access Patterns.\r\n * Chen Li. VLDB J. 12(3): 211-227 (2003).</pre>\r\n *\r\n * The string \"211-227 (2003)\" is not a telephone number.\r\n */\r\nconst PUB_PAGES = /\\d{1,5}-+\\d{1,5}\\s{0,4}\\(\\d{1,4}/\r\n\r\nexport default function isValidCandidate(candidate, offset, text, leniency)\r\n{\r\n\t// Check the candidate doesn't contain any formatting\r\n\t// which would indicate that it really isn't a phone number.\r\n\tif (!MATCHING_BRACKETS_ENTIRE.test(candidate) || PUB_PAGES.test(candidate)) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// If leniency is set to VALID or stricter, we also want to skip numbers that are surrounded\r\n\t// by Latin alphabetic characters, to skip cases like abc8005001234 or 8005001234def.\r\n\tif (leniency !== 'POSSIBLE')\r\n\t{\r\n\t\t// If the candidate is not at the start of the text,\r\n\t\t// and does not start with phone-number punctuation,\r\n\t\t// check the previous character.\r\n\t\tif (offset > 0 && !LEAD_CLASS_LEADING.test(candidate))\r\n\t\t{\r\n\t\t\tconst previousChar = text[offset - 1]\r\n\t\t\t// We return null if it is a latin letter or an invalid punctuation symbol.\r\n\t\t\tif (isInvalidPunctuationSymbol(previousChar) || isLatinLetter(previousChar)) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst lastCharIndex = offset + candidate.length\r\n\t\tif (lastCharIndex < text.length)\r\n\t\t{\r\n\t\t\tconst nextChar = text[lastCharIndex]\r\n\t\t\tif (isInvalidPunctuationSymbol(nextChar) || isLatinLetter(nextChar)) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn true\r\n}"], "mappings": "AAAA;AAEA,SAASA,UAAT,QAA2B,iBAA3B;AACA,SAASC,KAAT,QAAsB,WAAtB;AAEA,SACCC,aADD,EAECC,0BAFD,QAGO,YAHP;AAKA,IAAMC,cAAc,GAAG,kBAAvB;AACA,IAAMC,cAAc,GAAG,kBAAvB;AACA,IAAMC,UAAU,eAAQF,cAAR,SAAyBC,cAAzB,MAAhB;AAEA,OAAO,IAAME,UAAU,cAAOH,cAAP,SAAwBJ,UAAxB,MAAhB,C,CAEP;;AACA,IAAMQ,kBAAkB,GAAG,IAAIC,MAAJ,CAAW,MAAMF,UAAjB,CAA3B,C,CAEA;;AACA,IAAMG,kBAAkB,GAAGT,KAAK,CAAC,CAAD,EAAI,CAAJ,CAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMU,wBAAwB,GAAG,IAAIF,MAAJ,CAEhC,MACE,MADF,GACWL,cADX,GAC4B,KAD5B,GACoC,KADpC,GAC4CE,UAD5C,GACyD,GADzD,GAC+D,GAD/D,GACqED,cADrE,GACsF,KADtF,GAEEC,UAFF,GAEe,GAFf,GAGE,MAHF,GAGWF,cAHX,GAG4B,GAH5B,GAGkCE,UAHlC,GAG+C,IAH/C,GAGsDD,cAHtD,GAGuE,IAHvE,GAG8EK,kBAH9E,GAIEJ,UAJF,GAIe,GAJf,GAKE,GAP8B,CAAjC;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMM,SAAS,GAAG,kCAAlB;AAEA,eAAe,SAASC,gBAAT,CAA0BC,SAA1B,EAAqCC,MAArC,EAA6CC,IAA7C,EAAmDC,QAAnD,EACf;EACC;EACA;EACA,IAAI,CAACN,wBAAwB,CAACO,IAAzB,CAA8BJ,SAA9B,CAAD,IAA6CF,SAAS,CAACM,IAAV,CAAeJ,SAAf,CAAjD,EAA4E;IAC3E;EACA,CALF,CAOC;EACA;;;EACA,IAAIG,QAAQ,KAAK,UAAjB,EACA;IACC;IACA;IACA;IACA,IAAIF,MAAM,GAAG,CAAT,IAAc,CAACP,kBAAkB,CAACU,IAAnB,CAAwBJ,SAAxB,CAAnB,EACA;MACC,IAAMK,YAAY,GAAGH,IAAI,CAACD,MAAM,GAAG,CAAV,CAAzB,CADD,CAEC;;MACA,IAAIZ,0BAA0B,CAACgB,YAAD,CAA1B,IAA4CjB,aAAa,CAACiB,YAAD,CAA7D,EAA6E;QAC5E,OAAO,KAAP;MACA;IACD;;IAED,IAAMC,aAAa,GAAGL,MAAM,GAAGD,SAAS,CAACO,MAAzC;;IACA,IAAID,aAAa,GAAGJ,IAAI,CAACK,MAAzB,EACA;MACC,IAAMC,QAAQ,GAAGN,IAAI,CAACI,aAAD,CAArB;;MACA,IAAIjB,0BAA0B,CAACmB,QAAD,CAA1B,IAAwCpB,aAAa,CAACoB,QAAD,CAAzD,EAAqE;QACpE,OAAO,KAAP;MACA;IACD;EACD;;EAED,OAAO,IAAP;AACA"}