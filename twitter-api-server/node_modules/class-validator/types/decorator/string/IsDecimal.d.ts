import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_DECIMAL = "isDecimal";
/**
 * Checks if the string is a valid decimal.
 * If given value is not a string, then it returns false.
 */
export declare function isDecimal(value: unknown, options?: ValidatorJS.IsDecimalOptions): boolean;
/**
 * Checks if the string is a valid decimal.
 * If given value is not a string, then it returns false.
 */
export declare function IsDecimal(options?: ValidatorJS.IsDecimalOptions, validationOptions?: ValidationOptions): PropertyDecorator;
