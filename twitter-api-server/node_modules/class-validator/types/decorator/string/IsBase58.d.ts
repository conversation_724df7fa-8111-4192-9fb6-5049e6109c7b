import { ValidationOptions } from '../ValidationOptions';
export declare const IS_BASE58 = "isBase58";
/**
 * Checks if a string is base58 encoded.
 * If given value is not a string, then it returns false.
 */
export declare function isBase58(value: unknown): boolean;
/**
 * Checks if a string is base58 encoded.
 * If given value is not a string, then it returns false.
 */
export declare function IsBase58(validationOptions?: ValidationOptions): PropertyDecorator;
