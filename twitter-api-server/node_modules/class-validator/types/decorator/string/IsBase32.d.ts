import { ValidationOptions } from '../ValidationOptions';
export declare const IS_BASE32 = "isBase32";
/**
 * Checks if a string is base32 encoded.
 * If given value is not a string, then it returns false.
 */
export declare function isBase32(value: unknown): boolean;
/**
 * Check if a string is base32 encoded.
 * If given value is not a string, then it returns false.
 */
export declare function IsBase32(validationOptions?: ValidationOptions): PropertyDecorator;
