#!/usr/bin/env node

/**
 * Complete OAuth flow with authorization code and save tokens to database
 * Usage: node complete-oauth.js <authorization_code>
 */

const { TwitterApi } = require('twitter-api-v2');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function completeOAuth(authCode) {
  console.log('🔐 Completing OAuth flow...\n');

  // Check environment variables
  const requiredEnvVars = [
    'TWITTER_CLIENT_ID',
    'TWITTER_CLIENT_SECRET',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.error(`❌ Missing environment variable: ${envVar}`);
      process.exit(1);
    }
  }

  // Create Supabase client
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  try {
    // Create Twitter OAuth2 client
    const client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID,
      clientSecret: process.env.TWITTER_CLIENT_SECRET,
    });

    console.log('🔄 Exchanging authorization code for tokens...');

    // Note: You'll need the codeVerifier and state from the original auth flow
    // For now, let's try a simplified approach
    const callbackUrl = 'http://localhost:3001/callback';
    
    // Extract the authorization code from the URL you provided
    console.log(`📝 Using authorization code: ${authCode.substring(0, 20)}...`);

    // This is a simplified version - in a real app, you'd store codeVerifier and state
    // For testing, let's try to use the code directly
    try {
      // Since we don't have the original codeVerifier, we need to start a new OAuth flow
      console.log('⚠️  Note: We need the original codeVerifier from the OAuth flow.');
      console.log('🔄 Starting a new OAuth flow instead...');

      const { url, codeVerifier, state } = client.generateOAuth2AuthLink(callbackUrl, {
        scope: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
      });

      console.log('\n🌐 New OAuth URL:');
      console.log(url);
      console.log('\n📋 Instructions:');
      console.log('1. Open the URL above in your browser');
      console.log('2. Authorize the application');
      console.log('3. Copy the "code" parameter from the callback URL');
      console.log('4. Run: node complete-oauth-with-verifier.js <code> <codeVerifier> <state>');
      console.log(`\nCodeVerifier for this session: ${codeVerifier}`);
      console.log(`State for this session: ${state}`);

    } catch (error) {
      console.error('❌ OAuth exchange failed:', error.message);
      
      if (error.message.includes('invalid_grant')) {
        console.log('\n💡 The authorization code may be expired or invalid.');
        console.log('   Please generate a new one using the OAuth flow.');
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Alternative function to complete OAuth with all required parameters
async function completeOAuthWithVerifier(authCode, codeVerifier, state) {
  console.log('🔐 Completing OAuth flow with verifier...\n');

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  try {
    const client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID,
      clientSecret: process.env.TWITTER_CLIENT_SECRET,
    });

    const callbackUrl = 'http://localhost:3001/callback';

    console.log('🔄 Exchanging authorization code for tokens...');
    const { client: userClient, accessToken, refreshToken } = await client.loginWithOAuth2({
      code: authCode,
      codeVerifier: codeVerifier,
      redirectUri: callbackUrl,
    });

    console.log('✅ OAuth successful!');
    console.log(`🔑 Access Token: ${accessToken.substring(0, 20)}...`);
    console.log(`🔄 Refresh Token: ${refreshToken.substring(0, 20)}...`);

    // Test the tokens by getting user info
    console.log('\n🔍 Testing authentication...');
    const user = await userClient.v2.me();
    console.log(`👤 Authenticated as: @${user.data.username} (${user.data.name})`);

    // Save tokens to database
    console.log('\n💾 Saving tokens to database...');
    const { error } = await supabase
      .from('twitter_tokens')
      .insert({
        access_token: accessToken,
        refresh_token: refreshToken,
        token_type: 'bearer',
        scope: 'tweet.read tweet.write users.read offline.access',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('❌ Failed to save tokens to database:', error.message);
      console.log('\n⚠️  Tokens are valid but not saved. You can save them manually:');
      console.log(`Access Token: ${accessToken}`);
      console.log(`Refresh Token: ${refreshToken}`);
    } else {
      console.log('✅ Tokens saved to database successfully!');
      console.log('\n🎉 Setup complete! You can now use the Twitter API.');
    }

  } catch (error) {
    console.error('❌ OAuth failed:', error.message);
    process.exit(1);
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.error('❌ Please provide the authorization code');
    console.log('Usage: node complete-oauth.js <authorization_code>');
    console.log('   or: node complete-oauth.js <code> <codeVerifier> <state>');
    process.exit(1);
  }

  if (args.length === 1) {
    await completeOAuth(args[0]);
  } else if (args.length === 3) {
    await completeOAuthWithVerifier(args[0], args[1], args[2]);
  } else {
    console.error('❌ Invalid number of arguments');
    console.log('Usage: node complete-oauth.js <authorization_code>');
    console.log('   or: node complete-oauth.js <code> <codeVerifier> <state>');
    process.exit(1);
  }
}

main().catch(console.error);
