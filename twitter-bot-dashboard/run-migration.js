#!/usr/bin/env node

/**
 * <PERSON>ript to run the Twitter tokens table migration
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function runMigration() {
  console.log('🚀 Running Twitter tokens table migration...\n');

  // Check environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing required environment variables:');
    if (!supabaseUrl) console.error('   - NEXT_PUBLIC_SUPABASE_URL');
    if (!supabaseServiceKey) console.error('   - SUPABASE_SERVICE_ROLE_KEY');
    console.error('\nPlease check your .env file.');
    process.exit(1);
  }

  // Create Supabase admin client
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'migrations', 'create_twitter_tokens_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📄 Migration SQL:');
    console.log('─'.repeat(50));
    console.log(migrationSQL);
    console.log('─'.repeat(50));
    console.log('');

    // Execute the migration
    console.log('⚡ Executing migration...');
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

    if (error) {
      // Try alternative method if rpc doesn't work
      console.log('🔄 Trying alternative execution method...');
      
      // Split SQL into individual statements
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          console.log(`   Executing: ${statement.substring(0, 50)}...`);
          const { error: stmtError } = await supabase.from('_').select('*').limit(0); // This won't work, but let's try a different approach
          
          // Since we can't execute raw SQL easily, let's provide instructions
          console.log('⚠️  Cannot execute raw SQL through Supabase client.');
          console.log('📋 Please run this migration manually in your Supabase SQL editor:');
          console.log('');
          console.log('1. Go to your Supabase dashboard');
          console.log('2. Navigate to SQL Editor');
          console.log('3. Copy and paste the migration SQL above');
          console.log('4. Click "Run"');
          console.log('');
          return;
        }
      }
    } else {
      console.log('✅ Migration executed successfully!');
    }

    // Verify the table was created
    console.log('🔍 Verifying table creation...');
    const { data: tableData, error: tableError } = await supabase
      .from('twitter_tokens')
      .select('*')
      .limit(1);

    if (tableError && tableError.code === '42P01') {
      console.log('⚠️  Table not found. Please run the migration manually.');
    } else if (tableError) {
      console.log('⚠️  Error verifying table:', tableError.message);
    } else {
      console.log('✅ Table verified successfully!');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Instructions for manual migration
function showManualInstructions() {
  console.log('\n📋 Manual Migration Instructions:');
  console.log('================================');
  console.log('');
  console.log('1. Open your Supabase dashboard');
  console.log('2. Go to SQL Editor');
  console.log('3. Copy the SQL from migrations/create_twitter_tokens_table.sql');
  console.log('4. Paste it into the SQL editor');
  console.log('5. Click "Run" to execute the migration');
  console.log('');
  console.log('The SQL file is located at:');
  console.log('   twitter-bot-dashboard/migrations/create_twitter_tokens_table.sql');
  console.log('');
}

// Run the migration
runMigration()
  .then(() => {
    showManualInstructions();
    console.log('🎉 Migration process completed!');
  })
  .catch((error) => {
    console.error('💥 Unexpected error:', error);
    showManualInstructions();
  });
