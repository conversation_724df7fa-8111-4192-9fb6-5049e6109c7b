#!/usr/bin/env node

/**
 * Test script for the new Twitter post API endpoint with database token management
 * Usage: node test-tweet-api.js "Your tweet content here"
 */

const http = require('http');

const API_BASE_URL = 'http://localhost:3000';
const ENDPOINT = '/api/twitter/post';

async function testTweetAPI(content) {
  const postData = JSON.stringify({ content });
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: ENDPOINT,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = require('http').request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: response
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

async function main() {
  const content = process.argv[2];
  
  if (!content) {
    console.error('❌ Please provide tweet content as an argument');
    console.log('Usage: node test-tweet-api.js "Your tweet content here"');
    process.exit(1);
  }

  console.log('🚀 Testing Twitter Post API...');
  console.log(`📝 Content: "${content}"`);
  console.log(`📏 Length: ${content.length} characters`);
  console.log('');

  try {
    const response = await testTweetAPI(content);
    
    console.log(`📊 Status: ${response.status}`);
    console.log('📋 Response:');
    console.log(JSON.stringify(response.body, null, 2));
    
    if (response.status === 200 && response.body.success) {
      console.log('');
      console.log('✅ Tweet posted successfully!');
      if (response.body.tweet?.url) {
        console.log(`🔗 Tweet URL: ${response.body.tweet.url}`);
      }
    } else {
      console.log('');
      console.log('❌ Tweet posting failed');
      if (response.body.error) {
        console.log(`💬 Error: ${response.body.error}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    process.exit(1);
  }
}

// Run the test
main().catch(console.error);
