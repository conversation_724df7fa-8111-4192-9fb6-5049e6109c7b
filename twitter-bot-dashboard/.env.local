# Twitter API Configuration
TWITTER_CLIENT_ID="ZlUzVW13MHBBZXdHUktpUFVudFg6MTpjaQ"
TWITTER_CLIENT_SECRET="XRWVbuUajUVsekcdFj5pRq0gYDH_tFB-NyGY5kXZ_XntiGWga6"
TWITTER_ACCESS_TOKEN="1938912974934040576-3l4c2XRoc0f1HXxuYYgy4v3hAdg9wt"
TWITTER_REFRESH_TOKEN=your_twitter_refresh_token

# Twitter API v1.1 (for additional features)
TWITTER_APP_KEY="*************************"
TWITTER_APP_SECRET="QbGO7SBMHC9NIE0uHUDK4jhCE0w0dZfmEluaWYdhdjlKZKzBva"
TWITTER_ACCESS_SECRET="8Ahg7dtiMM0584TRB22FC9vi18pBSWOI913rCjM0k1gDk"
TWITTER_BEARER_TOKEN="AAAAAAAAAAAAAAAAAAAAAD472wEAAAAA3YHHz6LJkBpaLYXAn95Lxd6XsVw%3Dl6sl4sNoOYorH5WzWQzCNbE8KxntCxKJoQhHrajmuixAqRYC6D"
# OpenAI Configuration
OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://fmhujzbqfzyyffgzwtzb.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30"
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# App Configuration
MAX_TOPICS_TO_SELECT=4
POSTING_INTERVAL_MINUTES=30

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001/api
