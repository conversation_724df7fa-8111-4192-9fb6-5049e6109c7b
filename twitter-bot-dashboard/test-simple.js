#!/usr/bin/env node

/**
 * Simple test using the database directly to verify token management
 */

const { TwitterApi } = require('twitter-api-v2');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testSimple() {
  console.log('🧪 Simple Twitter API Test...\n');

  // Create Supabase client
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  try {
    console.log('1️⃣ Fetching tokens from database...');
    const { data: tokens, error } = await supabase
      .from('twitter_tokens')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.error('   ❌ Failed to fetch tokens:', error.message);
      return;
    }

    console.log('   ✅ Tokens found in database');
    console.log(`   📅 Last updated: ${tokens.updated_at}`);

    console.log('\n2️⃣ Creating OAuth2 client...');
    const client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID,
      clientSecret: process.env.TWITTER_CLIENT_SECRET,
    });

    console.log('\n3️⃣ Refreshing tokens...');
    const { client: rwClient, accessToken, refreshToken } = await client.refreshOAuth2Token(
      tokens.refresh_token
    );

    console.log('   ✅ Token refresh successful!');
    console.log(`   🔑 New access token: ${accessToken.substring(0, 20)}...`);
    console.log(`   🔄 New refresh token: ${refreshToken.substring(0, 20)}...`);

    console.log('\n4️⃣ Updating tokens in database...');
    const { error: updateError } = await supabase
      .from('twitter_tokens')
      .update({
        access_token: accessToken,
        refresh_token: refreshToken,
        updated_at: new Date().toISOString()
      })
      .eq('id', tokens.id);

    if (updateError) {
      console.error('   ❌ Failed to update tokens:', updateError.message);
    } else {
      console.log('   ✅ Tokens updated in database');
    }

    console.log('\n5️⃣ Testing authentication...');
    const user = await rwClient.v2.me();
    console.log(`   ✅ Authenticated as: @${user.data.username} (${user.data.name})`);

    console.log('\n6️⃣ Testing tweet posting...');
    const testContent = `🧪 Simple API test - ${new Date().toISOString().slice(0, 19)}`;
    console.log(`   📝 Content: "${testContent}"`);
    console.log(`   📏 Length: ${testContent.length} characters`);

    // Ask user if they want to actually post
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const shouldPost = await new Promise((resolve) => {
      rl.question('\n❓ Do you want to actually post this test tweet? (y/N): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });

    if (shouldPost) {
      console.log('\n🚀 Posting tweet...');
      const tweet = await rwClient.v2.tweet(testContent);
      
      console.log('   ✅ Tweet posted successfully!');
      console.log(`   🆔 Tweet ID: ${tweet.data.id}`);
      console.log(`   🔗 Tweet URL: https://twitter.com/i/web/status/${tweet.data.id}`);
      console.log(`   📝 Content: "${tweet.data.text}"`);

      console.log('\n7️⃣ Saving tweet to database...');
      const { error: saveError } = await supabase
        .from('posted_tweets')
        .insert({
          tweet_id: tweet.data.id,
          content: testContent,
          impressions: 0,
          retweets: 0,
          likes: 0,
          replies: 0,
          posted_at: new Date().toISOString()
        });

      if (saveError) {
        console.error('   ❌ Failed to save tweet:', saveError.message);
      } else {
        console.log('   ✅ Tweet saved to database');
      }

    } else {
      console.log('\n⏭️  Tweet posting skipped');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Database token retrieval working');
    console.log('   ✅ Token refresh working');
    console.log('   ✅ Token storage working');
    console.log('   ✅ Authentication successful');
    if (shouldPost) {
      console.log('   ✅ Tweet posting working');
      console.log('   ✅ Database integration working');
    }
    console.log('\n🚀 Your Twitter API implementation is fully functional!');
    console.log('\n💡 The circular dependency issue has been resolved.');
    console.log('   The API should now work correctly in your Next.js app.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('\n🔍 Error details:', error);
    
    if (error.message.includes('invalid_grant')) {
      console.log('\n💡 The refresh token is invalid or expired.');
      console.log('   Please run the OAuth flow again.');
    } else if (error.code === 'PGRST116') {
      console.log('\n💡 No tokens found in database.');
      console.log('   Please complete the OAuth setup first.');
    }
  }
}

testSimple().catch(console.error);
