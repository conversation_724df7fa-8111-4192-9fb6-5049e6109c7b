-- Create twitter_tokens table for storing OAuth2 tokens
CREATE TABLE IF NOT EXISTS twitter_tokens (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  access_token TEXT NOT NULL,
  refresh_token TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE,
  token_type VARCHAR(50) DEFAULT 'bearer',
  scope TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_twitter_tokens_created_at ON twitter_tokens(created_at DESC);

-- Add RLS (Row Level Security) policy if needed
-- ALTER TABLE twitter_tokens ENABLE ROW LEVEL SECURITY;

-- Add comment to table
COMMENT ON TABLE twitter_tokens IS 'Stores Twitter OAuth2 access and refresh tokens';
COMMENT ON COLUMN twitter_tokens.access_token IS 'Twitter OAuth2 access token (short-lived)';
COMMENT ON COLUMN twitter_tokens.refresh_token IS 'Twitter OAuth2 refresh token (single-use, gets new one on refresh)';
COMMENT ON COLUMN twitter_tokens.expires_at IS 'When the access token expires';
COMMENT ON COLUMN twitter_tokens.token_type IS 'Type of token (usually bearer)';
COMMENT ON COLUMN twitter_tokens.scope IS 'OAuth2 scopes granted to the token';
