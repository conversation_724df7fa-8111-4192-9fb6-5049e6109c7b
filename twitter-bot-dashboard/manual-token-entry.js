#!/usr/bin/env node

/**
 * Manual token entry script for when you have tokens but need to save them to database
 * This is a workaround for the OAuth flow completion
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');
require('dotenv').config();

async function manualTokenEntry() {
  console.log('🔑 Manual Token Entry\n');
  console.log('This script helps you manually enter Twitter tokens into the database.');
  console.log('You can get these tokens by running the auth.js server and completing OAuth.\n');

  // Check environment variables
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
  }

  // Create Supabase client
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  try {
    console.log('📋 Please provide the following information:\n');

    const accessToken = await askQuestion(rl, '🔑 Access Token: ');
    const refreshToken = await askQuestion(rl, '🔄 Refresh Token: ');

    console.log('\n💾 Saving tokens to database...');

    // Save tokens to database
    const { error } = await supabase
      .from('twitter_tokens')
      .insert({
        access_token: accessToken,
        refresh_token: refreshToken,
        token_type: 'bearer',
        scope: 'tweet.read tweet.write users.read offline.access',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('❌ Failed to save tokens:', error.message);
      
      if (error.code === '42P01') {
        console.log('\n💡 The twitter_tokens table does not exist.');
        console.log('   Please run: node run-migration.js');
      }
    } else {
      console.log('✅ Tokens saved successfully!');
      console.log('\n🧪 Testing the tokens...');
      
      // Test the setup
      await testTokens();
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

async function testTokens() {
  try {
    // Import the twitter auth manager
    const { twitterAuth } = require('./src/lib/twitter-auth');
    
    console.log('🔄 Testing token refresh...');
    const client = await twitterAuth.getAuthenticatedClient();
    
    console.log('🔍 Getting user information...');
    const user = await client.v2.me();
    
    console.log(`✅ Success! Authenticated as: @${user.data.username} (${user.data.name})`);
    console.log('\n🎉 Your Twitter API is ready to use!');
    
  } catch (error) {
    console.error('❌ Token test failed:', error.message);
    console.log('\n💡 The tokens were saved but there might be an issue with the setup.');
    console.log('   Try running: node setup-and-test.js');
  }
}

function askQuestion(rl, question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Alternative: Quick setup with known tokens (for testing)
async function quickSetup() {
  console.log('🚀 Quick Setup Mode\n');
  console.log('This will run a fresh OAuth flow and save tokens automatically.\n');

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const runOAuth = await askQuestion(rl, '❓ Do you want to run a fresh OAuth flow? (y/N): ');
  rl.close();

  if (runOAuth.toLowerCase() === 'y' || runOAuth.toLowerCase() === 'yes') {
    console.log('\n🔄 Starting OAuth server...');
    console.log('📋 Instructions:');
    console.log('1. The auth server will start on port 3001');
    console.log('2. Open the OAuth URL in your browser');
    console.log('3. Authorize the app');
    console.log('4. The tokens will be automatically saved to the database');
    console.log('\n🚀 Starting server...\n');

    // Import and run the auth server
    const { spawn } = require('child_process');
    const authProcess = spawn('node', ['../auth.js'], {
      stdio: 'inherit',
      cwd: __dirname
    });

    authProcess.on('error', (error) => {
      console.error('❌ Failed to start auth server:', error.message);
    });

  } else {
    console.log('⏭️  OAuth setup skipped.');
    console.log('💡 You can manually enter tokens using: node manual-token-entry.js');
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--quick') || args.includes('-q')) {
    await quickSetup();
  } else {
    await manualTokenEntry();
  }
}

main().catch(console.error);
