#!/usr/bin/env node

/**
 * Standalone test for Twitter API functionality with database token management
 * This tests the new token management system
 */

const { TwitterA<PERSON> } = require('twitter-api-v2');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testTwitterApiLogic() {
  console.log('🧪 Testing Twitter API Logic...\n');

  // Test 1: Environment Variables
  console.log('1️⃣ Checking Environment Variables:');
  const requiredEnvVars = [
    'TWITTER_CLIENT_ID',
    'TWITTER_CLIENT_SECRET', 
    'TWITTER_REFRESH_TOKEN'
  ];

  let envVarsOk = true;
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (value) {
      console.log(`   ✅ ${envVar}: ${value.substring(0, 10)}...`);
    } else {
      console.log(`   ❌ ${envVar}: Missing`);
      envVarsOk = false;
    }
  }

  if (!envVarsOk) {
    console.log('\n❌ Missing required environment variables. Please check your .env file.');
    return false;
  }

  // Test 2: Twitter Client Initialization
  console.log('\n2️⃣ Testing Twitter Client Initialization:');
  try {
    const client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID,
      clientSecret: process.env.TWITTER_CLIENT_SECRET,
    });
    console.log('   ✅ Twitter client initialized successfully');
  } catch (error) {
    console.log('   ❌ Failed to initialize Twitter client:', error.message);
    return false;
  }

  // Test 3: Token Refresh (without actually posting)
  console.log('\n3️⃣ Testing Token Refresh:');
  try {
    const client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID,
      clientSecret: process.env.TWITTER_CLIENT_SECRET,
    });

    // This will test if the refresh token is valid
    const { client: rwClient, accessToken } = await client.refreshOAuth2Token(
      process.env.TWITTER_REFRESH_TOKEN
    );
    
    console.log('   ✅ Token refresh successful');
    console.log(`   📝 New access token: ${accessToken.substring(0, 20)}...`);
    
    // Test 4: Get current user (to verify authentication)
    console.log('\n4️⃣ Testing Authentication:');
    try {
      const user = await rwClient.v2.me();
      console.log('   ✅ Authentication successful');
      console.log(`   👤 User: @${user.data.username} (${user.data.name})`);
      console.log(`   🆔 User ID: ${user.data.id}`);
      
      return true;
    } catch (authError) {
      console.log('   ❌ Authentication failed:', authError.message);
      return false;
    }
    
  } catch (error) {
    console.log('   ❌ Token refresh failed:', error.message);
    
    // Provide helpful error messages
    if (error.message.includes('invalid_grant')) {
      console.log('   💡 Hint: Your refresh token may have expired. Please re-authenticate using auth.js');
    } else if (error.message.includes('invalid_client')) {
      console.log('   💡 Hint: Check your TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET');
    }
    
    return false;
  }
}

async function testTweetPosting() {
  console.log('\n5️⃣ Testing Tweet Posting (DRY RUN):');
  
  const testContent = `Test tweet from API - ${new Date().toISOString()}`;
  console.log(`   📝 Test content: "${testContent}"`);
  console.log(`   📏 Length: ${testContent.length} characters`);
  
  // Validate content
  if (testContent.length > 280) {
    console.log('   ❌ Content exceeds 280 character limit');
    return false;
  }
  
  console.log('   ✅ Content validation passed');
  
  // Ask user if they want to actually post
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    rl.question('\n❓ Do you want to actually post this test tweet? (y/N): ', async (answer) => {
      rl.close();
      
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        console.log('\n🚀 Posting test tweet...');
        
        try {
          const client = new TwitterApi({
            clientId: process.env.TWITTER_CLIENT_ID,
            clientSecret: process.env.TWITTER_CLIENT_SECRET,
          });
          
          const { client: rwClient } = await client.refreshOAuth2Token(
            process.env.TWITTER_REFRESH_TOKEN
          );
          
          const tweet = await rwClient.v2.tweet(testContent);
          
          console.log('   ✅ Tweet posted successfully!');
          console.log(`   🆔 Tweet ID: ${tweet.data.id}`);
          console.log(`   🔗 Tweet URL: https://twitter.com/i/web/status/${tweet.data.id}`);
          console.log(`   📝 Content: "${tweet.data.text}"`);
          
          resolve(true);
        } catch (error) {
          console.log('   ❌ Failed to post tweet:', error.message);
          resolve(false);
        }
      } else {
        console.log('   ⏭️  Skipping actual tweet posting');
        resolve(true);
      }
    });
  });
}

async function main() {
  console.log('🐦 Twitter API Test Suite');
  console.log('========================\n');
  
  try {
    const basicTestsPassed = await testTwitterApiLogic();
    
    if (basicTestsPassed) {
      console.log('\n✅ All basic tests passed!');
      
      const postTestPassed = await testTweetPosting();
      
      if (postTestPassed) {
        console.log('\n🎉 All tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log('   ✅ Environment variables configured');
        console.log('   ✅ Twitter client initialization works');
        console.log('   ✅ Token refresh works');
        console.log('   ✅ Authentication successful');
        console.log('   ✅ Tweet posting logic validated');
        console.log('\n🚀 Your API should work correctly!');
      } else {
        console.log('\n⚠️  Basic tests passed but tweet posting had issues');
      }
    } else {
      console.log('\n❌ Basic tests failed. Please fix the issues above before testing the API.');
    }
    
  } catch (error) {
    console.error('\n💥 Unexpected error:', error);
  }
}

// Run the test suite
main().catch(console.error);
