{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/css/messages.ts"], "sourcesContent": ["import { bold, cyan } from '../../../../../lib/picocolors'\n\nexport function getGlobalImportError() {\n  return `Global CSS ${bold(\n    'cannot'\n  )} be imported from files other than your ${bold(\n    'Custom <App>'\n  )}. Due to the Global nature of stylesheets, and to avoid conflicts, Please move all first-party global CSS imports to ${cyan(\n    'pages/_app.js'\n  )}. Or convert the import to Component-Level CSS (CSS Modules).\\nRead more: https://nextjs.org/docs/messages/css-global`\n}\n\nexport function getGlobalModuleImportError() {\n  return `Global CSS ${bold('cannot')} be imported from within ${bold(\n    'node_modules'\n  )}.\\nRead more: https://nextjs.org/docs/messages/css-npm`\n}\n\nexport function getLocalModuleImportError() {\n  return `CSS Modules ${bold('cannot')} be imported from within ${bold(\n    'node_modules'\n  )}.\\nRead more: https://nextjs.org/docs/messages/css-modules-npm`\n}\n\nexport function getCustomDocumentError() {\n  return `CSS ${bold('cannot')} be imported within ${cyan(\n    'pages/_document.js'\n  )}. Please move global styles to ${cyan('pages/_app.js')}.`\n}\n"], "names": ["getCustomDocumentError", "getGlobalImportError", "getGlobalModuleImportError", "getLocalModuleImportError", "bold", "cyan"], "mappings": ";;;;;;;;;;;;;;;;;IAwBgBA,sBAAsB;eAAtBA;;IAtBAC,oBAAoB;eAApBA;;IAUAC,0BAA0B;eAA1BA;;IAMAC,yBAAyB;eAAzBA;;;4BAlBW;AAEpB,SAASF;IACd,OAAO,CAAC,WAAW,EAAEG,IAAAA,gBAAI,EACvB,UACA,wCAAwC,EAAEA,IAAAA,gBAAI,EAC9C,gBACA,qHAAqH,EAAEC,IAAAA,gBAAI,EAC3H,iBACA,qHAAqH,CAAC;AAC1H;AAEO,SAASH;IACd,OAAO,CAAC,WAAW,EAAEE,IAAAA,gBAAI,EAAC,UAAU,yBAAyB,EAAEA,IAAAA,gBAAI,EACjE,gBACA,sDAAsD,CAAC;AAC3D;AAEO,SAASD;IACd,OAAO,CAAC,YAAY,EAAEC,IAAAA,gBAAI,EAAC,UAAU,yBAAyB,EAAEA,IAAAA,gBAAI,EAClE,gBACA,8DAA8D,CAAC;AACnE;AAEO,SAASJ;IACd,OAAO,CAAC,IAAI,EAAEI,IAAAA,gBAAI,EAAC,UAAU,oBAAoB,EAAEC,IAAAA,gBAAI,EACrD,sBACA,+BAA+B,EAAEA,IAAAA,gBAAI,EAAC,iBAAiB,CAAC,CAAC;AAC7D"}