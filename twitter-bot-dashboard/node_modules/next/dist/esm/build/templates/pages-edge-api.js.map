{"version": 3, "sources": ["../../../src/build/templates/pages-edge-api.ts"], "sourcesContent": ["import type { AdapterOptions } from '../../server/web/adapter'\n\nimport '../../server/web/globals'\n\nimport { adapter } from '../../server/web/adapter'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { wrapApi<PERSON>andler } from '../../server/api-utils'\n\n// Import the userland code.\nimport handler from 'VAR_USERLAND'\n\nconst page = 'VAR_DEFINITION_PAGE'\n\nif (typeof handler !== 'function') {\n  throw new Error(\n    `The Edge Function \"pages${page}\" must export a \\`default\\` function`\n  )\n}\n\nexport default function (\n  opts: Omit<AdapterOptions, 'IncrementalCache' | 'page' | 'handler'>\n) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    page: 'VAR_DEFINITION_PATHNAME',\n    handler: wrapApi<PERSON>and<PERSON>(page, handler),\n  })\n}\n"], "names": ["adapter", "IncrementalCache", "wrapApiHandler", "handler", "page", "Error", "opts"], "mappings": "AAEA,OAAO,2BAA0B;AAEjC,SAASA,OAAO,QAAQ,2BAA0B;AAClD,SAASC,gBAAgB,QAAQ,qCAAoC;AACrE,SAASC,cAAc,QAAQ,yBAAwB;AAEvD,4BAA4B;AAC5B,OAAOC,aAAa,eAAc;AAElC,MAAMC,OAAO;AAEb,IAAI,OAAOD,YAAY,YAAY;IACjC,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,wBAAwB,EAAED,KAAK,oCAAoC,CAAC,GADjE,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,eAAe,SACbE,IAAmE;IAEnE,OAAON,QAAQ;QACb,GAAGM,IAAI;QACPL;QACAG,MAAM;QACND,SAASD,eAAeE,MAAMD;IAChC;AACF"}