{"version": 3, "sources": ["../../../src/build/static-paths/types.ts"], "sourcesContent": ["import type { FallbackMode } from '../../lib/fallback'\n\ntype StaticPrerenderedRoute = {\n  pathname: string\n  encodedPathname: string\n  fallbackRouteParams: undefined\n  fallbackMode: FallbackMode | undefined\n  fallbackRootParams: undefined\n}\n\ntype FallbackPrerenderedRoute = {\n  pathname: string\n  encodedPathname: string\n  fallbackRouteParams: readonly string[]\n  fallbackMode: FallbackMode | undefined\n  fallbackRootParams: readonly string[]\n}\n\nexport type PrerenderedRoute = StaticPrerenderedRoute | FallbackPrerenderedRoute\n\nexport type StaticPathsResult = {\n  fallbackMode: FallbackMode\n  prerenderedRoutes: PrerenderedRoute[]\n}\n"], "names": [], "mappings": "AAoBA,WAGC"}