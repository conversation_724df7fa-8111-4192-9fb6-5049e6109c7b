import { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher';
import { getRouteRegex } from '../../shared/lib/router/utils/route-regex';
// The last page and matcher that this function handled.
let last = null;
/**
 * Gets the params for the provided page.
 * @param page the page that contains dynamic path parameters
 * @param pathname the pathname to match
 * @returns the matches that were found, throws otherwise
 */ export function getParams(page, pathname) {
    // Because this is often called on the output of `getStaticPaths` or similar
    // where the `page` here doesn't change, this will "remember" the last page
    // it created the RegExp for. If it matches, it'll just re-use it.
    let matcher;
    if ((last == null ? void 0 : last.page) === page) {
        matcher = last.matcher;
    } else {
        matcher = getRouteMatcher(getRouteRegex(page));
    }
    const params = matcher(pathname);
    if (!params) {
        throw Object.defineProperty(new Error(`The provided export path '${pathname}' doesn't match the '${page}' page.\nRead more: https://nextjs.org/docs/messages/export-path-mismatch`), "__NEXT_ERROR_CODE", {
            value: "E20",
            enumerable: false,
            configurable: true
        });
    }
    return params;
}

//# sourceMappingURL=get-params.js.map