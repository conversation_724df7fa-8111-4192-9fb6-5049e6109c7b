{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware-turbopack.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport {\n  getOriginalCodeFrame,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n} from './shared'\nimport { middlewareResponse } from './middleware-response'\nimport fs, { constants as FS } from 'fs/promises'\nimport path from 'path'\nimport url from 'url'\nimport { launchEditor } from '../utils/launch-editor'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  SourceMapConsumer,\n  type BasicSourceMapConsumer,\n  type NullableMappedPosition,\n} from 'next/dist/compiled/source-map08'\nimport type { Project, TurbopackStackFrame } from '../../../../build/swc/types'\nimport { getSourceMapFromFile } from '../utils/get-source-map-from-file'\nimport { findSourceMap, type SourceMapPayload } from 'node:module'\nimport { pathToFileURL } from 'node:url'\nimport { inspect } from 'node:util'\n\nfunction shouldIgnorePath(modulePath: string): boolean {\n  return (\n    modulePath.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    modulePath.includes('next/dist') ||\n    modulePath.startsWith('node:')\n  )\n}\n\ntype IgnorableStackFrame = StackFrame & { ignored: boolean }\n\nconst currentSourcesByFile: Map<string, Promise<string | null>> = new Map()\nasync function batchedTraceSource(\n  project: Project,\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const file = frame.file\n    ? // TODO(veil): Why are the frames sent encoded?\n      decodeURIComponent(frame.file)\n    : undefined\n\n  if (!file) return\n\n  // For node internals they cannot traced the actual source code with project.traceSource,\n  // we need an early return to indicate it's ignored to avoid the unknown scheme error from `project.traceSource`.\n  if (file.startsWith('node:')) {\n    return {\n      frame: {\n        file,\n        lineNumber: frame.line ?? 0,\n        column: frame.column ?? 0,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: true,\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  const currentDirectoryFileUrl = pathToFileURL(process.cwd()).href\n\n  const sourceFrame = await project.traceSource(frame, currentDirectoryFileUrl)\n  if (!sourceFrame) {\n    return {\n      frame: {\n        file,\n        lineNumber: frame.line ?? 0,\n        column: frame.column ?? 0,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: shouldIgnorePath(file),\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  let source = null\n  const originalFile = sourceFrame.originalFile\n\n  // Don't look up source for node_modules or internals. These can often be large bundled files.\n  const ignored =\n    shouldIgnorePath(originalFile ?? sourceFrame.file) ||\n    // isInternal means resource starts with turbopack:///[turbopack]\n    !!sourceFrame.isInternal\n  if (originalFile && !ignored) {\n    let sourcePromise = currentSourcesByFile.get(originalFile)\n    if (!sourcePromise) {\n      sourcePromise = project.getSourceForAsset(originalFile)\n      currentSourcesByFile.set(originalFile, sourcePromise)\n      setTimeout(() => {\n        // Cache file reads for 100ms, as frames will often reference the same\n        // files and can be large.\n        currentSourcesByFile.delete(originalFile!)\n      }, 100)\n    }\n    source = await sourcePromise\n  }\n\n  // TODO: get ignoredList from turbopack source map\n  const ignorableFrame = {\n    file: sourceFrame.file,\n    lineNumber: sourceFrame.line ?? 0,\n    column: sourceFrame.column ?? 0,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      frame.methodName ?? '<unknown>',\n    ignored,\n    arguments: [],\n  }\n\n  return {\n    frame: ignorableFrame,\n    source,\n  }\n}\nfunction parseFile(fileParam: string | null): string | undefined {\n  if (!fileParam) {\n    return undefined\n  }\n\n  // rsc://React/Server/file://<filename>?42 => file://<filename>\n  return fileParam.replace(/^rsc:\\/\\/React\\/[^/]+\\//, '').replace(/\\?\\d+$/, '')\n}\n\nfunction createStackFrames(\n  body: OriginalStackFramesRequest\n): TurbopackStackFrame[] {\n  const { frames, isServer } = body\n\n  return frames\n    .map((frame): TurbopackStackFrame | undefined => {\n      const file = parseFile(frame.file)\n\n      if (!file) {\n        return undefined\n      }\n\n      return {\n        file,\n        methodName: frame.methodName ?? '<unknown>',\n        line: frame.lineNumber ?? 0,\n        column: frame.column ?? 0,\n        isServer,\n      } satisfies TurbopackStackFrame\n    })\n    .filter((f): f is TurbopackStackFrame => f !== undefined)\n}\n\nfunction createStackFrame(\n  searchParams: URLSearchParams\n): TurbopackStackFrame | undefined {\n  const file = parseFile(searchParams.get('file'))\n\n  if (!file) {\n    return undefined\n  }\n\n  return {\n    file,\n    methodName: searchParams.get('methodName') ?? '<unknown>',\n    line: parseInt(searchParams.get('lineNumber') ?? '0', 10) || 0,\n    column: parseInt(searchParams.get('column') ?? '0', 10) || 0,\n    isServer: searchParams.get('isServer') === 'true',\n  } satisfies TurbopackStackFrame\n}\n\n/**\n * https://tc39.es/source-map/#index-map\n */\ninterface IndexSourceMapSection {\n  offset: {\n    line: number\n    column: number\n  }\n  map: ModernRawSourceMap\n}\n\n// TODO(veil): Upstream types\ninterface IndexSourceMap {\n  version: number\n  file: string\n  sections: IndexSourceMapSection[]\n}\n\ninterface ModernRawSourceMap extends SourceMapPayload {\n  ignoreList?: number[]\n}\n\ntype ModernSourceMapPayload = ModernRawSourceMap | IndexSourceMap\n\n/**\n * Finds the sourcemap payload applicable to a given frame.\n * Equal to the input unless an Index Source Map is used.\n */\nfunction findApplicableSourceMapPayload(\n  frame: TurbopackStackFrame,\n  payload: ModernSourceMapPayload\n): ModernRawSourceMap | undefined {\n  if ('sections' in payload) {\n    const frameLine = frame.line ?? 0\n    const frameColumn = frame.column ?? 0\n    // Sections must not overlap and must be sorted: https://tc39.es/source-map/#section-object\n    // Therefore the last section that has an offset less than or equal to the frame is the applicable one.\n    // TODO(veil): Binary search\n    let section: IndexSourceMapSection | undefined = payload.sections[0]\n    for (\n      let i = 0;\n      i < payload.sections.length &&\n      payload.sections[i].offset.line <= frameLine &&\n      payload.sections[i].offset.column <= frameColumn;\n      i++\n    ) {\n      section = payload.sections[i]\n    }\n\n    return section === undefined ? undefined : section.map\n  } else {\n    return payload\n  }\n}\n\n/**\n * @returns 1-based lines and 0-based columns\n */\nasync function nativeTraceSource(\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const sourceURL = // TODO(veil): Why are the frames sent encoded?\n    decodeURIComponent(frame.file)\n  let sourceMapPayload: ModernSourceMapPayload | undefined\n  try {\n    sourceMapPayload = findSourceMap(sourceURL)?.payload\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (sourceMapPayload !== undefined) {\n    let consumer: BasicSourceMapConsumer\n    try {\n      consumer = await new SourceMapConsumer(sourceMapPayload)\n    } catch (cause) {\n      throw new Error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n        { cause }\n      )\n    }\n    let traced: {\n      originalPosition: NullableMappedPosition\n      sourceContent: string | null\n    } | null\n    try {\n      const originalPosition = consumer.originalPositionFor({\n        line: frame.line ?? 1,\n        // 0-based columns out requires 0-based columns in.\n        column: (frame.column ?? 1) - 1,\n      })\n\n      if (originalPosition.source === null) {\n        traced = null\n      } else {\n        const sourceContent: string | null =\n          consumer.sourceContentFor(\n            originalPosition.source,\n            /* returnNullOnMissing */ true\n          ) ?? null\n\n        traced = { originalPosition, sourceContent }\n      }\n    } finally {\n      consumer.destroy()\n    }\n\n    if (traced !== null) {\n      const { originalPosition, sourceContent } = traced\n      const applicableSourceMap = findApplicableSourceMapPayload(\n        frame,\n        sourceMapPayload\n      )\n\n      // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n      let ignored = false\n      if (applicableSourceMap === undefined) {\n        console.error(\n          'No applicable source map found in sections for frame',\n          frame\n        )\n      } else {\n        // TODO: O(n^2). Consider moving `ignoreList` into a Set\n        const sourceIndex = applicableSourceMap.sources.indexOf(\n          originalPosition.source!\n        )\n        ignored =\n          applicableSourceMap.ignoreList?.includes(sourceIndex) ??\n          // When sourcemap is not available, fallback to checking `frame.file`.\n          // e.g. In pages router, nextjs server code is not bundled into the page.\n          shouldIgnorePath(frame.file)\n      }\n\n      const originalStackFrame: IgnorableStackFrame = {\n        methodName:\n          // We ignore the sourcemapped name since it won't be the correct name.\n          // The callsite will point to the column of the variable name instead of the\n          // name of the enclosing function.\n          // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n          frame.methodName\n            ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n            ?.replace('__webpack_exports__.', '') || '<unknown>',\n        column: (originalPosition.column ?? 0) + 1,\n        file: originalPosition.source,\n        lineNumber: originalPosition.line ?? 0,\n        // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n        arguments: [],\n        ignored,\n      }\n\n      return {\n        frame: originalStackFrame,\n        source: sourceContent,\n      }\n    }\n  }\n\n  return undefined\n}\n\nasync function createOriginalStackFrame(\n  project: Project,\n  projectPath: string,\n  frame: TurbopackStackFrame\n): Promise<OriginalStackFrameResponse | null> {\n  const traced =\n    (await nativeTraceSource(frame)) ??\n    // TODO(veil): When would the bundler know more than native?\n    // If it's faster, try the bundler first and fall back to native later.\n    (await batchedTraceSource(project, frame))\n  if (!traced) {\n    return null\n  }\n\n  let normalizedStackFrameLocation = traced.frame.file\n  if (\n    normalizedStackFrameLocation !== null &&\n    normalizedStackFrameLocation.startsWith('file://')\n  ) {\n    normalizedStackFrameLocation = path.relative(\n      projectPath,\n      url.fileURLToPath(normalizedStackFrameLocation)\n    )\n  }\n\n  return {\n    originalStackFrame: {\n      arguments: traced.frame.arguments,\n      column: traced.frame.column,\n      file: normalizedStackFrameLocation,\n      ignored: traced.frame.ignored,\n      lineNumber: traced.frame.lineNumber,\n      methodName: traced.frame.methodName,\n    },\n    originalCodeFrame: getOriginalCodeFrame(traced.frame, traced.source),\n  }\n}\n\nexport function getOverlayMiddleware(project: Project, projectPath: string) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      const request = JSON.parse(body) as OriginalStackFramesRequest\n      const stackFrames = createStackFrames(request)\n      const result: OriginalStackFramesResponse = await Promise.all(\n        stackFrames.map(async (frame) => {\n          try {\n            const stackFrame = await createOriginalStackFrame(\n              project,\n              projectPath,\n              frame\n            )\n            if (stackFrame === null) {\n              return {\n                status: 'rejected',\n                reason: 'Failed to create original stack frame',\n              }\n            }\n            return { status: 'fulfilled', value: stackFrame }\n          } catch (error) {\n            return {\n              status: 'rejected',\n              reason: inspect(error, { colors: false }),\n            }\n          }\n        })\n      )\n\n      return middlewareResponse.json(res, result)\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const frame = createStackFrame(searchParams)\n\n      if (!frame) return middlewareResponse.badRequest(res)\n\n      const fileExists = await fs.access(frame.file, FS.F_OK).then(\n        () => true,\n        () => false\n      )\n      if (!fileExists) return middlewareResponse.notFound(res)\n\n      try {\n        launchEditor(frame.file, frame.line ?? 1, frame.column ?? 1)\n      } catch (err) {\n        console.log('Failed to launch editor:', err)\n        return middlewareResponse.internalServerError(res)\n      }\n\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(project: Project) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    let filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    // TODO(veil): Always try the native version first.\n    // Externals could also be files that aren't bundled via Webpack.\n    if (\n      filename.startsWith('webpack://') ||\n      filename.startsWith('webpack-internal:///')\n    ) {\n      const sourceMap = findSourceMap(filename)\n\n      if (sourceMap) {\n        return middlewareResponse.json(res, sourceMap.payload)\n      }\n\n      return middlewareResponse.noContent(res)\n    }\n\n    try {\n      // Turbopack chunk filenames might be URL-encoded.\n      filename = decodeURI(filename)\n\n      if (path.isAbsolute(filename)) {\n        filename = url.pathToFileURL(filename).href\n      }\n\n      const sourceMapString = await project.getSourceMap(filename)\n\n      if (sourceMapString) {\n        return middlewareResponse.jsonString(res, sourceMapString)\n      }\n\n      if (filename.startsWith('file:')) {\n        const sourceMap = await getSourceMapFromFile(filename)\n\n        if (sourceMap) {\n          return middlewareResponse.json(res, sourceMap)\n        }\n      }\n    } catch (error) {\n      console.error('Failed to get source map:', error)\n    }\n\n    middlewareResponse.noContent(res)\n  }\n}\n"], "names": ["getOverlayMiddleware", "getSourceMapMiddleware", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modulePath", "includes", "startsWith", "currentSourcesByFile", "Map", "batchedTraceSource", "project", "frame", "file", "decodeURIComponent", "undefined", "lineNumber", "line", "column", "methodName", "ignored", "arguments", "source", "currentDirectoryFileUrl", "pathToFileURL", "process", "cwd", "href", "sourceFrame", "traceSource", "originalFile", "isInternal", "sourcePromise", "get", "getSourceForAsset", "set", "setTimeout", "delete", "ignorableFrame", "parseFile", "fileParam", "replace", "createStackFrames", "body", "frames", "isServer", "map", "filter", "f", "createStackFrame", "searchParams", "parseInt", "findApplicableSourceMapPayload", "payload", "frameLine", "frameColumn", "section", "sections", "i", "length", "offset", "nativeTraceSource", "sourceURL", "sourceMapPayload", "findSourceMap", "cause", "Error", "consumer", "SourceMapConsumer", "traced", "originalPosition", "originalPositionFor", "sourceContent", "sourceContentFor", "destroy", "applicableSourceMap", "console", "error", "sourceIndex", "sources", "indexOf", "ignoreList", "originalStackFrame", "createOriginalStackFrame", "projectPath", "normalizedStackFrameLocation", "path", "relative", "url", "fileURLToPath", "originalCodeFrame", "getOriginalCodeFrame", "req", "res", "next", "pathname", "URL", "method", "middlewareResponse", "badRequest", "Promise", "resolve", "reject", "data", "on", "chunk", "request", "JSON", "parse", "stackFrames", "result", "all", "stackFrame", "status", "reason", "value", "inspect", "colors", "json", "fileExists", "fs", "access", "FS", "F_OK", "then", "notFound", "launchEditor", "err", "log", "internalServerError", "noContent", "filename", "sourceMap", "decodeURI", "isAbsolute", "sourceMapString", "getSourceMap", "jsonString", "getSourceMapFromFile"], "mappings": ";;;;;;;;;;;;;;;IAqXgBA,oBAAoB;eAApBA;;IA0EAC,sBAAsB;eAAtBA;;;;;wBAzbT;oCAC4B;oEACC;+DACnB;8DACD;8BACa;6BAMtB;sCAE8B;4BACgB;yBACvB;0BACN;AAExB,SAASC,iBAAiBC,UAAkB;IAC1C,OACEA,WAAWC,QAAQ,CAAC,mBACpB,2EAA2E;IAC3ED,WAAWC,QAAQ,CAAC,gBACpBD,WAAWE,UAAU,CAAC;AAE1B;AAIA,MAAMC,uBAA4D,IAAIC;AACtE,eAAeC,mBACbC,OAAgB,EAChBC,KAA0B;IAE1B,MAAMC,OAAOD,MAAMC,IAAI,GAEnBC,mBAAmBF,MAAMC,IAAI,IAC7BE;IAEJ,IAAI,CAACF,MAAM;IAEX,yFAAyF;IACzF,iHAAiH;IACjH,IAAIA,KAAKN,UAAU,CAAC,UAAU;YAIZK,aACJA,eACIA;QALhB,OAAO;YACLA,OAAO;gBACLC;gBACAG,YAAYJ,CAAAA,cAAAA,MAAMK,IAAI,YAAVL,cAAc;gBAC1BM,QAAQN,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;gBACxBO,YAAYP,CAAAA,oBAAAA,MAAMO,UAAU,YAAhBP,oBAAoB;gBAChCQ,SAAS;gBACTC,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,MAAMC,0BAA0BC,IAAAA,sBAAa,EAACC,QAAQC,GAAG,IAAIC,IAAI;IAEjE,MAAMC,cAAc,MAAMjB,QAAQkB,WAAW,CAACjB,OAAOW;IACrD,IAAI,CAACK,aAAa;YAIAhB,cACJA,gBACIA;QALhB,OAAO;YACLA,OAAO;gBACLC;gBACAG,YAAYJ,CAAAA,eAAAA,MAAMK,IAAI,YAAVL,eAAc;gBAC1BM,QAAQN,CAAAA,iBAAAA,MAAMM,MAAM,YAAZN,iBAAgB;gBACxBO,YAAYP,CAAAA,qBAAAA,MAAMO,UAAU,YAAhBP,qBAAoB;gBAChCQ,SAAShB,iBAAiBS;gBAC1BQ,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,IAAIA,SAAS;IACb,MAAMQ,eAAeF,YAAYE,YAAY;IAE7C,8FAA8F;IAC9F,MAAMV,UACJhB,iBAAiB0B,uBAAAA,eAAgBF,YAAYf,IAAI,KACjD,iEAAiE;IACjE,CAAC,CAACe,YAAYG,UAAU;IAC1B,IAAID,gBAAgB,CAACV,SAAS;QAC5B,IAAIY,gBAAgBxB,qBAAqByB,GAAG,CAACH;QAC7C,IAAI,CAACE,eAAe;YAClBA,gBAAgBrB,QAAQuB,iBAAiB,CAACJ;YAC1CtB,qBAAqB2B,GAAG,CAACL,cAAcE;YACvCI,WAAW;gBACT,sEAAsE;gBACtE,0BAA0B;gBAC1B5B,qBAAqB6B,MAAM,CAACP;YAC9B,GAAG;QACL;QACAR,SAAS,MAAMU;IACjB;QAKcJ,mBACJA,qBAEN,sEAAsE;IACtE,4EAA4E;IAC5E,kCAAkC;IAClC,oGAAoG;IACpGhB;IAVJ,kDAAkD;IAClD,MAAM0B,iBAAiB;QACrBzB,MAAMe,YAAYf,IAAI;QACtBG,YAAYY,CAAAA,oBAAAA,YAAYX,IAAI,YAAhBW,oBAAoB;QAChCV,QAAQU,CAAAA,sBAAAA,YAAYV,MAAM,YAAlBU,sBAAsB;QAC9BT,YAKEP,CAAAA,qBAAAA,MAAMO,UAAU,YAAhBP,qBAAoB;QACtBQ;QACAC,WAAW,EAAE;IACf;IAEA,OAAO;QACLT,OAAO0B;QACPhB;IACF;AACF;AACA,SAASiB,UAAUC,SAAwB;IACzC,IAAI,CAACA,WAAW;QACd,OAAOzB;IACT;IAEA,+DAA+D;IAC/D,OAAOyB,UAAUC,OAAO,CAAC,2BAA2B,IAAIA,OAAO,CAAC,UAAU;AAC5E;AAEA,SAASC,kBACPC,IAAgC;IAEhC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGF;IAE7B,OAAOC,OACJE,GAAG,CAAC,CAAClC;QACJ,MAAMC,OAAO0B,UAAU3B,MAAMC,IAAI;QAEjC,IAAI,CAACA,MAAM;YACT,OAAOE;QACT;YAIcH,mBACNA,mBACEA;QAJV,OAAO;YACLC;YACAM,YAAYP,CAAAA,oBAAAA,MAAMO,UAAU,YAAhBP,oBAAoB;YAChCK,MAAML,CAAAA,oBAAAA,MAAMI,UAAU,YAAhBJ,oBAAoB;YAC1BM,QAAQN,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;YACxBiC;QACF;IACF,GACCE,MAAM,CAAC,CAACC,IAAgCA,MAAMjC;AACnD;AAEA,SAASkC,iBACPC,YAA6B;IAE7B,MAAMrC,OAAO0B,UAAUW,aAAajB,GAAG,CAAC;IAExC,IAAI,CAACpB,MAAM;QACT,OAAOE;IACT;QAIcmC,mBACGA,oBACEA;IAJnB,OAAO;QACLrC;QACAM,YAAY+B,CAAAA,oBAAAA,aAAajB,GAAG,CAAC,yBAAjBiB,oBAAkC;QAC9CjC,MAAMkC,SAASD,CAAAA,qBAAAA,aAAajB,GAAG,CAAC,yBAAjBiB,qBAAkC,KAAK,OAAO;QAC7DhC,QAAQiC,SAASD,CAAAA,qBAAAA,aAAajB,GAAG,CAAC,qBAAjBiB,qBAA8B,KAAK,OAAO;QAC3DL,UAAUK,aAAajB,GAAG,CAAC,gBAAgB;IAC7C;AACF;AA0BA;;;CAGC,GACD,SAASmB,+BACPxC,KAA0B,EAC1ByC,OAA+B;IAE/B,IAAI,cAAcA,SAAS;YACPzC;QAAlB,MAAM0C,YAAY1C,CAAAA,cAAAA,MAAMK,IAAI,YAAVL,cAAc;YACZA;QAApB,MAAM2C,cAAc3C,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;QACpC,2FAA2F;QAC3F,uGAAuG;QACvG,4BAA4B;QAC5B,IAAI4C,UAA6CH,QAAQI,QAAQ,CAAC,EAAE;QACpE,IACE,IAAIC,IAAI,GACRA,IAAIL,QAAQI,QAAQ,CAACE,MAAM,IAC3BN,QAAQI,QAAQ,CAACC,EAAE,CAACE,MAAM,CAAC3C,IAAI,IAAIqC,aACnCD,QAAQI,QAAQ,CAACC,EAAE,CAACE,MAAM,CAAC1C,MAAM,IAAIqC,aACrCG,IACA;YACAF,UAAUH,QAAQI,QAAQ,CAACC,EAAE;QAC/B;QAEA,OAAOF,YAAYzC,YAAYA,YAAYyC,QAAQV,GAAG;IACxD,OAAO;QACL,OAAOO;IACT;AACF;AAEA;;CAEC,GACD,eAAeQ,kBACbjD,KAA0B;IAE1B,MAAMkD,YACJhD,mBAAmBF,MAAMC,IAAI;IAC/B,IAAIkD;IACJ,IAAI;YACiBC;QAAnBD,oBAAmBC,iBAAAA,IAAAA,yBAAa,EAACF,+BAAdE,eAA0BX,OAAO;IACtD,EAAE,OAAOY,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,KAAEJ,YAAU,4FACb;YAAEG;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIF,qBAAqBhD,WAAW;QAClC,IAAIoD;QACJ,IAAI;YACFA,WAAW,MAAM,IAAIC,8BAAiB,CAACL;QACzC,EAAE,OAAOE,OAAO;YACd,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,KAAEJ,YAAU,4FACb;gBAAEG;YAAM,IAFJ,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,IAAII;QAIJ,IAAI;gBAEMzD,aAEGA;YAHX,MAAM0D,mBAAmBH,SAASI,mBAAmB,CAAC;gBACpDtD,MAAML,CAAAA,cAAAA,MAAMK,IAAI,YAAVL,cAAc;gBACpB,mDAAmD;gBACnDM,QAAQ,AAACN,CAAAA,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB,CAAA,IAAK;YAChC;YAEA,IAAI0D,iBAAiBhD,MAAM,KAAK,MAAM;gBACpC+C,SAAS;YACX,OAAO;oBAEHF;gBADF,MAAMK,gBACJL,CAAAA,6BAAAA,SAASM,gBAAgB,CACvBH,iBAAiBhD,MAAM,EACvB,uBAAuB,GAAG,iBAF5B6C,6BAGK;gBAEPE,SAAS;oBAAEC;oBAAkBE;gBAAc;YAC7C;QACF,SAAU;YACRL,SAASO,OAAO;QAClB;QAEA,IAAIL,WAAW,MAAM;gBA4Bf,sEAAsE;YACtE,4EAA4E;YAC5E,kCAAkC;YAClC,oGAAoG;YACpGzD,2BAAAA;YA/BJ,MAAM,EAAE0D,gBAAgB,EAAEE,aAAa,EAAE,GAAGH;YAC5C,MAAMM,sBAAsBvB,+BAC1BxC,OACAmD;YAGF,0GAA0G;YAC1G,IAAI3C,UAAU;YACd,IAAIuD,wBAAwB5D,WAAW;gBACrC6D,QAAQC,KAAK,CACX,wDACAjE;YAEJ,OAAO;oBAMH+D;gBALF,wDAAwD;gBACxD,MAAMG,cAAcH,oBAAoBI,OAAO,CAACC,OAAO,CACrDV,iBAAiBhD,MAAM;oBAGvBqD;gBADFvD,UACEuD,CAAAA,4CAAAA,kCAAAA,oBAAoBM,UAAU,qBAA9BN,gCAAgCrE,QAAQ,CAACwE,wBAAzCH,2CACA,sEAAsE;gBACtE,yEAAyE;gBACzEvE,iBAAiBQ,MAAMC,IAAI;YAC/B;gBAWWyD,0BAEGA;YAXd,MAAMY,qBAA0C;gBAC9C/D,YAKEP,EAAAA,oBAAAA,MAAMO,UAAU,sBAAhBP,4BAAAA,kBACI6B,OAAO,CAAC,8BAA8B,+BAD1C7B,0BAEI6B,OAAO,CAAC,wBAAwB,QAAO;gBAC7CvB,QAAQ,AAACoD,CAAAA,CAAAA,2BAAAA,iBAAiBpD,MAAM,YAAvBoD,2BAA2B,CAAA,IAAK;gBACzCzD,MAAMyD,iBAAiBhD,MAAM;gBAC7BN,YAAYsD,CAAAA,yBAAAA,iBAAiBrD,IAAI,YAArBqD,yBAAyB;gBACrC,6EAA6E;gBAC7EjD,WAAW,EAAE;gBACbD;YACF;YAEA,OAAO;gBACLR,OAAOsE;gBACP5D,QAAQkD;YACV;QACF;IACF;IAEA,OAAOzD;AACT;AAEA,eAAeoE,yBACbxE,OAAgB,EAChByE,WAAmB,EACnBxE,KAA0B;QAGvB;IADH,MAAMyD,SACJ,CAAC,OAAA,MAAMR,kBAAkBjD,kBAAxB,OACD,4DAA4D;IAC5D,uEAAuE;IACtE,MAAMF,mBAAmBC,SAASC;IACrC,IAAI,CAACyD,QAAQ;QACX,OAAO;IACT;IAEA,IAAIgB,+BAA+BhB,OAAOzD,KAAK,CAACC,IAAI;IACpD,IACEwE,iCAAiC,QACjCA,6BAA6B9E,UAAU,CAAC,YACxC;QACA8E,+BAA+BC,aAAI,CAACC,QAAQ,CAC1CH,aACAI,YAAG,CAACC,aAAa,CAACJ;IAEtB;IAEA,OAAO;QACLH,oBAAoB;YAClB7D,WAAWgD,OAAOzD,KAAK,CAACS,SAAS;YACjCH,QAAQmD,OAAOzD,KAAK,CAACM,MAAM;YAC3BL,MAAMwE;YACNjE,SAASiD,OAAOzD,KAAK,CAACQ,OAAO;YAC7BJ,YAAYqD,OAAOzD,KAAK,CAACI,UAAU;YACnCG,YAAYkD,OAAOzD,KAAK,CAACO,UAAU;QACrC;QACAuE,mBAAmBC,IAAAA,4BAAoB,EAACtB,OAAOzD,KAAK,EAAEyD,OAAO/C,MAAM;IACrE;AACF;AAEO,SAASpB,qBAAqBS,OAAgB,EAAEyE,WAAmB;IACxE,OAAO,eACLQ,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAE7C,YAAY,EAAE,GAAG,IAAI8C,IAAIJ,IAAIJ,GAAG,EAAG;QAErD,IAAIO,aAAa,mCAAmC;YAClD,IAAIH,IAAIK,MAAM,KAAK,QAAQ;gBACzB,OAAOC,sCAAkB,CAACC,UAAU,CAACN;YACvC;YAEA,MAAMlD,OAAO,MAAM,IAAIyD,QAAgB,CAACC,SAASC;gBAC/C,IAAIC,OAAO;gBACXX,IAAIY,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAb,IAAIY,EAAE,CAAC,OAAO,IAAMH,QAAQE;gBAC5BX,IAAIY,EAAE,CAAC,SAASF;YAClB;YAEA,MAAMI,UAAUC,KAAKC,KAAK,CAACjE;YAC3B,MAAMkE,cAAcnE,kBAAkBgE;YACtC,MAAMI,SAAsC,MAAMV,QAAQW,GAAG,CAC3DF,YAAY/D,GAAG,CAAC,OAAOlC;gBACrB,IAAI;oBACF,MAAMoG,aAAa,MAAM7B,yBACvBxE,SACAyE,aACAxE;oBAEF,IAAIoG,eAAe,MAAM;wBACvB,OAAO;4BACLC,QAAQ;4BACRC,QAAQ;wBACV;oBACF;oBACA,OAAO;wBAAED,QAAQ;wBAAaE,OAAOH;oBAAW;gBAClD,EAAE,OAAOnC,OAAO;oBACd,OAAO;wBACLoC,QAAQ;wBACRC,QAAQE,IAAAA,iBAAO,EAACvC,OAAO;4BAAEwC,QAAQ;wBAAM;oBACzC;gBACF;YACF;YAGF,OAAOnB,sCAAkB,CAACoB,IAAI,CAACzB,KAAKiB;QACtC,OAAO,IAAIf,aAAa,2BAA2B;YACjD,MAAMnF,QAAQqC,iBAAiBC;YAE/B,IAAI,CAACtC,OAAO,OAAOsF,sCAAkB,CAACC,UAAU,CAACN;YAEjD,MAAM0B,aAAa,MAAMC,iBAAE,CAACC,MAAM,CAAC7G,MAAMC,IAAI,EAAE6G,mBAAE,CAACC,IAAI,EAAEC,IAAI,CAC1D,IAAM,MACN,IAAM;YAER,IAAI,CAACL,YAAY,OAAOrB,sCAAkB,CAAC2B,QAAQ,CAAChC;YAEpD,IAAI;oBACuBjF,aAAiBA;gBAA1CkH,IAAAA,0BAAY,EAAClH,MAAMC,IAAI,EAAED,CAAAA,cAAAA,MAAMK,IAAI,YAAVL,cAAc,GAAGA,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;YAC5D,EAAE,OAAOmH,KAAK;gBACZnD,QAAQoD,GAAG,CAAC,4BAA4BD;gBACxC,OAAO7B,sCAAkB,CAAC+B,mBAAmB,CAACpC;YAChD;YAEA,OAAOK,sCAAkB,CAACgC,SAAS,CAACrC;QACtC;QAEA,OAAOC;IACT;AACF;AAEO,SAAS3F,uBAAuBQ,OAAgB;IACrD,OAAO,eACLiF,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAE7C,YAAY,EAAE,GAAG,IAAI8C,IAAIJ,IAAIJ,GAAG,EAAG;QAErD,IAAIO,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,IAAIqC,WAAWjF,aAAajB,GAAG,CAAC;QAEhC,IAAI,CAACkG,UAAU;YACb,OAAOjC,sCAAkB,CAACC,UAAU,CAACN;QACvC;QAEA,mDAAmD;QACnD,iEAAiE;QACjE,IACEsC,SAAS5H,UAAU,CAAC,iBACpB4H,SAAS5H,UAAU,CAAC,yBACpB;YACA,MAAM6H,YAAYpE,IAAAA,yBAAa,EAACmE;YAEhC,IAAIC,WAAW;gBACb,OAAOlC,sCAAkB,CAACoB,IAAI,CAACzB,KAAKuC,UAAU/E,OAAO;YACvD;YAEA,OAAO6C,sCAAkB,CAACgC,SAAS,CAACrC;QACtC;QAEA,IAAI;YACF,kDAAkD;YAClDsC,WAAWE,UAAUF;YAErB,IAAI7C,aAAI,CAACgD,UAAU,CAACH,WAAW;gBAC7BA,WAAW3C,YAAG,CAAChE,aAAa,CAAC2G,UAAUxG,IAAI;YAC7C;YAEA,MAAM4G,kBAAkB,MAAM5H,QAAQ6H,YAAY,CAACL;YAEnD,IAAII,iBAAiB;gBACnB,OAAOrC,sCAAkB,CAACuC,UAAU,CAAC5C,KAAK0C;YAC5C;YAEA,IAAIJ,SAAS5H,UAAU,CAAC,UAAU;gBAChC,MAAM6H,YAAY,MAAMM,IAAAA,0CAAoB,EAACP;gBAE7C,IAAIC,WAAW;oBACb,OAAOlC,sCAAkB,CAACoB,IAAI,CAACzB,KAAKuC;gBACtC;YACF;QACF,EAAE,OAAOvD,OAAO;YACdD,QAAQC,KAAK,CAAC,6BAA6BA;QAC7C;QAEAqB,sCAAkB,CAACgC,SAAS,CAACrC;IAC/B;AACF"}