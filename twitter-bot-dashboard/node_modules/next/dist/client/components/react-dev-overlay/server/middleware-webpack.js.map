{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware-webpack.ts"], "sourcesContent": ["import { constants as FS, promises as fs } from 'fs'\nimport { findSourceMap, type SourceMap } from 'module'\nimport path from 'path'\nimport { fileURLToPath, pathToFileURL } from 'url'\nimport {\n  SourceMapConsumer,\n  type BasicSourceMapConsumer,\n} from 'next/dist/compiled/source-map08'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport { getSourceMapFromFile } from '../utils/get-source-map-from-file'\nimport { launchEditor } from '../utils/launch-editor'\nimport {\n  getOriginalCodeFrame,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n} from './shared'\nimport { middlewareResponse } from './middleware-response'\nexport { getServerError } from '../utils/node-stack-frames'\nexport { parseStack } from '../utils/parse-stack'\nexport { getSourceMapFromFile }\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type webpack from 'webpack'\nimport type {\n  NullableMappedPosition,\n  RawSourceMap,\n} from 'next/dist/compiled/source-map08'\nimport { formatFrameSourceFile } from '../utils/webpack-module-path'\nimport type { MappedPosition } from 'source-map'\nimport { inspect } from 'util'\n\nfunction shouldIgnoreSource(sourceURL: string): boolean {\n  return (\n    sourceURL.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    sourceURL.includes('next/dist') ||\n    sourceURL.startsWith('node:')\n  )\n}\n\ntype IgnoredSources = Array<{ url: string; ignored: boolean }>\n\nexport interface IgnorableStackFrame extends StackFrame {\n  ignored: boolean\n}\n\ntype SourceAttributes = {\n  sourcePosition: NullableMappedPosition\n  sourceContent: string | null\n}\n\ntype Source =\n  | {\n      type: 'file'\n      sourceMap: RawSourceMap\n      ignoredSources: IgnoredSources\n      moduleURL: string\n    }\n  | {\n      type: 'bundle'\n      sourceMap: RawSourceMap\n      ignoredSources: IgnoredSources\n      compilation: webpack.Compilation\n      moduleId: string\n      moduleURL: string\n    }\n\nfunction getModuleById(\n  id: string | undefined,\n  compilation: webpack.Compilation\n) {\n  const { chunkGraph, modules } = compilation\n\n  return [...modules].find((module) => chunkGraph.getModuleId(module) === id)\n}\n\nfunction findModuleNotFoundFromError(errorMessage: string | undefined) {\n  return errorMessage?.match(/'([^']+)' module/)?.[1]\n}\n\nfunction getSourcePath(source: string) {\n  if (source.startsWith('file://')) {\n    return fileURLToPath(source)\n  }\n  return source.replace(/^(webpack:\\/\\/\\/|webpack:\\/\\/|webpack:\\/\\/_N_E\\/)/, '')\n}\n\n/**\n * @returns 1-based lines and 0-based columns\n */\nasync function findOriginalSourcePositionAndContent(\n  sourceMap: RawSourceMap,\n  position: { lineNumber: number | null; column: number | null }\n): Promise<SourceAttributes | null> {\n  let consumer: BasicSourceMapConsumer\n  try {\n    consumer = await new SourceMapConsumer(sourceMap)\n  } catch (cause) {\n    throw new Error(\n      `${sourceMap.file}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  try {\n    const sourcePosition = consumer.originalPositionFor({\n      line: position.lineNumber ?? 1,\n      // 0-based columns out requires 0-based columns in.\n      column: (position.column ?? 1) - 1,\n    })\n\n    if (!sourcePosition.source) {\n      return null\n    }\n\n    const sourceContent: string | null =\n      consumer.sourceContentFor(\n        sourcePosition.source,\n        /* returnNullOnMissing */ true\n      ) ?? null\n\n    return {\n      sourcePosition,\n      sourceContent,\n    }\n  } finally {\n    consumer.destroy()\n  }\n}\n\nexport function getIgnoredSources(\n  sourceMap: RawSourceMap & { ignoreList?: number[] }\n): IgnoredSources {\n  const ignoreList = new Set<number>(sourceMap.ignoreList ?? [])\n  const moduleFilenames = sourceMap?.sources ?? []\n\n  for (let index = 0; index < moduleFilenames.length; index++) {\n    // bundlerFilePath case: webpack://./app/page.tsx\n    const webpackSourceURL = moduleFilenames[index]\n    // Format the path to the normal file path\n    const formattedFilePath = formatFrameSourceFile(webpackSourceURL)\n    if (shouldIgnoreSource(formattedFilePath)) {\n      ignoreList.add(index)\n    }\n  }\n\n  const ignoredSources = sourceMap.sources.map((source, index) => {\n    return {\n      url: source,\n      ignored: ignoreList.has(sourceMap.sources.indexOf(source)),\n      content: sourceMap.sourcesContent?.[index] ?? null,\n    }\n  })\n  return ignoredSources\n}\n\nfunction isIgnoredSource(\n  source: Source,\n  sourcePosition: MappedPosition | NullableMappedPosition\n) {\n  if (sourcePosition.source == null) {\n    return true\n  }\n  for (const ignoredSource of source.ignoredSources) {\n    if (ignoredSource.ignored && ignoredSource.url === sourcePosition.source) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction findOriginalSourcePositionAndContentFromCompilation(\n  moduleId: string | undefined,\n  importedModule: string,\n  compilation: webpack.Compilation\n): SourceAttributes | null {\n  const module = getModuleById(moduleId, compilation)\n  return module?.buildInfo?.importLocByPath?.get(importedModule) ?? null\n}\n\nexport async function createOriginalStackFrame({\n  source,\n  rootDirectory,\n  frame,\n  errorMessage,\n}: {\n  source: Source\n  rootDirectory: string\n  frame: StackFrame\n  errorMessage?: string\n}): Promise<OriginalStackFrameResponse | null> {\n  const moduleNotFound = findModuleNotFoundFromError(errorMessage)\n  const result = await (() => {\n    if (moduleNotFound) {\n      if (source.type === 'file') {\n        return undefined\n      }\n\n      return findOriginalSourcePositionAndContentFromCompilation(\n        source.moduleId,\n        moduleNotFound,\n        source.compilation\n      )\n    }\n    return findOriginalSourcePositionAndContent(source.sourceMap, frame)\n  })()\n\n  if (!result) {\n    return null\n  }\n  const { sourcePosition, sourceContent } = result\n\n  if (!sourcePosition.source) {\n    return null\n  }\n\n  const ignored =\n    isIgnoredSource(source, sourcePosition) ||\n    // If the source file is externals, should be excluded even it's not ignored source.\n    // e.g. webpack://next/dist/.. needs to be ignored\n    shouldIgnoreSource(source.moduleURL)\n\n  const sourcePath = getSourcePath(\n    // When sourcePosition.source is the loader path the modulePath is generally better.\n    (sourcePosition.source!.includes('|')\n      ? source.moduleURL\n      : sourcePosition.source) || source.moduleURL\n  )\n  const filePath = path.resolve(rootDirectory, sourcePath)\n  const resolvedFilePath = path.relative(rootDirectory, filePath)\n\n  const traced: IgnorableStackFrame = {\n    file: resolvedFilePath,\n    lineNumber: sourcePosition.line,\n    column: (sourcePosition.column ?? 0) + 1,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      // default is not a valid identifier in JS so webpack uses a custom variable when it's an unnamed default export\n      // Resolve it back to `default` for the method name if the source position didn't have the method.\n      frame.methodName\n        ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n        ?.replace('__webpack_exports__.', ''),\n    arguments: [],\n    ignored,\n  }\n\n  return {\n    originalStackFrame: traced,\n    originalCodeFrame: getOriginalCodeFrame(traced, sourceContent),\n  }\n}\n\nasync function getSourceMapFromCompilation(\n  id: string,\n  compilation: webpack.Compilation\n): Promise<RawSourceMap | undefined> {\n  try {\n    const module = getModuleById(id, compilation)\n\n    if (!module) {\n      return undefined\n    }\n\n    // @ts-expect-error The types for `CodeGenerationResults.get` require a\n    // runtime to be passed as second argument, but apparently it also works\n    // without it.\n    const codeGenerationResult = compilation.codeGenerationResults.get(module)\n    const source = codeGenerationResult?.sources.get('javascript')\n\n    return source?.map() ?? undefined\n  } catch (err) {\n    console.error(`Failed to lookup module by ID (\"${id}\"):`, err)\n    return undefined\n  }\n}\n\nasync function getSource(\n  sourceURL: string,\n  options: {\n    getCompilations: () => webpack.Compilation[]\n  }\n): Promise<Source | undefined> {\n  const { getCompilations } = options\n\n  // Rspack is now using file:// URLs for source maps. Remove the rsc prefix to produce the file:/// url.\n  sourceURL = sourceURL.replace(/(.*)\\/(?=file:\\/\\/)/, '')\n\n  let nativeSourceMap: SourceMap | undefined\n  try {\n    nativeSourceMap = findSourceMap(sourceURL)\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (nativeSourceMap !== undefined) {\n    const sourceMapPayload = nativeSourceMap.payload\n    return {\n      type: 'file',\n      sourceMap: sourceMapPayload,\n      ignoredSources: getIgnoredSources(sourceMapPayload),\n      moduleURL: sourceURL,\n    }\n  }\n\n  if (path.isAbsolute(sourceURL)) {\n    sourceURL = pathToFileURL(sourceURL).href\n  }\n\n  if (sourceURL.startsWith('file:')) {\n    const sourceMap = await getSourceMapFromFile(sourceURL)\n    return sourceMap\n      ? {\n          type: 'file',\n          sourceMap,\n          ignoredSources: getIgnoredSources(sourceMap),\n          moduleURL: sourceURL,\n        }\n      : undefined\n  }\n\n  // webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n  // rsc://React/Server/webpack-internal:///(rsc)/./src/hello.tsx?42 => (rsc)/./src/hello.tsx\n  // webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n  const moduleId = sourceURL\n    .replace(\n      /^(rsc:\\/\\/React\\/[^/]+\\/)?(webpack-internal:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)/,\n      ''\n    )\n    .replace(/\\?\\d+$/, '')\n\n  // (rsc)/./src/hello.tsx => ./src/hello.tsx\n  const moduleURL = moduleId.replace(/^(\\(.*\\)\\/?)/, '')\n\n  for (const compilation of getCompilations()) {\n    const sourceMap = await getSourceMapFromCompilation(moduleId, compilation)\n\n    if (sourceMap) {\n      const ignoredSources = getIgnoredSources(sourceMap)\n      return {\n        type: 'bundle',\n        sourceMap,\n        compilation,\n        moduleId,\n        moduleURL,\n        ignoredSources,\n      }\n    }\n  }\n\n  return undefined\n}\n\nfunction getOriginalStackFrames({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frames,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frames: StackFrame[]\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFramesResponse> {\n  return Promise.all(\n    frames.map(\n      (frame): Promise<OriginalStackFramesResponse[number]> =>\n        getOriginalStackFrame({\n          isServer,\n          isEdgeServer,\n          isAppDirectory,\n          frame,\n          clientStats,\n          serverStats,\n          edgeServerStats,\n          rootDirectory,\n        }).then(\n          (value) => {\n            return {\n              status: 'fulfilled',\n              value,\n            }\n          },\n          (reason) => {\n            return {\n              status: 'rejected',\n              reason: inspect(reason, { colors: false }),\n            }\n          }\n        )\n    )\n  )\n}\n\nasync function getOriginalStackFrame({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frame,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frame: StackFrame\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFrameResponse> {\n  const filename = frame.file ?? ''\n  const source = await getSource(filename, {\n    getCompilations: () => {\n      const compilations: webpack.Compilation[] = []\n\n      // Try Client Compilation first. In `pages` we leverage\n      // `isClientError` to check. In `app` it depends on if it's a server\n      // / client component and when the code throws. E.g. during HTML\n      // rendering it's the server/edge compilation.\n      if ((!isEdgeServer && !isServer) || isAppDirectory) {\n        const compilation = clientStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Server Compilation. In `pages` this could be something\n      // imported in getServerSideProps/getStaticProps as the code for\n      // those is tree-shaken. In `app` this finds server components and\n      // code that was imported from a server component. It also covers\n      // when client component code throws during HTML rendering.\n      if (isServer || isAppDirectory) {\n        const compilation = serverStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Edge Server Compilation. Both cases are the same as Server\n      // Compilation, main difference is that it covers `runtime: 'edge'`\n      // pages/app routes.\n      if (isEdgeServer || isAppDirectory) {\n        const compilation = edgeServerStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      return compilations\n    },\n  })\n\n  let defaultNormalizedStackFrameLocation = frame.file\n  if (\n    defaultNormalizedStackFrameLocation !== null &&\n    defaultNormalizedStackFrameLocation.startsWith('file://')\n  ) {\n    defaultNormalizedStackFrameLocation = path.relative(\n      rootDirectory,\n      fileURLToPath(defaultNormalizedStackFrameLocation)\n    )\n  }\n  // This stack frame is used for the one that couldn't locate the source or source mapped frame\n  const defaultStackFrame: IgnorableStackFrame = {\n    file: defaultNormalizedStackFrameLocation,\n    lineNumber: frame.lineNumber,\n    column: frame.column ?? 1,\n    methodName: frame.methodName,\n    ignored: shouldIgnoreSource(filename),\n    arguments: [],\n  }\n  if (!source) {\n    // return original stack frame with no source map\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n\n  const originalStackFrameResponse = await createOriginalStackFrame({\n    frame,\n    source,\n    rootDirectory,\n  })\n\n  if (!originalStackFrameResponse) {\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n\n  return originalStackFrameResponse\n}\n\nexport function getOverlayMiddleware(options: {\n  rootDirectory: string\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { rootDirectory, clientStats, serverStats, edgeServerStats } = options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      try {\n        const { frames, isServer, isEdgeServer, isAppDirectory } = JSON.parse(\n          body\n        ) as OriginalStackFramesRequest\n\n        return middlewareResponse.json(\n          res,\n          await getOriginalStackFrames({\n            isServer,\n            isEdgeServer,\n            isAppDirectory,\n            frames: frames.map((frame) => ({\n              ...frame,\n              lineNumber: frame.lineNumber ?? 0,\n              column: frame.column ?? 0,\n            })),\n            clientStats,\n            serverStats,\n            edgeServerStats,\n            rootDirectory,\n          })\n        )\n      } catch (err) {\n        return middlewareResponse.badRequest(res)\n      }\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const frame = {\n        file: searchParams.get('file') as string,\n        methodName: searchParams.get('methodName') as string,\n        lineNumber: parseInt(searchParams.get('lineNumber') ?? '0', 10) || 0,\n        column: parseInt(searchParams.get('column') ?? '0', 10) || 0,\n        arguments: searchParams.getAll('arguments').filter(Boolean),\n      } satisfies StackFrame\n\n      if (!frame.file) return middlewareResponse.badRequest(res)\n\n      // frame files may start with their webpack layer, like (middleware)/middleware.js\n      const filePath = path.resolve(\n        rootDirectory,\n        frame.file.replace(/^\\([^)]+\\)\\//, '')\n      )\n      const fileExists = await fs.access(filePath, FS.F_OK).then(\n        () => true,\n        () => false\n      )\n      if (!fileExists) return middlewareResponse.notFound(res)\n\n      try {\n        launchEditor(filePath, frame.lineNumber, frame.column ?? 1)\n      } catch (err) {\n        console.log('Failed to launch editor:', err)\n        return middlewareResponse.internalServerError(res)\n      }\n\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(options: {\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { clientStats, serverStats, edgeServerStats } = options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    const filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    let source: Source | undefined\n\n    try {\n      source = await getSource(filename, {\n        getCompilations: () => {\n          const compilations: webpack.Compilation[] = []\n\n          for (const stats of [\n            clientStats(),\n            serverStats(),\n            edgeServerStats(),\n          ]) {\n            if (stats?.compilation) {\n              compilations.push(stats.compilation)\n            }\n          }\n\n          return compilations\n        },\n      })\n    } catch (error) {\n      return middlewareResponse.internalServerError(res, error)\n    }\n\n    if (!source) {\n      return middlewareResponse.noContent(res)\n    }\n\n    return middlewareResponse.json(res, source.sourceMap)\n  }\n}\n"], "names": ["createOriginalStackFrame", "getIgnoredSources", "getOverlayMiddleware", "getServerError", "getSourceMapFromFile", "getSourceMapMiddleware", "parseStack", "shouldIgnoreSource", "sourceURL", "includes", "startsWith", "getModuleById", "id", "compilation", "chunkGraph", "modules", "find", "module", "getModuleId", "findModuleNotFoundFromError", "errorMessage", "match", "getSourcePath", "source", "fileURLToPath", "replace", "findOriginalSourcePositionAndContent", "sourceMap", "position", "consumer", "SourceMapConsumer", "cause", "Error", "file", "sourcePosition", "originalPositionFor", "line", "lineNumber", "column", "sourceContent", "sourceContentFor", "destroy", "ignoreList", "Set", "moduleFilenames", "sources", "index", "length", "webpackSourceURL", "formattedFilePath", "formatFrameSourceFile", "add", "ignoredSources", "map", "url", "ignored", "has", "indexOf", "content", "sourcesContent", "isIgnoredSource", "ignoredSource", "findOriginalSourcePositionAndContentFromCompilation", "moduleId", "importedModule", "buildInfo", "importLocByPath", "get", "rootDirectory", "frame", "moduleNotFound", "result", "type", "undefined", "moduleURL", "sourcePath", "filePath", "path", "resolve", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relative", "traced", "methodName", "arguments", "originalStackFrame", "originalCodeFrame", "getOriginalCodeFrame", "getSourceMapFromCompilation", "codeGenerationResult", "codeGenerationResults", "err", "console", "error", "getSource", "options", "getCompilations", "nativeSourceMap", "findSourceMap", "sourceMapPayload", "payload", "isAbsolute", "pathToFileURL", "href", "getOriginalStackFrames", "isServer", "isEdgeServer", "isAppDirectory", "frames", "clientStats", "serverStats", "edgeServerStats", "Promise", "all", "getOriginalStackFrame", "then", "value", "status", "reason", "inspect", "colors", "filename", "compilations", "push", "defaultNormalizedStackFrameLocation", "defaultStackFrame", "originalStackFrameResponse", "req", "res", "next", "pathname", "searchParams", "URL", "method", "middlewareResponse", "badRequest", "body", "reject", "data", "on", "chunk", "JSON", "parse", "json", "parseInt", "getAll", "filter", "Boolean", "fileExists", "fs", "access", "FS", "F_OK", "notFound", "launchEditor", "log", "internalServerError", "noContent", "stats"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAsLsBA,wBAAwB;eAAxBA;;IAnDNC,iBAAiB;eAAjBA;;IAiYAC,oBAAoB;eAApBA;;IAlfPC,cAAc;eAAdA,+BAAc;;IAEdC,oBAAoB;eAApBA,0CAAoB;;IA0kBbC,sBAAsB;eAAtBA;;IA3kBPC,UAAU;eAAVA,sBAAU;;;;oBAnB6B;wBACF;+DAC7B;qBAC4B;6BAItC;sCAE8B;8BACR;wBAMtB;oCAC4B;iCACJ;4BACJ;mCASW;sBAEd;AAExB,SAASC,mBAAmBC,SAAiB;IAC3C,OACEA,UAAUC,QAAQ,CAAC,mBACnB,2EAA2E;IAC3ED,UAAUC,QAAQ,CAAC,gBACnBD,UAAUE,UAAU,CAAC;AAEzB;AA6BA,SAASC,cACPC,EAAsB,EACtBC,WAAgC;IAEhC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE,GAAGF;IAEhC,OAAO;WAAIE;KAAQ,CAACC,IAAI,CAAC,CAACC,UAAWH,WAAWI,WAAW,CAACD,aAAYL;AAC1E;AAEA,SAASO,4BAA4BC,YAAgC;QAC5DA;IAAP,OAAOA,iCAAAA,sBAAAA,aAAcC,KAAK,CAAC,wCAApBD,mBAAyC,CAAC,EAAE;AACrD;AAEA,SAASE,cAAcC,MAAc;IACnC,IAAIA,OAAOb,UAAU,CAAC,YAAY;QAChC,OAAOc,IAAAA,kBAAa,EAACD;IACvB;IACA,OAAOA,OAAOE,OAAO,CAAC,qDAAqD;AAC7E;AAEA;;CAEC,GACD,eAAeC,qCACbC,SAAuB,EACvBC,QAA8D;IAE9D,IAAIC;IACJ,IAAI;QACFA,WAAW,MAAM,IAAIC,8BAAiB,CAACH;IACzC,EAAE,OAAOI,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,KAAEL,UAAUM,IAAI,GAAC,4FAClB;YAAEF;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAI;YAEMH,sBAEGA;QAHX,MAAMM,iBAAiBL,SAASM,mBAAmB,CAAC;YAClDC,MAAMR,CAAAA,uBAAAA,SAASS,UAAU,YAAnBT,uBAAuB;YAC7B,mDAAmD;YACnDU,QAAQ,AAACV,CAAAA,CAAAA,mBAAAA,SAASU,MAAM,YAAfV,mBAAmB,CAAA,IAAK;QACnC;QAEA,IAAI,CAACM,eAAeX,MAAM,EAAE;YAC1B,OAAO;QACT;YAGEM;QADF,MAAMU,gBACJV,CAAAA,6BAAAA,SAASW,gBAAgB,CACvBN,eAAeX,MAAM,EACrB,uBAAuB,GAAG,iBAF5BM,6BAGK;QAEP,OAAO;YACLK;YACAK;QACF;IACF,SAAU;QACRV,SAASY,OAAO;IAClB;AACF;AAEO,SAASxC,kBACd0B,SAAmD;QAEhBA;IAAnC,MAAMe,aAAa,IAAIC,IAAYhB,CAAAA,wBAAAA,UAAUe,UAAU,YAApBf,wBAAwB,EAAE;QACrCA;IAAxB,MAAMiB,kBAAkBjB,CAAAA,qBAAAA,6BAAAA,UAAWkB,OAAO,YAAlBlB,qBAAsB,EAAE;IAEhD,IAAK,IAAImB,QAAQ,GAAGA,QAAQF,gBAAgBG,MAAM,EAAED,QAAS;QAC3D,iDAAiD;QACjD,MAAME,mBAAmBJ,eAAe,CAACE,MAAM;QAC/C,0CAA0C;QAC1C,MAAMG,oBAAoBC,IAAAA,wCAAqB,EAACF;QAChD,IAAIzC,mBAAmB0C,oBAAoB;YACzCP,WAAWS,GAAG,CAACL;QACjB;IACF;IAEA,MAAMM,iBAAiBzB,UAAUkB,OAAO,CAACQ,GAAG,CAAC,CAAC9B,QAAQuB;YAIzCnB;YAAAA;QAHX,OAAO;YACL2B,KAAK/B;YACLgC,SAASb,WAAWc,GAAG,CAAC7B,UAAUkB,OAAO,CAACY,OAAO,CAAClC;YAClDmC,SAAS/B,CAAAA,mCAAAA,4BAAAA,UAAUgC,cAAc,qBAAxBhC,yBAA0B,CAACmB,MAAM,YAAjCnB,kCAAqC;QAChD;IACF;IACA,OAAOyB;AACT;AAEA,SAASQ,gBACPrC,MAAc,EACdW,cAAuD;IAEvD,IAAIA,eAAeX,MAAM,IAAI,MAAM;QACjC,OAAO;IACT;IACA,KAAK,MAAMsC,iBAAiBtC,OAAO6B,cAAc,CAAE;QACjD,IAAIS,cAAcN,OAAO,IAAIM,cAAcP,GAAG,KAAKpB,eAAeX,MAAM,EAAE;YACxE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASuC,oDACPC,QAA4B,EAC5BC,cAAsB,EACtBnD,WAAgC;QAGzBI,mCAAAA;IADP,MAAMA,UAASN,cAAcoD,UAAUlD;QAChCI;IAAP,OAAOA,CAAAA,wCAAAA,4BAAAA,oBAAAA,QAAQgD,SAAS,sBAAjBhD,oCAAAA,kBAAmBiD,eAAe,qBAAlCjD,kCAAoCkD,GAAG,CAACH,2BAAxC/C,wCAA2D;AACpE;AAEO,eAAejB,yBAAyB,KAU9C;IAV8C,IAAA,EAC7CuB,MAAM,EACN6C,aAAa,EACbC,KAAK,EACLjD,YAAY,EAMb,GAV8C;QAwDzC,sEAAsE;IACtE,4EAA4E;IAC5E,kCAAkC;IAClC,oGAAoG;IACpG,gHAAgH;IAChH,kGAAkG;IAClGiD,2BAAAA;IAnDJ,MAAMC,iBAAiBnD,4BAA4BC;IACnD,MAAMmD,SAAS,MAAM,AAAC,CAAA;QACpB,IAAID,gBAAgB;YAClB,IAAI/C,OAAOiD,IAAI,KAAK,QAAQ;gBAC1B,OAAOC;YACT;YAEA,OAAOX,oDACLvC,OAAOwC,QAAQ,EACfO,gBACA/C,OAAOV,WAAW;QAEtB;QACA,OAAOa,qCAAqCH,OAAOI,SAAS,EAAE0C;IAChE,CAAA;IAEA,IAAI,CAACE,QAAQ;QACX,OAAO;IACT;IACA,MAAM,EAAErC,cAAc,EAAEK,aAAa,EAAE,GAAGgC;IAE1C,IAAI,CAACrC,eAAeX,MAAM,EAAE;QAC1B,OAAO;IACT;IAEA,MAAMgC,UACJK,gBAAgBrC,QAAQW,mBACxB,oFAAoF;IACpF,kDAAkD;IAClD3B,mBAAmBgB,OAAOmD,SAAS;IAErC,MAAMC,aAAarD,cAEjB,AADA,oFAAoF;IACnFY,CAAAA,eAAeX,MAAM,CAAEd,QAAQ,CAAC,OAC7Bc,OAAOmD,SAAS,GAChBxC,eAAeX,MAAM,AAAD,KAAMA,OAAOmD,SAAS;IAEhD,MAAME,WAAWC,aAAI,CAACC,OAAO,CAACV,eAAeO;IAC7C,MAAMI,mBAAmBF,aAAI,CAACG,QAAQ,CAACZ,eAAeQ;QAK3C1C;IAHX,MAAM+C,SAA8B;QAClChD,MAAM8C;QACN1C,YAAYH,eAAeE,IAAI;QAC/BE,QAAQ,AAACJ,CAAAA,CAAAA,yBAAAA,eAAeI,MAAM,YAArBJ,yBAAyB,CAAA,IAAK;QACvCgD,UAAU,GAORb,oBAAAA,MAAMa,UAAU,sBAAhBb,4BAAAA,kBACI5C,OAAO,CAAC,8BAA8B,+BAD1C4C,0BAEI5C,OAAO,CAAC,wBAAwB;QACtC0D,WAAW,EAAE;QACb5B;IACF;IAEA,OAAO;QACL6B,oBAAoBH;QACpBI,mBAAmBC,IAAAA,4BAAoB,EAACL,QAAQ1C;IAClD;AACF;AAEA,eAAegD,4BACb3E,EAAU,EACVC,WAAgC;IAEhC,IAAI;QACF,MAAMI,UAASN,cAAcC,IAAIC;QAEjC,IAAI,CAACI,SAAQ;YACX,OAAOwD;QACT;QAEA,uEAAuE;QACvE,wEAAwE;QACxE,cAAc;QACd,MAAMe,uBAAuB3E,YAAY4E,qBAAqB,CAACtB,GAAG,CAAClD;QACnE,MAAMM,SAASiE,wCAAAA,qBAAsB3C,OAAO,CAACsB,GAAG,CAAC;YAE1C5C;QAAP,OAAOA,CAAAA,cAAAA,0BAAAA,OAAQ8B,GAAG,cAAX9B,cAAiBkD;IAC1B,EAAE,OAAOiB,KAAK;QACZC,QAAQC,KAAK,CAAC,AAAC,qCAAkChF,KAAG,OAAM8E;QAC1D,OAAOjB;IACT;AACF;AAEA,eAAeoB,UACbrF,SAAiB,EACjBsF,OAEC;IAED,MAAM,EAAEC,eAAe,EAAE,GAAGD;IAE5B,uGAAuG;IACvGtF,YAAYA,UAAUiB,OAAO,CAAC,uBAAuB;IAErD,IAAIuE;IACJ,IAAI;QACFA,kBAAkBC,IAAAA,qBAAa,EAACzF;IAClC,EAAE,OAAOuB,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,KAAExB,YAAU,4FACb;YAAEuB;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIiE,oBAAoBvB,WAAW;QACjC,MAAMyB,mBAAmBF,gBAAgBG,OAAO;QAChD,OAAO;YACL3B,MAAM;YACN7C,WAAWuE;YACX9C,gBAAgBnD,kBAAkBiG;YAClCxB,WAAWlE;QACb;IACF;IAEA,IAAIqE,aAAI,CAACuB,UAAU,CAAC5F,YAAY;QAC9BA,YAAY6F,IAAAA,kBAAa,EAAC7F,WAAW8F,IAAI;IAC3C;IAEA,IAAI9F,UAAUE,UAAU,CAAC,UAAU;QACjC,MAAMiB,YAAY,MAAMvB,IAAAA,0CAAoB,EAACI;QAC7C,OAAOmB,YACH;YACE6C,MAAM;YACN7C;YACAyB,gBAAgBnD,kBAAkB0B;YAClC+C,WAAWlE;QACb,IACAiE;IACN;IAEA,yDAAyD;IACzD,2FAA2F;IAC3F,oDAAoD;IACpD,MAAMV,WAAWvD,UACdiB,OAAO,CACN,6EACA,IAEDA,OAAO,CAAC,UAAU;IAErB,2CAA2C;IAC3C,MAAMiD,YAAYX,SAAStC,OAAO,CAAC,gBAAgB;IAEnD,KAAK,MAAMZ,eAAekF,kBAAmB;QAC3C,MAAMpE,YAAY,MAAM4D,4BAA4BxB,UAAUlD;QAE9D,IAAIc,WAAW;YACb,MAAMyB,iBAAiBnD,kBAAkB0B;YACzC,OAAO;gBACL6C,MAAM;gBACN7C;gBACAd;gBACAkD;gBACAW;gBACAtB;YACF;QACF;IACF;IAEA,OAAOqB;AACT;AAEA,SAAS8B,uBAAuB,KAkB/B;IAlB+B,IAAA,EAC9BC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,WAAW,EACXC,eAAe,EACf1C,aAAa,EAUd,GAlB+B;IAmB9B,OAAO2C,QAAQC,GAAG,CAChBL,OAAOtD,GAAG,CACR,CAACgB,QACC4C,sBAAsB;YACpBT;YACAC;YACAC;YACArC;YACAuC;YACAC;YACAC;YACA1C;QACF,GAAG8C,IAAI,CACL,CAACC;YACC,OAAO;gBACLC,QAAQ;gBACRD;YACF;QACF,GACA,CAACE;YACC,OAAO;gBACLD,QAAQ;gBACRC,QAAQC,IAAAA,aAAO,EAACD,QAAQ;oBAAEE,QAAQ;gBAAM;YAC1C;QACF;AAIV;AAEA,eAAeN,sBAAsB,KAkBpC;IAlBoC,IAAA,EACnCT,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdrC,KAAK,EACLuC,WAAW,EACXC,WAAW,EACXC,eAAe,EACf1C,aAAa,EAUd,GAlBoC;QAmBlBC;IAAjB,MAAMmD,WAAWnD,CAAAA,cAAAA,MAAMpC,IAAI,YAAVoC,cAAc;IAC/B,MAAM9C,SAAS,MAAMsE,UAAU2B,UAAU;QACvCzB,iBAAiB;YACf,MAAM0B,eAAsC,EAAE;YAE9C,uDAAuD;YACvD,oEAAoE;YACpE,gEAAgE;YAChE,8CAA8C;YAC9C,IAAI,AAAC,CAAChB,gBAAgB,CAACD,YAAaE,gBAAgB;oBAC9BE;gBAApB,MAAM/F,eAAc+F,eAAAA,kCAAAA,aAAe/F,WAAW;gBAE9C,IAAIA,aAAa;oBACf4G,aAAaC,IAAI,CAAC7G;gBACpB;YACF;YAEA,6DAA6D;YAC7D,gEAAgE;YAChE,kEAAkE;YAClE,iEAAiE;YACjE,2DAA2D;YAC3D,IAAI2F,YAAYE,gBAAgB;oBACVG;gBAApB,MAAMhG,eAAcgG,eAAAA,kCAAAA,aAAehG,WAAW;gBAE9C,IAAIA,aAAa;oBACf4G,aAAaC,IAAI,CAAC7G;gBACpB;YACF;YAEA,iEAAiE;YACjE,mEAAmE;YACnE,oBAAoB;YACpB,IAAI4F,gBAAgBC,gBAAgB;oBACdI;gBAApB,MAAMjG,eAAciG,mBAAAA,sCAAAA,iBAAmBjG,WAAW;gBAElD,IAAIA,aAAa;oBACf4G,aAAaC,IAAI,CAAC7G;gBACpB;YACF;YAEA,OAAO4G;QACT;IACF;IAEA,IAAIE,sCAAsCtD,MAAMpC,IAAI;IACpD,IACE0F,wCAAwC,QACxCA,oCAAoCjH,UAAU,CAAC,YAC/C;QACAiH,sCAAsC9C,aAAI,CAACG,QAAQ,CACjDZ,eACA5C,IAAAA,kBAAa,EAACmG;IAElB;QAKUtD;IAJV,8FAA8F;IAC9F,MAAMuD,oBAAyC;QAC7C3F,MAAM0F;QACNtF,YAAYgC,MAAMhC,UAAU;QAC5BC,QAAQ+B,CAAAA,gBAAAA,MAAM/B,MAAM,YAAZ+B,gBAAgB;QACxBa,YAAYb,MAAMa,UAAU;QAC5B3B,SAAShD,mBAAmBiH;QAC5BrC,WAAW,EAAE;IACf;IACA,IAAI,CAAC5D,QAAQ;QACX,iDAAiD;QACjD,OAAO;YACL6D,oBAAoBwC;YACpBvC,mBAAmB;QACrB;IACF;IAEA,MAAMwC,6BAA6B,MAAM7H,yBAAyB;QAChEqE;QACA9C;QACA6C;IACF;IAEA,IAAI,CAACyD,4BAA4B;QAC/B,OAAO;YACLzC,oBAAoBwC;YACpBvC,mBAAmB;QACrB;IACF;IAEA,OAAOwC;AACT;AAEO,SAAS3H,qBAAqB4F,OAKpC;IACC,MAAM,EAAE1B,aAAa,EAAEwC,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAGhB;IAErE,OAAO,eACLgC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUL,IAAIxE,GAAG;QAE7D,IAAI2E,aAAa,mCAAmC;YAClD,IAAIH,IAAIM,MAAM,KAAK,QAAQ;gBACzB,OAAOC,sCAAkB,CAACC,UAAU,CAACP;YACvC;YAEA,MAAMQ,OAAO,MAAM,IAAIxB,QAAgB,CAACjC,SAAS0D;gBAC/C,IAAIC,OAAO;gBACXX,IAAIY,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAb,IAAIY,EAAE,CAAC,OAAO,IAAM5D,QAAQ2D;gBAC5BX,IAAIY,EAAE,CAAC,SAASF;YAClB;YAEA,IAAI;gBACF,MAAM,EAAE7B,MAAM,EAAEH,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAE,GAAGkC,KAAKC,KAAK,CACnEN;gBAGF,OAAOF,sCAAkB,CAACS,IAAI,CAC5Bf,KACA,MAAMxB,uBAAuB;oBAC3BC;oBACAC;oBACAC;oBACAC,QAAQA,OAAOtD,GAAG,CAAC,CAACgB;4BAENA,mBACJA;+BAHqB;4BAC7B,GAAGA,KAAK;4BACRhC,YAAYgC,CAAAA,oBAAAA,MAAMhC,UAAU,YAAhBgC,oBAAoB;4BAChC/B,QAAQ+B,CAAAA,gBAAAA,MAAM/B,MAAM,YAAZ+B,gBAAgB;wBAC1B;;oBACAuC;oBACAC;oBACAC;oBACA1C;gBACF;YAEJ,EAAE,OAAOsB,KAAK;gBACZ,OAAO2C,sCAAkB,CAACC,UAAU,CAACP;YACvC;QACF,OAAO,IAAIE,aAAa,2BAA2B;gBAI1BC,mBACJA;YAJnB,MAAM7D,QAAQ;gBACZpC,MAAMiG,aAAa/D,GAAG,CAAC;gBACvBe,YAAYgD,aAAa/D,GAAG,CAAC;gBAC7B9B,YAAY0G,SAASb,CAAAA,oBAAAA,aAAa/D,GAAG,CAAC,yBAAjB+D,oBAAkC,KAAK,OAAO;gBACnE5F,QAAQyG,SAASb,CAAAA,qBAAAA,aAAa/D,GAAG,CAAC,qBAAjB+D,qBAA8B,KAAK,OAAO;gBAC3D/C,WAAW+C,aAAac,MAAM,CAAC,aAAaC,MAAM,CAACC;YACrD;YAEA,IAAI,CAAC7E,MAAMpC,IAAI,EAAE,OAAOoG,sCAAkB,CAACC,UAAU,CAACP;YAEtD,kFAAkF;YAClF,MAAMnD,WAAWC,aAAI,CAACC,OAAO,CAC3BV,eACAC,MAAMpC,IAAI,CAACR,OAAO,CAAC,gBAAgB;YAErC,MAAM0H,aAAa,MAAMC,YAAE,CAACC,MAAM,CAACzE,UAAU0E,aAAE,CAACC,IAAI,EAAErC,IAAI,CACxD,IAAM,MACN,IAAM;YAER,IAAI,CAACiC,YAAY,OAAOd,sCAAkB,CAACmB,QAAQ,CAACzB;YAEpD,IAAI;oBACuC1D;gBAAzCoF,IAAAA,0BAAY,EAAC7E,UAAUP,MAAMhC,UAAU,EAAEgC,CAAAA,gBAAAA,MAAM/B,MAAM,YAAZ+B,gBAAgB;YAC3D,EAAE,OAAOqB,KAAK;gBACZC,QAAQ+D,GAAG,CAAC,4BAA4BhE;gBACxC,OAAO2C,sCAAkB,CAACsB,mBAAmB,CAAC5B;YAChD;YAEA,OAAOM,sCAAkB,CAACuB,SAAS,CAAC7B;QACtC;QAEA,OAAOC;IACT;AACF;AAEO,SAAS3H,uBAAuByF,OAItC;IACC,MAAM,EAAEc,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAGhB;IAEtD,OAAO,eACLgC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUL,IAAIxE,GAAG;QAE7D,IAAI2E,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,MAAMR,WAAWU,aAAa/D,GAAG,CAAC;QAElC,IAAI,CAACqD,UAAU;YACb,OAAOa,sCAAkB,CAACC,UAAU,CAACP;QACvC;QAEA,IAAIxG;QAEJ,IAAI;YACFA,SAAS,MAAMsE,UAAU2B,UAAU;gBACjCzB,iBAAiB;oBACf,MAAM0B,eAAsC,EAAE;oBAE9C,KAAK,MAAMoC,SAAS;wBAClBjD;wBACAC;wBACAC;qBACD,CAAE;wBACD,IAAI+C,yBAAAA,MAAOhJ,WAAW,EAAE;4BACtB4G,aAAaC,IAAI,CAACmC,MAAMhJ,WAAW;wBACrC;oBACF;oBAEA,OAAO4G;gBACT;YACF;QACF,EAAE,OAAO7B,OAAO;YACd,OAAOyC,sCAAkB,CAACsB,mBAAmB,CAAC5B,KAAKnC;QACrD;QAEA,IAAI,CAACrE,QAAQ;YACX,OAAO8G,sCAAkB,CAACuB,SAAS,CAAC7B;QACtC;QAEA,OAAOM,sCAAkB,CAACS,IAAI,CAACf,KAAKxG,OAAOI,SAAS;IACtD;AACF"}