#!/usr/bin/env node

/**
 * Complete setup and test script for the new Twitter API with database token management
 */

const { TwitterApi } = require('twitter-api-v2');
const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');
require('dotenv').config();

// Create Supabase client
let supabase;
try {
  supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );
} catch (error) {
  console.error('❌ Failed to initialize Supabase client:', error.message);
}

async function checkEnvironment() {
  console.log('🔍 Checking Environment Setup...\n');

  const requiredEnvVars = [
    'TWITTER_CLIENT_ID',
    'TWITTER_CLIENT_SECRET',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  let allGood = true;
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (value) {
      console.log(`   ✅ ${envVar}: ${value.substring(0, 15)}...`);
    } else {
      console.log(`   ❌ ${envVar}: Missing`);
      allGood = false;
    }
  }

  return allGood;
}

async function checkDatabase() {
  console.log('\n🗄️  Checking Database Setup...\n');

  if (!supabase) {
    console.log('   ❌ Supabase client not initialized');
    return false;
  }

  try {
    // Check if twitter_tokens table exists
    const { data, error } = await supabase
      .from('twitter_tokens')
      .select('*')
      .limit(1);

    if (error && error.code === '42P01') {
      console.log('   ❌ twitter_tokens table does not exist');
      console.log('   💡 Run: node run-migration.js or create the table manually');
      return false;
    } else if (error) {
      console.log('   ❌ Database error:', error.message);
      return false;
    } else {
      console.log('   ✅ twitter_tokens table exists');
      console.log(`   📊 Found ${data.length} token records`);
      return true;
    }
  } catch (error) {
    console.log('   ❌ Database connection failed:', error.message);
    return false;
  }
}

async function checkStoredTokens() {
  console.log('\n🔑 Checking Stored Tokens...\n');

  try {
    const { data, error } = await supabase
      .from('twitter_tokens')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code === 'PGRST116') {
      console.log('   ⚠️  No tokens found in database');
      console.log('   💡 You need to run the OAuth flow first');
      return null;
    } else if (error) {
      console.log('   ❌ Error fetching tokens:', error.message);
      return null;
    } else {
      console.log('   ✅ Tokens found in database');
      console.log(`   📅 Last updated: ${data.updated_at}`);
      console.log(`   🔒 Token type: ${data.token_type}`);
      if (data.scope) {
        console.log(`   🎯 Scope: ${data.scope}`);
      }
      if (data.expires_at) {
        const expiresAt = new Date(data.expires_at);
        const now = new Date();
        const isExpired = expiresAt < now;
        console.log(`   ⏰ Expires: ${data.expires_at} ${isExpired ? '(EXPIRED)' : '(Valid)'}`);
      }
      return data;
    }
  } catch (error) {
    console.log('   ❌ Unexpected error:', error.message);
    return null;
  }
}

async function testTokenRefresh(storedTokens) {
  console.log('\n🔄 Testing Token Refresh...\n');

  if (!storedTokens) {
    console.log('   ⏭️  Skipping - no stored tokens');
    return false;
  }

  try {
    const client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID,
      clientSecret: process.env.TWITTER_CLIENT_SECRET,
    });

    console.log('   🔄 Attempting token refresh...');
    const { client: rwClient, accessToken, refreshToken } = await client.refreshOAuth2Token(
      storedTokens.refresh_token
    );

    console.log('   ✅ Token refresh successful!');
    console.log(`   🔑 New access token: ${accessToken.substring(0, 20)}...`);
    console.log(`   🔄 New refresh token: ${refreshToken.substring(0, 20)}...`);

    // Update tokens in database
    const { error: updateError } = await supabase
      .from('twitter_tokens')
      .update({
        access_token: accessToken,
        refresh_token: refreshToken,
        updated_at: new Date().toISOString()
      })
      .eq('id', storedTokens.id);

    if (updateError) {
      console.log('   ⚠️  Failed to update tokens in database:', updateError.message);
    } else {
      console.log('   ✅ Tokens updated in database');
    }

    // Test authentication
    console.log('   🔐 Testing authentication...');
    const user = await rwClient.v2.me();
    console.log(`   👤 Authenticated as: @${user.data.username} (${user.data.name})`);

    return true;
  } catch (error) {
    console.log('   ❌ Token refresh failed:', error.message);
    
    if (error.message.includes('invalid_grant')) {
      console.log('   💡 Hint: Refresh token is invalid/expired. Re-run OAuth flow.');
    }
    
    return false;
  }
}

async function promptForOAuth() {
  console.log('\n🔐 OAuth Setup Required...\n');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question('❓ Do you want to run the OAuth flow now? (y/N): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function runOAuthFlow() {
  console.log('\n🚀 Starting OAuth Flow...\n');

  try {
    const client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID,
      clientSecret: process.env.TWITTER_CLIENT_SECRET,
    });

    const callbackUrl = 'http://localhost:3000/callback';
    const { url, codeVerifier, state } = client.generateOAuth2AuthLink(callbackUrl, {
      scope: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
    });

    console.log('🌐 OAuth URL generated:');
    console.log(url);
    console.log('');
    console.log('📋 Next steps:');
    console.log('1. Open the URL above in your browser');
    console.log('2. Authorize the application');
    console.log('3. Copy the authorization code from the callback URL');
    console.log('4. Come back here to complete the setup');
    console.log('');

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('📝 Enter the authorization code: ', async (code) => {
        rl.close();
        
        try {
          const { client: userClient, accessToken, refreshToken } = await client.loginWithOAuth2({
            code,
            codeVerifier,
            redirectUri: callbackUrl,
          });

          console.log('\n✅ OAuth successful!');
          
          // Save tokens to database
          const { error } = await supabase
            .from('twitter_tokens')
            .insert({
              access_token: accessToken,
              refresh_token: refreshToken,
              token_type: 'bearer',
              scope: 'tweet.read tweet.write users.read offline.access',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (error) {
            console.log('⚠️  Failed to save tokens to database:', error.message);
          } else {
            console.log('✅ Tokens saved to database');
          }

          resolve(true);
        } catch (error) {
          console.log('❌ OAuth failed:', error.message);
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.log('❌ Failed to start OAuth flow:', error.message);
    return false;
  }
}

async function main() {
  console.log('🐦 Twitter API Setup & Test Suite');
  console.log('=================================\n');

  // Step 1: Check environment
  const envOk = await checkEnvironment();
  if (!envOk) {
    console.log('\n❌ Environment setup incomplete. Please fix the issues above.');
    return;
  }

  // Step 2: Check database
  const dbOk = await checkDatabase();
  if (!dbOk) {
    console.log('\n❌ Database setup incomplete. Please run the migration first.');
    return;
  }

  // Step 3: Check stored tokens
  const storedTokens = await checkStoredTokens();

  // Step 4: Test token refresh or run OAuth
  let refreshOk = false;
  if (storedTokens) {
    refreshOk = await testTokenRefresh(storedTokens);
  }

  if (!refreshOk) {
    const runOAuth = await promptForOAuth();
    if (runOAuth) {
      const oauthOk = await runOAuthFlow();
      if (oauthOk) {
        console.log('\n🎉 Setup completed successfully!');
        console.log('\n📋 Summary:');
        console.log('   ✅ Environment configured');
        console.log('   ✅ Database table ready');
        console.log('   ✅ OAuth tokens stored');
        console.log('   ✅ Authentication working');
        console.log('\n🚀 Your Twitter API is ready to use!');
      }
    } else {
      console.log('\n⏭️  OAuth setup skipped. You can run this script again later.');
    }
  } else {
    console.log('\n🎉 All tests passed! Your Twitter API is ready to use!');
  }
}

main().catch(console.error);
