module.exports = {

"[project]/node_modules/@supabase/node-fetch/lib/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/lib/index.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@supabase/realtime-js/node_modules/@supabase/node-fetch/lib/index.mjs [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/91b86_@supabase_node-fetch_lib_index_mjs_2c9c0bb5._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@supabase/realtime-js/node_modules/@supabase/node-fetch/lib/index.mjs [app-route] (ecmascript)");
    });
});
}}),
"[project]/src/lib/twitter-auth.ts [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_twitter-api-v2_dist_esm_4854f7ca._.js",
  "server/chunks/[root-of-the-server]__25387e8e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/twitter-auth.ts [app-route] (ecmascript)");
    });
});
}}),

};