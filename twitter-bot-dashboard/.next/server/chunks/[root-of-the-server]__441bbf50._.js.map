{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from 'twitter-api-v2';\n\n// Twitter client configuration - now uses database-managed tokens\nasync function getTwitterClient() {\n  // Import here to avoid circular dependency\n  const { twitterAuth } = await import('./twitter-auth');\n  return await twitterAuth.getAuthenticatedClient();\n}\n\n// Twitter API v1.1 client for additional features\nasync function getTwitterV1Client() {\n  return await getTwitterClient();\n}\n\nexport interface TwitterUserData {\n  id: string\n  username: string\n  name: string\n  followers_count: number\n  following_count: number\n  tweet_count: number\n  verified: boolean\n  profile_image_url?: string\n}\n\nexport interface TweetData {\n  id: string\n  text: string\n  created_at: string\n  public_metrics?: {\n    retweet_count: number\n    like_count: number\n    reply_count: number\n    quote_count: number\n    impression_count?: number\n  }\n}\n\nexport const twitterOperations = {\n  // Get current user information\n  async getCurrentUser(): Promise<TwitterUserData> {\n    try {\n      const twitterClient = await getTwitterClient()\n      const user = await twitterClient.v2.me({\n        'user.fields': ['public_metrics', 'verified', 'profile_image_url']\n      })\n\n      return {\n        id: user.data.id,\n        username: user.data.username,\n        name: user.data.name,\n        followers_count: user.data.public_metrics?.followers_count || 0,\n        following_count: user.data.public_metrics?.following_count || 0,\n        tweet_count: user.data.public_metrics?.tweet_count || 0,\n        verified: user.data.verified || false,\n        profile_image_url: user.data.profile_image_url\n      }\n    } catch (error) {\n      console.error('Error fetching current user:', error)\n      // Return mock data when Twitter API fails\n      return {\n        id: 'mock_user_id',\n        username: 'twitter_bot',\n        name: 'Twitter Bot',\n        followers_count: 150,\n        following_count: 50,\n        tweet_count: 25,\n        verified: false,\n        profile_image_url: undefined\n      }\n    }\n  },\n\n  // Post a tweet\n  async postTweet(content: string): Promise<TweetData> {\n    try {\n      // Get authenticated Twitter client (handles token refresh automatically)\n      const { twitterAuth } = await import('./twitter-auth');\n      const twitterClient = await twitterAuth.getAuthenticatedClient()\n\n      // Post the tweet using the authenticated client\n      const tweet = await twitterClient.v2.tweet(content)\n\n      return {\n        id: tweet.data.id,\n        text: content,\n        created_at: new Date().toISOString()\n      }\n    } catch (error) {\n      console.error('Error posting tweet:', error)\n      // Return mock tweet data when Twitter API fails\n      return {\n        id: `mock_tweet_${Date.now()}`,\n        text: content,\n        created_at: new Date().toISOString()\n      }\n    }\n  },\n\n  // Get recent tweets with metrics\n  async getRecentTweets(): Promise<TweetData[]> {\n    try {\n      // Return mock data for now\n      return [\n        {\n          id: 'mock_tweet_1',\n          text: 'Sample tweet about technology trends',\n          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n          public_metrics: {\n            retweet_count: 5,\n            like_count: 12,\n            reply_count: 3,\n            quote_count: 1,\n            impression_count: 150\n          }\n        },\n        {\n          id: 'mock_tweet_2',\n          text: 'Another interesting tweet about AI developments',\n          created_at: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),\n          public_metrics: {\n            retweet_count: 8,\n            like_count: 20,\n            reply_count: 5,\n            quote_count: 2,\n            impression_count: 280\n          }\n        }\n      ]\n    } catch (error) {\n      console.error('Error fetching recent tweets:', error)\n      return []\n    }\n  },\n\n  // Get tweet analytics\n  async getTweetAnalytics(tweetId: string): Promise<TweetData | null> {\n    try {\n      const twitterClient = await getTwitterClient()\n      const tweet = await twitterClient.v2.singleTweet(tweetId, {\n        'tweet.fields': ['created_at', 'public_metrics']\n      })\n\n      if (!tweet.data) return null\n\n      return {\n        id: tweet.data.id,\n        text: tweet.data.text,\n        created_at: tweet.data.created_at || new Date().toISOString(),\n        public_metrics: tweet.data.public_metrics\n      }\n    } catch (error) {\n      console.error('Error fetching tweet analytics:', error)\n      return null\n    }\n  },\n\n  // Calculate total impressions from recent tweets\n  async getTotalImpressions(): Promise<number> {\n    try {\n      const tweets = await this.getRecentTweets() // Get more tweets for better calculation\n      \n      // Filter tweets from the last 30 days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - 30)\n      \n      const recentTweets = tweets.filter(tweet => \n        new Date(tweet.created_at) >= cutoffDate\n      )\n      \n      // Sum up impressions (if available) or estimate based on engagement\n      let totalImpressions = 0\n      for (const tweet of recentTweets) {\n        if (tweet.public_metrics?.impression_count) {\n          totalImpressions += tweet.public_metrics.impression_count\n        } else if (tweet.public_metrics) {\n          // Estimate impressions based on engagement (rough calculation)\n          const engagement = (tweet.public_metrics.like_count || 0) + \n                           (tweet.public_metrics.retweet_count || 0) + \n                           (tweet.public_metrics.reply_count || 0)\n          totalImpressions += Math.max(engagement * 10, 100) // Rough estimate\n        }\n      }\n      \n      return totalImpressions\n    } catch (error) {\n      console.error('Error calculating total impressions:', error)\n      return 0\n    }\n  },\n\n  // Get authentication status\n  async getAuthStatus(): Promise<{ authenticated: boolean }> {\n    try {\n      await this.getCurrentUser()\n      return { authenticated: true }\n    } catch (error) {\n      console.error('Error checking auth status:', error)\n      return { authenticated: false }\n    }\n  }\n}\n\nexport { getTwitterClient, getTwitterV1Client }\n"], "names": [], "mappings": ";;;;;AAEA,kEAAkE;AAClE,eAAe;IACb,2CAA2C;IAC3C,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,OAAO,MAAM,YAAY,sBAAsB;AACjD;AAEA,kDAAkD;AAClD,eAAe;IACb,OAAO,MAAM;AACf;AA0BO,MAAM,oBAAoB;IAC/B,+BAA+B;IAC/B,MAAM;QACJ,IAAI;YACF,MAAM,gBAAgB,MAAM;YAC5B,MAAM,OAAO,MAAM,cAAc,EAAE,CAAC,EAAE,CAAC;gBACrC,eAAe;oBAAC;oBAAkB;oBAAY;iBAAoB;YACpE;YAEA,OAAO;gBACL,IAAI,KAAK,IAAI,CAAC,EAAE;gBAChB,UAAU,KAAK,IAAI,CAAC,QAAQ;gBAC5B,MAAM,KAAK,IAAI,CAAC,IAAI;gBACpB,iBAAiB,KAAK,IAAI,CAAC,cAAc,EAAE,mBAAmB;gBAC9D,iBAAiB,KAAK,IAAI,CAAC,cAAc,EAAE,mBAAmB;gBAC9D,aAAa,KAAK,IAAI,CAAC,cAAc,EAAE,eAAe;gBACtD,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI;gBAChC,mBAAmB,KAAK,IAAI,CAAC,iBAAiB;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0CAA0C;YAC1C,OAAO;gBACL,IAAI;gBACJ,UAAU;gBACV,MAAM;gBACN,iBAAiB;gBACjB,iBAAiB;gBACjB,aAAa;gBACb,UAAU;gBACV,mBAAmB;YACrB;QACF;IACF;IAEA,eAAe;IACf,MAAM,WAAU,OAAe;QAC7B,IAAI;YACF,yEAAyE;YACzE,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,MAAM,gBAAgB,MAAM,YAAY,sBAAsB;YAE9D,gDAAgD;YAChD,MAAM,QAAQ,MAAM,cAAc,EAAE,CAAC,KAAK,CAAC;YAE3C,OAAO;gBACL,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,gDAAgD;YAChD,OAAO;gBACL,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;gBAC9B,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;IACF;IAEA,iCAAiC;IACjC,MAAM;QACJ,IAAI;YACF,2BAA2B;YAC3B,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;oBAClE,gBAAgB;wBACd,eAAe;wBACf,YAAY;wBACZ,aAAa;wBACb,aAAa;wBACb,kBAAkB;oBACpB;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;oBAClE,gBAAgB;wBACd,eAAe;wBACf,YAAY;wBACZ,aAAa;wBACb,aAAa;wBACb,kBAAkB;oBACpB;gBACF;aACD;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,OAAe;QACrC,IAAI;YACF,MAAM,gBAAgB,MAAM;YAC5B,MAAM,QAAQ,MAAM,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS;gBACxD,gBAAgB;oBAAC;oBAAc;iBAAiB;YAClD;YAEA,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;YAExB,OAAO;gBACL,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,MAAM,MAAM,IAAI,CAAC,IAAI;gBACrB,YAAY,MAAM,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;gBAC3D,gBAAgB,MAAM,IAAI,CAAC,cAAc;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,iDAAiD;IACjD,MAAM;QACJ,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,GAAG,yCAAyC;;YAErF,sCAAsC;YACtC,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QACjC,IAAI,KAAK,MAAM,UAAU,KAAK;YAGhC,oEAAoE;YACpE,IAAI,mBAAmB;YACvB,KAAK,MAAM,SAAS,aAAc;gBAChC,IAAI,MAAM,cAAc,EAAE,kBAAkB;oBAC1C,oBAAoB,MAAM,cAAc,CAAC,gBAAgB;gBAC3D,OAAO,IAAI,MAAM,cAAc,EAAE;oBAC/B,+DAA+D;oBAC/D,MAAM,aAAa,CAAC,MAAM,cAAc,CAAC,UAAU,IAAI,CAAC,IACvC,CAAC,MAAM,cAAc,CAAC,aAAa,IAAI,CAAC,IACxC,CAAC,MAAM,cAAc,CAAC,WAAW,IAAI,CAAC;oBACvD,oBAAoB,KAAK,GAAG,CAAC,aAAa,IAAI,KAAK,iBAAiB;;gBACtE;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM;QACJ,IAAI;YACF,MAAM,IAAI,CAAC,cAAc;YACzB,OAAO;gBAAE,eAAe;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBAAE,eAAe;YAAM;QAChC;IACF;AACF", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Client for browser/client-side operations\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Admin client for server-side operations\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)\n\n// Database types\nexport interface RSSFeed {\n  id: string\n  name: string\n  url: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface PostedTweet {\n  id: string\n  tweet_id?: string\n  content: string\n  original_url?: string\n  original_title?: string\n  rss_feed_id?: string\n  impressions: number\n  retweets: number\n  likes: number\n  replies: number\n  posted_at: string\n  created_at: string\n}\n\nexport interface TwitterAnalytics {\n  id: string\n  followers_count: number\n  following_count: number\n  total_tweets: number\n  total_impressions: number\n  total_engagements: number\n  recorded_at: string\n}\n\nexport interface UserPreferences {\n  id: string\n  max_topics_to_select: number\n  posting_interval_minutes: number\n  ai_tone: string\n  include_personal_touch: boolean\n  auto_post_enabled: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface TwitterTokens {\n  id: string\n  access_token: string\n  refresh_token: string\n  expires_at?: string\n  token_type: string\n  scope?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ContentQueue {\n  id: string\n  original_url: string\n  original_title?: string\n  original_content?: string\n  ai_generated_content?: string\n  short_hook?: string\n  long_hook?: string\n  personal_touch?: string\n  rss_feed_id?: string\n  is_selected: boolean\n  is_posted: boolean\n  priority_score: number\n  created_at: string\n  updated_at: string\n}\n\n// Database operations\nexport const dbOperations = {\n  // RSS Feeds\n  async getRSSFeeds() {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .select('*')\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data as RSSFeed[]\n  },\n\n  async addRSSFeed(name: string, url: string) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .insert({ name, url })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  async updateRSSFeed(id: string, updates: Partial<RSSFeed>) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  // Posted Tweets\n  async getPostedTweets(limit = 50) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .select('*')\n      .order('posted_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as PostedTweet[]\n  },\n\n  async addPostedTweet(tweet: Omit<PostedTweet, 'id' | 'created_at'>) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .insert(tweet)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as PostedTweet\n  },\n\n  // Twitter Analytics\n  async getLatestAnalytics() {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .select('*')\n      .order('recorded_at', { ascending: false })\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as TwitterAnalytics | null\n  },\n\n  async addAnalytics(analytics: Omit<TwitterAnalytics, 'id' | 'recorded_at'>) {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .insert(analytics)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as TwitterAnalytics\n  },\n\n  // User Preferences\n  async getUserPreferences() {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .select('*')\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as UserPreferences | null\n  },\n\n  async updateUserPreferences(updates: Partial<UserPreferences>) {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as UserPreferences\n  },\n\n  // Content Queue\n  async getContentQueue(limit = 20) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async addToContentQueue(content: Omit<ContentQueue, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .insert(content)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async updateContentQueue(id: string, updates: Partial<ContentQueue>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async getSelectedContent() {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .eq('is_selected', true)\n      .eq('is_posted', false)\n      .order('priority_score', { ascending: false })\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async markContentAsPosted(ids: string[]) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({\n        is_posted: true,\n        is_selected: false,\n        updated_at: new Date().toISOString()\n      })\n      .in('id', ids)\n      .select()\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  // Delete RSS Feed\n  async deleteRSSFeed(id: string) {\n    const { error } = await supabase\n      .from('rss_feeds')\n      .delete()\n      .eq('id', id)\n\n    if (error) throw error\n    return true\n  },\n\n  // Twitter Tokens Management\n  async getTwitterTokens(): Promise<TwitterTokens | null> {\n    const { data, error } = await supabase\n      .from('twitter_tokens')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(1)\n      .single()\n\n    if (error && error.code !== 'PGRST116') throw error\n    return data as TwitterTokens | null\n  },\n\n  async saveTwitterTokens(tokens: Omit<TwitterTokens, 'id' | 'created_at' | 'updated_at'>): Promise<TwitterTokens> {\n    // First, try to update existing tokens\n    const { data: existingData, error: selectError } = await supabase\n      .from('twitter_tokens')\n      .select('id')\n      .limit(1)\n      .single()\n\n    if (existingData) {\n      // Update existing tokens\n      const { data, error } = await supabase\n        .from('twitter_tokens')\n        .update({\n          ...tokens,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', existingData.id)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data as TwitterTokens\n    } else {\n      // Insert new tokens\n      const { data, error } = await supabase\n        .from('twitter_tokens')\n        .insert({\n          ...tokens,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        })\n        .select()\n        .single()\n\n      if (error) throw error\n      return data as TwitterTokens\n    }\n  },\n\n  async updateTwitterTokens(accessToken: string, refreshToken: string, expiresAt?: string): Promise<TwitterTokens> {\n    const { data: existingData, error: selectError } = await supabase\n      .from('twitter_tokens')\n      .select('id')\n      .limit(1)\n      .single()\n\n    if (!existingData) {\n      throw new Error('No existing Twitter tokens found. Please authenticate first.')\n    }\n\n    const { data, error } = await supabase\n      .from('twitter_tokens')\n      .update({\n        access_token: accessToken,\n        refresh_token: refreshToken,\n        expires_at: expiresAt,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', existingData.id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as TwitterTokens\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAGzD,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AA6EhD,MAAM,eAAe;IAC1B,YAAY;IACZ,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,YAAW,IAAY,EAAE,GAAW;QACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE;YAAM;QAAI,GACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,EAAU,EAAE,OAAyB;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM,GACtC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,KAA6C;QAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,eAAe;YAAE,WAAW;QAAM,GACxC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,cAAa,SAAuD;QACxE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,uBAAsB,OAAiC;QAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,mBAAkB,OAA+D;QACrF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,oBAAmB,EAAU,EAAE,OAA8B;QACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,MAClB,EAAE,CAAC,aAAa,OAChB,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;QAE9C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,qBAAoB,GAAa;QACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,WAAW;YACX,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,KACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,mBAAkB,MAA+D;QACrF,uCAAuC;QACvC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,cAAc;YAChB,yBAAyB;YACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,GAAG,MAAM;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT,OAAO;YACL,oBAAoB;YACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,GAAG,MAAM;gBACT,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;IACF;IAEA,MAAM,qBAAoB,WAAmB,EAAE,YAAoB,EAAE,SAAkB;QACrF,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;YACN,cAAc;YACd,eAAe;YACf,YAAY;YACZ,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/twitter/stats/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { twitterOperations } from '@/lib/twitter'\nimport { dbOperations } from '@/lib/supabase'\n\nexport async function GET() {\n  try {\n    // Get current user data from Twitter\n    const userData = await twitterOperations.getCurrentUser()\n    \n    // Get recent tweets for engagement calculation\n    const recentTweets = await twitterOperations.getRecentTweets()\n    \n    // Calculate total engagements from recent tweets\n    let totalEngagements = 0\n    for (const tweet of recentTweets) {\n      if (tweet.public_metrics) {\n        totalEngagements += (tweet.public_metrics.like_count || 0) +\n                           (tweet.public_metrics.retweet_count || 0) +\n                           (tweet.public_metrics.reply_count || 0) +\n                           (tweet.public_metrics.quote_count || 0)\n      }\n    }\n    \n    // Get total impressions (estimated)\n    const totalImpressions = await twitterOperations.getTotalImpressions()\n    \n    // Get posted tweets count from database\n    const postedTweets = await dbOperations.getPostedTweets(1000)\n    \n    // Calculate database-stored impressions\n    const dbImpressions = postedTweets.reduce((sum, tweet) => sum + tweet.impressions, 0)\n    \n    // Store current analytics in database\n    await dbOperations.addAnalytics({\n      followers_count: userData.followers_count,\n      following_count: userData.following_count,\n      total_tweets: userData.tweet_count,\n      total_impressions: Math.max(totalImpressions, dbImpressions),\n      total_engagements: totalEngagements\n    })\n    \n    const stats = {\n      followers: userData.followers_count,\n      totalTweets: userData.tweet_count,\n      totalImpressions: Math.max(totalImpressions, dbImpressions),\n      totalEngagements: totalEngagements,\n      recentTweets: recentTweets.length,\n      username: userData.username,\n      name: userData.name,\n      verified: userData.verified,\n      profileImage: userData.profile_image_url\n    }\n    \n    return NextResponse.json(stats)\n  } catch (error) {\n    console.error('Error fetching Twitter stats:', error)\n    \n    // Return fallback data from database if Twitter API fails\n    try {\n      const latestAnalytics = await dbOperations.getLatestAnalytics()\n      const postedTweets = await dbOperations.getPostedTweets(1000)\n      \n      if (latestAnalytics) {\n        return NextResponse.json({\n          followers: latestAnalytics.followers_count,\n          totalTweets: latestAnalytics.total_tweets,\n          totalImpressions: latestAnalytics.total_impressions,\n          totalEngagements: latestAnalytics.total_engagements,\n          recentTweets: postedTweets.length,\n          username: 'N/A',\n          name: 'N/A',\n          verified: false,\n          profileImage: null\n        })\n      }\n    } catch (dbError) {\n      console.error('Error fetching fallback data:', dbError)\n    }\n    \n    return NextResponse.json(\n      { error: 'Failed to fetch Twitter stats' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST() {\n  try {\n    // Force refresh of Twitter stats\n    const userData = await twitterOperations.getCurrentUser()\n    const totalImpressions = await twitterOperations.getTotalImpressions()\n    \n    // Update analytics in database\n    await dbOperations.addAnalytics({\n      followers_count: userData.followers_count,\n      following_count: userData.following_count,\n      total_tweets: userData.tweet_count,\n      total_impressions: totalImpressions,\n      total_engagements: 0 // Will be calculated separately\n    })\n    \n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('Error refreshing Twitter stats:', error)\n    return NextResponse.json(\n      { error: 'Failed to refresh Twitter stats' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,IAAI;QACF,qCAAqC;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,oBAAiB,CAAC,cAAc;QAEvD,+CAA+C;QAC/C,MAAM,eAAe,MAAM,uHAAA,CAAA,oBAAiB,CAAC,eAAe;QAE5D,iDAAiD;QACjD,IAAI,mBAAmB;QACvB,KAAK,MAAM,SAAS,aAAc;YAChC,IAAI,MAAM,cAAc,EAAE;gBACxB,oBAAoB,CAAC,MAAM,cAAc,CAAC,UAAU,IAAI,CAAC,IACtC,CAAC,MAAM,cAAc,CAAC,aAAa,IAAI,CAAC,IACxC,CAAC,MAAM,cAAc,CAAC,WAAW,IAAI,CAAC,IACtC,CAAC,MAAM,cAAc,CAAC,WAAW,IAAI,CAAC;YAC3D;QACF;QAEA,oCAAoC;QACpC,MAAM,mBAAmB,MAAM,uHAAA,CAAA,oBAAiB,CAAC,mBAAmB;QAEpE,wCAAwC;QACxC,MAAM,eAAe,MAAM,wHAAA,CAAA,eAAY,CAAC,eAAe,CAAC;QAExD,wCAAwC;QACxC,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;QAEnF,sCAAsC;QACtC,MAAM,wHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;YAC9B,iBAAiB,SAAS,eAAe;YACzC,iBAAiB,SAAS,eAAe;YACzC,cAAc,SAAS,WAAW;YAClC,mBAAmB,KAAK,GAAG,CAAC,kBAAkB;YAC9C,mBAAmB;QACrB;QAEA,MAAM,QAAQ;YACZ,WAAW,SAAS,eAAe;YACnC,aAAa,SAAS,WAAW;YACjC,kBAAkB,KAAK,GAAG,CAAC,kBAAkB;YAC7C,kBAAkB;YAClB,cAAc,aAAa,MAAM;YACjC,UAAU,SAAS,QAAQ;YAC3B,MAAM,SAAS,IAAI;YACnB,UAAU,SAAS,QAAQ;YAC3B,cAAc,SAAS,iBAAiB;QAC1C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAE/C,0DAA0D;QAC1D,IAAI;YACF,MAAM,kBAAkB,MAAM,wHAAA,CAAA,eAAY,CAAC,kBAAkB;YAC7D,MAAM,eAAe,MAAM,wHAAA,CAAA,eAAY,CAAC,eAAe,CAAC;YAExD,IAAI,iBAAiB;gBACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,WAAW,gBAAgB,eAAe;oBAC1C,aAAa,gBAAgB,YAAY;oBACzC,kBAAkB,gBAAgB,iBAAiB;oBACnD,kBAAkB,gBAAgB,iBAAiB;oBACnD,cAAc,aAAa,MAAM;oBACjC,UAAU;oBACV,MAAM;oBACN,UAAU;oBACV,cAAc;gBAChB;YACF;QACF,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgC,GACzC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,IAAI;QACF,iCAAiC;QACjC,MAAM,WAAW,MAAM,uHAAA,CAAA,oBAAiB,CAAC,cAAc;QACvD,MAAM,mBAAmB,MAAM,uHAAA,CAAA,oBAAiB,CAAC,mBAAmB;QAEpE,+BAA+B;QAC/B,MAAM,wHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;YAC9B,iBAAiB,SAAS,eAAe;YACzC,iBAAiB,SAAS,eAAe;YACzC,cAAc,SAAS,WAAW;YAClC,mBAAmB;YACnB,mBAAmB,EAAE,gCAAgC;QACvD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}