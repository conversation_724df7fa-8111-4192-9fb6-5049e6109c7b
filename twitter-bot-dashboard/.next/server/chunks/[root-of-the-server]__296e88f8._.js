module.exports = {

"[project]/.next-internal/server/app/api/twitter/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "dbOperations": (()=>dbOperations),
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://fmhujzbqfzyyffgzwtzb.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceKey);
const dbOperations = {
    // RSS Feeds
    async getRSSFeeds () {
        const { data, error } = await supabase.from('rss_feeds').select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async addRSSFeed (name, url) {
        const { data, error } = await supabase.from('rss_feeds').insert({
            name,
            url
        }).select().single();
        if (error) throw error;
        return data;
    },
    async updateRSSFeed (id, updates) {
        const { data, error } = await supabase.from('rss_feeds').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Posted Tweets
    async getPostedTweets (limit = 50) {
        const { data, error } = await supabase.from('posted_tweets').select('*').order('posted_at', {
            ascending: false
        }).limit(limit);
        if (error) throw error;
        return data;
    },
    async addPostedTweet (tweet) {
        const { data, error } = await supabase.from('posted_tweets').insert(tweet).select().single();
        if (error) throw error;
        return data;
    },
    // Twitter Analytics
    async getLatestAnalytics () {
        const { data, error } = await supabase.from('twitter_analytics').select('*').order('recorded_at', {
            ascending: false
        }).limit(1).single();
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },
    async addAnalytics (analytics) {
        const { data, error } = await supabase.from('twitter_analytics').insert(analytics).select().single();
        if (error) throw error;
        return data;
    },
    // User Preferences
    async getUserPreferences () {
        const { data, error } = await supabase.from('user_preferences').select('*').limit(1).single();
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },
    async updateUserPreferences (updates) {
        const { data, error } = await supabase.from('user_preferences').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).select().single();
        if (error) throw error;
        return data;
    },
    // Content Queue
    async getContentQueue (limit = 20) {
        const { data, error } = await supabase.from('content_queue').select('*').order('created_at', {
            ascending: false
        }).limit(limit);
        if (error) throw error;
        return data;
    },
    async addToContentQueue (content) {
        const { data, error } = await supabase.from('content_queue').insert(content).select().single();
        if (error) throw error;
        return data;
    },
    async updateContentQueue (id, updates) {
        const { data, error } = await supabase.from('content_queue').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async getSelectedContent () {
        const { data, error } = await supabase.from('content_queue').select('*').eq('is_selected', true).eq('is_posted', false).order('priority_score', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async markContentAsPosted (ids) {
        const { data, error } = await supabase.from('content_queue').update({
            is_posted: true,
            is_selected: false,
            updated_at: new Date().toISOString()
        }).in('id', ids).select();
        if (error) throw error;
        return data;
    },
    // Delete RSS Feed
    async deleteRSSFeed (id) {
        const { error } = await supabase.from('rss_feeds').delete().eq('id', id);
        if (error) throw error;
        return true;
    },
    // Twitter Tokens Management
    async getTwitterTokens () {
        const { data, error } = await supabase.from('twitter_tokens').select('*').order('created_at', {
            ascending: false
        }).limit(1).single();
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },
    async saveTwitterTokens (tokens) {
        // First, try to update existing tokens
        const { data: existingData, error: selectError } = await supabase.from('twitter_tokens').select('id').limit(1).single();
        if (existingData) {
            // Update existing tokens
            const { data, error } = await supabase.from('twitter_tokens').update({
                ...tokens,
                updated_at: new Date().toISOString()
            }).eq('id', existingData.id).select().single();
            if (error) throw error;
            return data;
        } else {
            // Insert new tokens
            const { data, error } = await supabase.from('twitter_tokens').insert({
                ...tokens,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }).select().single();
            if (error) throw error;
            return data;
        }
    },
    async updateTwitterTokens (accessToken, refreshToken, expiresAt) {
        const { data: existingData, error: selectError } = await supabase.from('twitter_tokens').select('id').limit(1).single();
        if (!existingData) {
            throw new Error('No existing Twitter tokens found. Please authenticate first.');
        }
        const { data, error } = await supabase.from('twitter_tokens').update({
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_at: expiresAt,
            updated_at: new Date().toISOString()
        }).eq('id', existingData.id).select().single();
        if (error) throw error;
        return data;
    }
};
}}),
"[project]/src/lib/twitter-auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TwitterAuthManager": (()=>TwitterAuthManager),
    "twitterAuth": (()=>twitterAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$twitter$2d$api$2d$v2$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/twitter-api-v2/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$twitter$2d$api$2d$v2$2f$dist$2f$esm$2f$client$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/twitter-api-v2/dist/esm/client/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
;
;
class TwitterAuthManager {
    clientId;
    clientSecret;
    constructor(){
        this.clientId = process.env.TWITTER_CLIENT_ID;
        this.clientSecret = process.env.TWITTER_CLIENT_SECRET;
        if (!this.clientId || !this.clientSecret) {
            throw new Error('Twitter client credentials not found in environment variables');
        }
    }
    /**
   * Get a fresh Twitter client with valid tokens
   * This method handles token refresh automatically
   */ async getAuthenticatedClient() {
        try {
            // Get stored tokens from database
            const storedTokens = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getTwitterTokens();
            if (!storedTokens) {
                throw new Error('No Twitter tokens found in database. Please authenticate first using the OAuth flow.');
            }
            // Create OAuth2 client for token refresh
            const oauthClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$twitter$2d$api$2d$v2$2f$dist$2f$esm$2f$client$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["TwitterApi"]({
                clientId: this.clientId,
                clientSecret: this.clientSecret
            });
            // Refresh the tokens
            const refreshResult = await this.refreshTokens(oauthClient, storedTokens.refresh_token);
            return refreshResult.client;
        } catch (error) {
            console.error('Error getting authenticated Twitter client:', error);
            throw error;
        }
    }
    /**
   * Refresh Twitter OAuth2 tokens and update database
   */ async refreshTokens(oauthClient, refreshToken) {
        try {
            console.log('🔄 Refreshing Twitter OAuth2 tokens...');
            // Refresh the token
            const { client: rwClient, accessToken, refreshToken: newRefreshToken, expiresIn } = await oauthClient.refreshOAuth2Token(refreshToken);
            // Calculate expiration time if provided
            let expiresAt;
            if (expiresIn) {
                const expirationDate = new Date(Date.now() + expiresIn * 1000);
                expiresAt = expirationDate.toISOString();
            }
            // Update tokens in database
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].updateTwitterTokens(accessToken, newRefreshToken, expiresAt);
            console.log('✅ Twitter tokens refreshed and updated in database');
            console.log(`📅 New tokens expire at: ${expiresAt || 'Unknown'}`);
            return {
                client: rwClient,
                accessToken,
                refreshToken: newRefreshToken,
                expiresAt
            };
        } catch (error) {
            console.error('❌ Failed to refresh Twitter tokens:', error);
            // Provide helpful error messages
            if (error instanceof Error) {
                if (error.message.includes('invalid_grant')) {
                    throw new Error('Twitter refresh token is invalid or expired. Please re-authenticate using the OAuth flow.');
                } else if (error.message.includes('invalid_client')) {
                    throw new Error('Twitter client credentials are invalid. Please check TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET.');
                }
            }
            throw error;
        }
    }
    /**
   * Save initial tokens from OAuth flow
   */ async saveInitialTokens(accessToken, refreshToken, scope, expiresIn) {
        try {
            let expiresAt;
            if (expiresIn) {
                const expirationDate = new Date(Date.now() + expiresIn * 1000);
                expiresAt = expirationDate.toISOString();
            }
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].saveTwitterTokens({
                access_token: accessToken,
                refresh_token: refreshToken,
                expires_at: expiresAt,
                token_type: 'bearer',
                scope: scope
            });
            console.log('✅ Initial Twitter tokens saved to database');
        } catch (error) {
            console.error('❌ Failed to save initial Twitter tokens:', error);
            throw error;
        }
    }
    /**
   * Check if we have valid tokens stored
   */ async hasValidTokens() {
        try {
            const tokens = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getTwitterTokens();
            return !!tokens && !!tokens.access_token && !!tokens.refresh_token;
        } catch (error) {
            console.error('Error checking token validity:', error);
            return false;
        }
    }
    /**
   * Get current token status for debugging
   */ async getTokenStatus() {
        try {
            const tokens = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getTwitterTokens();
            if (!tokens) {
                return {
                    hasTokens: false
                };
            }
            return {
                hasTokens: true,
                expiresAt: tokens.expires_at,
                scope: tokens.scope,
                lastUpdated: tokens.updated_at
            };
        } catch (error) {
            console.error('Error getting token status:', error);
            return {
                hasTokens: false
            };
        }
    }
}
const twitterAuth = new TwitterAuthManager();
}}),
"[project]/src/lib/twitter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getTwitterClient": (()=>getTwitterClient),
    "getTwitterV1Client": (()=>getTwitterV1Client),
    "twitterOperations": (()=>twitterOperations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$twitter$2d$api$2d$v2$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/twitter-api-v2/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$twitter$2d$api$2d$v2$2f$dist$2f$esm$2f$client$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/twitter-api-v2/dist/esm/client/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/twitter-auth.ts [app-route] (ecmascript)");
;
;
// Cached Twitter client instance
let twitterClientInstance = null;
// Twitter client configuration - only initialize if keys are available
function getTwitterClient() {
    // Create new instance and cache it
    twitterClientInstance = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$twitter$2d$api$2d$v2$2f$dist$2f$esm$2f$client$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["TwitterApi"]({
        clientId: process.env.TWITTER_CLIENT_ID,
        clientSecret: process.env.TWITTER_CLIENT_SECRET,
        accessToken: process.env.TWITTER_ACCESS_TOKEN,
        refreshToken: process.env.TWITTER_REFRESH_TOKEN
    });
    return twitterClientInstance;
}
// Cached Twitter V1 client instance
let twitterV1ClientInstance = null;
// Twitter API v1.1 client for additional features
function getTwitterV1Client() {
    // Return cached instance if available
    if (twitterV1ClientInstance) {
        return twitterV1ClientInstance;
    }
    twitterV1ClientInstance = getTwitterClient();
    return twitterV1ClientInstance;
}
const twitterOperations = {
    // Get current user information
    async getCurrentUser () {
        try {
            const twitterClient = getTwitterClient();
            const user = await twitterClient.v2.me({
                'user.fields': [
                    'public_metrics',
                    'verified',
                    'profile_image_url'
                ]
            });
            return {
                id: user.data.id,
                username: user.data.username,
                name: user.data.name,
                followers_count: user.data.public_metrics?.followers_count || 0,
                following_count: user.data.public_metrics?.following_count || 0,
                tweet_count: user.data.public_metrics?.tweet_count || 0,
                verified: user.data.verified || false,
                profile_image_url: user.data.profile_image_url
            };
        } catch (error) {
            console.error('Error fetching current user:', error);
            // Return mock data when Twitter API fails
            return {
                id: 'mock_user_id',
                username: 'twitter_bot',
                name: 'Twitter Bot',
                followers_count: 150,
                following_count: 50,
                tweet_count: 25,
                verified: false,
                profile_image_url: undefined
            };
        }
    },
    // Post a tweet
    async postTweet (content) {
        try {
            // Get authenticated Twitter client (handles token refresh automatically)
            const twitterClient = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twitterAuth"].getAuthenticatedClient();
            // Post the tweet using the authenticated client
            const tweet = await twitterClient.v2.tweet(content);
            return {
                id: tweet.data.id,
                text: content,
                created_at: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error posting tweet:', error);
            // Return mock tweet data when Twitter API fails
            return {
                id: `mock_tweet_${Date.now()}`,
                text: content,
                created_at: new Date().toISOString()
            };
        }
    },
    // Get recent tweets with metrics
    async getRecentTweets () {
        try {
            // Return mock data for now
            return [
                {
                    id: 'mock_tweet_1',
                    text: 'Sample tweet about technology trends',
                    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                    public_metrics: {
                        retweet_count: 5,
                        like_count: 12,
                        reply_count: 3,
                        quote_count: 1,
                        impression_count: 150
                    }
                },
                {
                    id: 'mock_tweet_2',
                    text: 'Another interesting tweet about AI developments',
                    created_at: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
                    public_metrics: {
                        retweet_count: 8,
                        like_count: 20,
                        reply_count: 5,
                        quote_count: 2,
                        impression_count: 280
                    }
                }
            ];
        } catch (error) {
            console.error('Error fetching recent tweets:', error);
            return [];
        }
    },
    // Get tweet analytics
    async getTweetAnalytics (tweetId) {
        try {
            const twitterClient = getTwitterClient();
            const tweet = await twitterClient.v2.singleTweet(tweetId, {
                'tweet.fields': [
                    'created_at',
                    'public_metrics'
                ]
            });
            if (!tweet.data) return null;
            return {
                id: tweet.data.id,
                text: tweet.data.text,
                created_at: tweet.data.created_at || new Date().toISOString(),
                public_metrics: tweet.data.public_metrics
            };
        } catch (error) {
            console.error('Error fetching tweet analytics:', error);
            return null;
        }
    },
    // Calculate total impressions from recent tweets
    async getTotalImpressions () {
        try {
            const tweets = await this.getRecentTweets() // Get more tweets for better calculation
            ;
            // Filter tweets from the last 30 days
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 30);
            const recentTweets = tweets.filter((tweet)=>new Date(tweet.created_at) >= cutoffDate);
            // Sum up impressions (if available) or estimate based on engagement
            let totalImpressions = 0;
            for (const tweet of recentTweets){
                if (tweet.public_metrics?.impression_count) {
                    totalImpressions += tweet.public_metrics.impression_count;
                } else if (tweet.public_metrics) {
                    // Estimate impressions based on engagement (rough calculation)
                    const engagement = (tweet.public_metrics.like_count || 0) + (tweet.public_metrics.retweet_count || 0) + (tweet.public_metrics.reply_count || 0);
                    totalImpressions += Math.max(engagement * 10, 100) // Rough estimate
                    ;
                }
            }
            return totalImpressions;
        } catch (error) {
            console.error('Error calculating total impressions:', error);
            return 0;
        }
    },
    // Get authentication status
    async getAuthStatus () {
        try {
            await this.getCurrentUser();
            return {
                authenticated: true
            };
        } catch (error) {
            console.error('Error checking auth status:', error);
            return {
                authenticated: false
            };
        }
    }
};
;
}}),
"[project]/src/app/api/twitter/stats/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/twitter.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
;
;
;
async function GET() {
    try {
        // Get current user data from Twitter
        const userData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twitterOperations"].getCurrentUser();
        // Get recent tweets for engagement calculation
        const recentTweets = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twitterOperations"].getRecentTweets();
        // Calculate total engagements from recent tweets
        let totalEngagements = 0;
        for (const tweet of recentTweets){
            if (tweet.public_metrics) {
                totalEngagements += (tweet.public_metrics.like_count || 0) + (tweet.public_metrics.retweet_count || 0) + (tweet.public_metrics.reply_count || 0) + (tweet.public_metrics.quote_count || 0);
            }
        }
        // Get total impressions (estimated)
        const totalImpressions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twitterOperations"].getTotalImpressions();
        // Get posted tweets count from database
        const postedTweets = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getPostedTweets(1000);
        // Calculate database-stored impressions
        const dbImpressions = postedTweets.reduce((sum, tweet)=>sum + tweet.impressions, 0);
        // Store current analytics in database
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].addAnalytics({
            followers_count: userData.followers_count,
            following_count: userData.following_count,
            total_tweets: userData.tweet_count,
            total_impressions: Math.max(totalImpressions, dbImpressions),
            total_engagements: totalEngagements
        });
        const stats = {
            followers: userData.followers_count,
            totalTweets: userData.tweet_count,
            totalImpressions: Math.max(totalImpressions, dbImpressions),
            totalEngagements: totalEngagements,
            recentTweets: recentTweets.length,
            username: userData.username,
            name: userData.name,
            verified: userData.verified,
            profileImage: userData.profile_image_url
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(stats);
    } catch (error) {
        console.error('Error fetching Twitter stats:', error);
        // Return fallback data from database if Twitter API fails
        try {
            const latestAnalytics = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getLatestAnalytics();
            const postedTweets = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getPostedTweets(1000);
            if (latestAnalytics) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    followers: latestAnalytics.followers_count,
                    totalTweets: latestAnalytics.total_tweets,
                    totalImpressions: latestAnalytics.total_impressions,
                    totalEngagements: latestAnalytics.total_engagements,
                    recentTweets: postedTweets.length,
                    username: 'N/A',
                    name: 'N/A',
                    verified: false,
                    profileImage: null
                });
            }
        } catch (dbError) {
            console.error('Error fetching fallback data:', dbError);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch Twitter stats'
        }, {
            status: 500
        });
    }
}
async function POST() {
    try {
        // Force refresh of Twitter stats
        const userData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twitterOperations"].getCurrentUser();
        const totalImpressions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twitterOperations"].getTotalImpressions();
        // Update analytics in database
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].addAnalytics({
            followers_count: userData.followers_count,
            following_count: userData.following_count,
            total_tweets: userData.tweet_count,
            total_impressions: totalImpressions,
            total_engagements: 0 // Will be calculated separately
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('Error refreshing Twitter stats:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to refresh Twitter stats'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__296e88f8._.js.map