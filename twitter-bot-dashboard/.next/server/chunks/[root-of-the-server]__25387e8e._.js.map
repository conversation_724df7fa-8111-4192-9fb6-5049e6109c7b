{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter-auth.ts"], "sourcesContent": ["import { dbOperations } from './supabase';\nimport { <PERSON><PERSON><PERSON> } from 'twitter-api-v2';\n\nexport interface TwitterTokenRefreshResult {\n  client: any // TwitterApi\n  accessToken: string\n  refreshToken: string\n  expiresAt?: string\n}\n\nexport class TwitterAuthManager {\n  private clientId: string\n  private clientSecret: string\n  private oauthClient: any\n\n\n  constructor() {\n    this.clientId = process.env.TWITTER_CLIENT_ID!\n    this.clientSecret = process.env.TWITTER_CLIENT_SECRET!\n    this.oauthClient = new TwitterApi({\n        clientId: this.clientId,\n        clientSecret: this.clientSecret,\n      } as any)\n \n    if (!this.clientId || !this.clientSecret) {\n      throw new Error('Twitter client credentials not found in environment variables')\n    }\n  }\n\n  /**\n   * Get a fresh Twitter client with valid tokens\n   * This method handles token refresh automatically\n   */\n  async getAuthenticatedClient(): Promise<any> {\n    try {\n      // Get stored tokens from database\n      const storedTokens = await dbOperations.getTwitterTokens()\n      \n      if (!storedTokens) {\n        throw new Error('No Twitter tokens found in database. Please authenticate first using the OAuth flow.')\n      }\n\n\n      // Refresh the tokens\n      const refreshResult = await this.refreshTokens(this.oauthClient, storedTokens.refresh_token)\n      \n      return refreshResult.client\n    } catch (error) {\n      console.error('Error getting authenticated Twitter client:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Refresh Twitter OAuth2 tokens and update database\n   */\n  private async refreshTokens(oauthClient: any, refreshToken: string): Promise<TwitterTokenRefreshResult> {\n    try {\n      console.log('🔄 Refreshing Twitter OAuth2 tokens...')\n      \n      // Refresh the token\n      const { client: rwClient, accessToken, refreshToken: newRefreshToken, expiresIn } = await oauthClient.refreshOAuth2Token(refreshToken)\n\n      // Validate that we got a new refresh token\n      if (!newRefreshToken) {\n        throw new Error('No refresh token returned from Twitter OAuth2 refresh')\n      }\n\n      // Calculate expiration time if provided\n      let expiresAt: string | undefined\n      if (expiresIn) {\n        const expirationDate = new Date(Date.now() + (expiresIn * 1000))\n        expiresAt = expirationDate.toISOString()\n      }\n\n      // Update tokens in database\n      await dbOperations.updateTwitterTokens(accessToken, newRefreshToken, expiresAt)\n\n      console.log('✅ Twitter tokens refreshed and updated in database')\n      console.log(`📅 New tokens expire at: ${expiresAt || 'Unknown'}`)\n\n      return {\n        client: rwClient,\n        accessToken,\n        refreshToken: newRefreshToken,\n        expiresAt\n      }\n    } catch (error) {\n      console.error('❌ Failed to refresh Twitter tokens:', error)\n      \n      // Provide helpful error messages\n      if (error instanceof Error) {\n        if (error.message.includes('invalid_grant')) {\n          throw new Error('Twitter refresh token is invalid or expired. Please re-authenticate using the OAuth flow.')\n        } else if (error.message.includes('invalid_client')) {\n          throw new Error('Twitter client credentials are invalid. Please check TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET.')\n        }\n      }\n      \n      throw error\n    }\n  }\n\n  /**\n   * Save initial tokens from OAuth flow\n   */\n  async saveInitialTokens(accessToken: string, refreshToken: string, scope?: string, expiresIn?: number): Promise<void> {\n    try {\n      let expiresAt: string | undefined\n      if (expiresIn) {\n        const expirationDate = new Date(Date.now() + (expiresIn * 1000))\n        expiresAt = expirationDate.toISOString()\n      }\n\n      await dbOperations.saveTwitterTokens({\n        access_token: accessToken,\n        refresh_token: refreshToken,\n        expires_at: expiresAt,\n        token_type: 'bearer',\n        scope: scope\n      })\n\n      console.log('✅ Initial Twitter tokens saved to database')\n    } catch (error) {\n      console.error('❌ Failed to save initial Twitter tokens:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Check if we have valid tokens stored\n   */\n  async hasValidTokens(): Promise<boolean> {\n    try {\n      const tokens = await dbOperations.getTwitterTokens()\n      return !!tokens && !!tokens.access_token && !!tokens.refresh_token\n    } catch (error) {\n      console.error('Error checking token validity:', error)\n      return false\n    }\n  }\n\n  /**\n   * Get current token status for debugging\n   */\n  async getTokenStatus(): Promise<{\n    hasTokens: boolean\n    expiresAt?: string\n    scope?: string\n    lastUpdated?: string\n  }> {\n    try {\n      const tokens = await dbOperations.getTwitterTokens()\n      \n      if (!tokens) {\n        return { hasTokens: false }\n      }\n\n      return {\n        hasTokens: true,\n        expiresAt: tokens.expires_at,\n        scope: tokens.scope,\n        lastUpdated: tokens.updated_at\n      }\n    } catch (error) {\n      console.error('Error getting token status:', error)\n      return { hasTokens: false }\n    }\n  }\n}\n\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AASO,MAAM;IACH,SAAgB;IAChB,aAAoB;IACpB,YAAgB;IAGxB,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,iBAAiB;QAC7C,IAAI,CAAC,YAAY,GAAG,QAAQ,GAAG,CAAC,qBAAqB;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,wLAAA,CAAA,aAAU,CAAC;YAC9B,UAAU,IAAI,CAAC,QAAQ;YACvB,cAAc,IAAI,CAAC,YAAY;QACjC;QAEF,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;GAGC,GACD,MAAM,yBAAuC;QAC3C,IAAI;YACF,kCAAkC;YAClC,MAAM,eAAe,MAAM,wHAAA,CAAA,eAAY,CAAC,gBAAgB;YAExD,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAGA,qBAAqB;YACrB,MAAM,gBAAgB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,aAAa;YAE3F,OAAO,cAAc,MAAM;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAc,cAAc,WAAgB,EAAE,YAAoB,EAAsC;QACtG,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,oBAAoB;YACpB,MAAM,EAAE,QAAQ,QAAQ,EAAE,WAAW,EAAE,cAAc,eAAe,EAAE,SAAS,EAAE,GAAG,MAAM,YAAY,kBAAkB,CAAC;YAEzH,2CAA2C;YAC3C,IAAI,CAAC,iBAAiB;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,wCAAwC;YACxC,IAAI;YACJ,IAAI,WAAW;gBACb,MAAM,iBAAiB,IAAI,KAAK,KAAK,GAAG,KAAM,YAAY;gBAC1D,YAAY,eAAe,WAAW;YACxC;YAEA,4BAA4B;YAC5B,MAAM,wHAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,aAAa,iBAAiB;YAErE,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,aAAa,WAAW;YAEhE,OAAO;gBACL,QAAQ;gBACR;gBACA,cAAc;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YAErD,iCAAiC;YACjC,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBAC3C,MAAM,IAAI,MAAM;gBAClB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,mBAAmB;oBACnD,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,WAAmB,EAAE,YAAoB,EAAE,KAAc,EAAE,SAAkB,EAAiB;QACpH,IAAI;YACF,IAAI;YACJ,IAAI,WAAW;gBACb,MAAM,iBAAiB,IAAI,KAAK,KAAK,GAAG,KAAM,YAAY;gBAC1D,YAAY,eAAe,WAAW;YACxC;YAEA,MAAM,wHAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC;gBACnC,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,YAAY;gBACZ,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,SAAS,MAAM,wHAAA,CAAA,eAAY,CAAC,gBAAgB;YAClD,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,YAAY,IAAI,CAAC,CAAC,OAAO,aAAa;QACpE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,iBAKH;QACD,IAAI;YACF,MAAM,SAAS,MAAM,wHAAA,CAAA,eAAY,CAAC,gBAAgB;YAElD,IAAI,CAAC,QAAQ;gBACX,OAAO;oBAAE,WAAW;gBAAM;YAC5B;YAEA,OAAO;gBACL,WAAW;gBACX,WAAW,OAAO,UAAU;gBAC5B,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,UAAU;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBAAE,WAAW;YAAM;QAC5B;IACF;AACF", "debugId": null}}]}