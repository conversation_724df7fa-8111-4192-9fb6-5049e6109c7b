module.exports = {

"[project]/.next-internal/server/app/api/content/post/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "dbOperations": (()=>dbOperations),
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://fmhujzbqfzyyffgzwtzb.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceKey);
const dbOperations = {
    // RSS Feeds
    async getRSSFeeds () {
        const { data, error } = await supabase.from('rss_feeds').select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async addRSSFeed (name, url) {
        const { data, error } = await supabase.from('rss_feeds').insert({
            name,
            url
        }).select().single();
        if (error) throw error;
        return data;
    },
    async updateRSSFeed (id, updates) {
        const { data, error } = await supabase.from('rss_feeds').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // Posted Tweets
    async getPostedTweets (limit = 50) {
        const { data, error } = await supabase.from('posted_tweets').select('*').order('posted_at', {
            ascending: false
        }).limit(limit);
        if (error) throw error;
        return data;
    },
    async addPostedTweet (tweet) {
        const { data, error } = await supabase.from('posted_tweets').insert(tweet).select().single();
        if (error) throw error;
        return data;
    },
    // Twitter Analytics
    async getLatestAnalytics () {
        const { data, error } = await supabase.from('twitter_analytics').select('*').order('recorded_at', {
            ascending: false
        }).limit(1).single();
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },
    async addAnalytics (analytics) {
        const { data, error } = await supabase.from('twitter_analytics').insert(analytics).select().single();
        if (error) throw error;
        return data;
    },
    // User Preferences
    async getUserPreferences () {
        const { data, error } = await supabase.from('user_preferences').select('*').limit(1).single();
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },
    async updateUserPreferences (updates) {
        const { data, error } = await supabase.from('user_preferences').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).select().single();
        if (error) throw error;
        return data;
    },
    // Content Queue
    async getContentQueue (limit = 20) {
        const { data, error } = await supabase.from('content_queue').select('*').order('created_at', {
            ascending: false
        }).limit(limit);
        if (error) throw error;
        return data;
    },
    async addToContentQueue (content) {
        const { data, error } = await supabase.from('content_queue').insert(content).select().single();
        if (error) throw error;
        return data;
    },
    async updateContentQueue (id, updates) {
        const { data, error } = await supabase.from('content_queue').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async getSelectedContent () {
        const { data, error } = await supabase.from('content_queue').select('*').eq('is_selected', true).eq('is_posted', false).order('priority_score', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async markContentAsPosted (ids) {
        const { data, error } = await supabase.from('content_queue').update({
            is_posted: true,
            is_selected: false,
            updated_at: new Date().toISOString()
        }).in('id', ids).select();
        if (error) throw error;
        return data;
    },
    // Delete RSS Feed
    async deleteRSSFeed (id) {
        const { error } = await supabase.from('rss_feeds').delete().eq('id', id);
        if (error) throw error;
        return true;
    },
    // Twitter Tokens Management
    async getTwitterTokens () {
        const { data, error } = await supabase.from('twitter_tokens').select('*').order('created_at', {
            ascending: false
        }).limit(1).single();
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },
    async saveTwitterTokens (tokens) {
        // First, try to update existing tokens
        const { data: existingData, error: selectError } = await supabase.from('twitter_tokens').select('id').limit(1).single();
        if (existingData) {
            // Update existing tokens
            const { data, error } = await supabase.from('twitter_tokens').update({
                ...tokens,
                updated_at: new Date().toISOString()
            }).eq('id', existingData.id).select().single();
            if (error) throw error;
            return data;
        } else {
            // Insert new tokens
            const { data, error } = await supabase.from('twitter_tokens').insert({
                ...tokens,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }).select().single();
            if (error) throw error;
            return data;
        }
    },
    async updateTwitterTokens (accessToken, refreshToken, expiresAt) {
        const { data: existingData, error: selectError } = await supabase.from('twitter_tokens').select('id').limit(1).single();
        if (!existingData) {
            throw new Error('No existing Twitter tokens found. Please authenticate first.');
        }
        const { data, error } = await supabase.from('twitter_tokens').update({
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_at: expiresAt,
            updated_at: new Date().toISOString()
        }).eq('id', existingData.id).select().single();
        if (error) throw error;
        return data;
    }
};
}}),
"[project]/src/lib/twitter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Twitter client configuration - now uses database-managed tokens
__turbopack_context__.s({
    "getTwitterClient": (()=>getTwitterClient),
    "getTwitterV1Client": (()=>getTwitterV1Client),
    "twitterOperations": (()=>twitterOperations)
});
async function getTwitterClient() {
    // Import here to avoid circular dependency
    const { twitterAuth } = await __turbopack_context__.r("[project]/src/lib/twitter-auth.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
    return await twitterAuth.getAuthenticatedClient();
}
// Twitter API v1.1 client for additional features
async function getTwitterV1Client() {
    return await getTwitterClient();
}
const twitterOperations = {
    // Get current user information
    async getCurrentUser () {
        try {
            const twitterClient = await getTwitterClient();
            const user = await twitterClient.v2.me({
                'user.fields': [
                    'public_metrics',
                    'verified',
                    'profile_image_url'
                ]
            });
            return {
                id: user.data.id,
                username: user.data.username,
                name: user.data.name,
                followers_count: user.data.public_metrics?.followers_count || 0,
                following_count: user.data.public_metrics?.following_count || 0,
                tweet_count: user.data.public_metrics?.tweet_count || 0,
                verified: user.data.verified || false,
                profile_image_url: user.data.profile_image_url
            };
        } catch (error) {
            console.error('Error fetching current user:', error);
            // Return mock data when Twitter API fails
            return {
                id: 'mock_user_id',
                username: 'twitter_bot',
                name: 'Twitter Bot',
                followers_count: 150,
                following_count: 50,
                tweet_count: 25,
                verified: false,
                profile_image_url: undefined
            };
        }
    },
    // Post a tweet
    async postTweet (content) {
        try {
            // Get authenticated Twitter client (handles token refresh automatically)
            const { twitterAuth } = await __turbopack_context__.r("[project]/src/lib/twitter-auth.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const twitterClient = await twitterAuth.getAuthenticatedClient();
            // Post the tweet using the authenticated client
            const tweet = await twitterClient.v2.tweet(content);
            return {
                id: tweet.data.id,
                text: content,
                created_at: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error posting tweet:', error);
            // Return mock tweet data when Twitter API fails
            return {
                id: `mock_tweet_${Date.now()}`,
                text: content,
                created_at: new Date().toISOString()
            };
        }
    },
    // Get recent tweets with metrics
    async getRecentTweets () {
        try {
            // Return mock data for now
            return [
                {
                    id: 'mock_tweet_1',
                    text: 'Sample tweet about technology trends',
                    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                    public_metrics: {
                        retweet_count: 5,
                        like_count: 12,
                        reply_count: 3,
                        quote_count: 1,
                        impression_count: 150
                    }
                },
                {
                    id: 'mock_tweet_2',
                    text: 'Another interesting tweet about AI developments',
                    created_at: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
                    public_metrics: {
                        retweet_count: 8,
                        like_count: 20,
                        reply_count: 5,
                        quote_count: 2,
                        impression_count: 280
                    }
                }
            ];
        } catch (error) {
            console.error('Error fetching recent tweets:', error);
            return [];
        }
    },
    // Get tweet analytics
    async getTweetAnalytics (tweetId) {
        try {
            const twitterClient = await getTwitterClient();
            const tweet = await twitterClient.v2.singleTweet(tweetId, {
                'tweet.fields': [
                    'created_at',
                    'public_metrics'
                ]
            });
            if (!tweet.data) return null;
            return {
                id: tweet.data.id,
                text: tweet.data.text,
                created_at: tweet.data.created_at || new Date().toISOString(),
                public_metrics: tweet.data.public_metrics
            };
        } catch (error) {
            console.error('Error fetching tweet analytics:', error);
            return null;
        }
    },
    // Calculate total impressions from recent tweets
    async getTotalImpressions () {
        try {
            const tweets = await this.getRecentTweets() // Get more tweets for better calculation
            ;
            // Filter tweets from the last 30 days
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 30);
            const recentTweets = tweets.filter((tweet)=>new Date(tweet.created_at) >= cutoffDate);
            // Sum up impressions (if available) or estimate based on engagement
            let totalImpressions = 0;
            for (const tweet of recentTweets){
                if (tweet.public_metrics?.impression_count) {
                    totalImpressions += tweet.public_metrics.impression_count;
                } else if (tweet.public_metrics) {
                    // Estimate impressions based on engagement (rough calculation)
                    const engagement = (tweet.public_metrics.like_count || 0) + (tweet.public_metrics.retweet_count || 0) + (tweet.public_metrics.reply_count || 0);
                    totalImpressions += Math.max(engagement * 10, 100) // Rough estimate
                    ;
                }
            }
            return totalImpressions;
        } catch (error) {
            console.error('Error calculating total impressions:', error);
            return 0;
        }
    },
    // Get authentication status
    async getAuthStatus () {
        try {
            await this.getCurrentUser();
            return {
                authenticated: true
            };
        } catch (error) {
            console.error('Error checking auth status:', error);
            return {
                authenticated: false
            };
        }
    }
};
;
}}),
"[project]/src/app/api/content/post/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/twitter.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const { contentIds } = await request.json();
        if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Content IDs array is required'
            }, {
                status: 400
            });
        }
        // Get selected content items
        const contentQueue = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getContentQueue(100);
        const selectedItems = contentQueue.filter((item)=>contentIds.includes(item.id) && !item.is_posted);
        if (selectedItems.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No valid content items found'
            }, {
                status: 400
            });
        }
        let posted = 0;
        const results = [];
        for (const item of selectedItems){
            try {
                // Use AI-generated content if available, otherwise create basic tweet
                const tweetContent = item.ai_generated_content || `${item.original_title}\n\n${item.original_url}`;
                // Post to Twitter
                const tweetData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$twitter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twitterOperations"].postTweet(tweetContent);
                // Save to posted tweets database
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].addPostedTweet({
                    tweet_id: tweetData.id,
                    content: tweetContent,
                    original_url: item.original_url,
                    original_title: item.original_title,
                    impressions: 0,
                    retweets: 0,
                    likes: 0,
                    replies: 0,
                    posted_at: tweetData.created_at
                });
                posted++;
                results.push({
                    id: item.id,
                    success: true,
                    tweetId: tweetData.id
                });
                // Add delay between posts to avoid rate limiting
                if (posted < selectedItems.length) {
                    await new Promise((resolve)=>setTimeout(resolve, 2000));
                }
            } catch (error) {
                console.error(`Error posting content ${item.id}:`, error);
                results.push({
                    id: item.id,
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
        // Mark successfully posted items as posted
        const successfulIds = results.filter((r)=>r.success).map((r)=>r.id);
        if (successfulIds.length > 0) {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].markContentAsPosted(successfulIds);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            posted,
            total: selectedItems.length,
            results
        });
    } catch (error) {
        console.error('Error posting content:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to post content'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3dfc000d._.js.map