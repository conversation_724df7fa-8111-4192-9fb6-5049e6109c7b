{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Client for browser/client-side operations\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Admin client for server-side operations\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)\n\n// Database types\nexport interface RSSFeed {\n  id: string\n  name: string\n  url: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface PostedTweet {\n  id: string\n  tweet_id?: string\n  content: string\n  original_url?: string\n  original_title?: string\n  rss_feed_id?: string\n  impressions: number\n  retweets: number\n  likes: number\n  replies: number\n  posted_at: string\n  created_at: string\n}\n\nexport interface TwitterAnalytics {\n  id: string\n  followers_count: number\n  following_count: number\n  total_tweets: number\n  total_impressions: number\n  total_engagements: number\n  recorded_at: string\n}\n\nexport interface UserPreferences {\n  id: string\n  max_topics_to_select: number\n  posting_interval_minutes: number\n  ai_tone: string\n  include_personal_touch: boolean\n  auto_post_enabled: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface TwitterTokens {\n  id: string\n  access_token: string\n  refresh_token: string\n  expires_at?: string\n  token_type: string\n  scope?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ContentQueue {\n  id: string\n  original_url: string\n  original_title?: string\n  original_content?: string\n  ai_generated_content?: string\n  short_hook?: string\n  long_hook?: string\n  personal_touch?: string\n  rss_feed_id?: string\n  is_selected: boolean\n  is_posted: boolean\n  priority_score: number\n  created_at: string\n  updated_at: string\n}\n\n// Database operations\nexport const dbOperations = {\n  // RSS Feeds\n  async getRSSFeeds() {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .select('*')\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data as RSSFeed[]\n  },\n\n  async addRSSFeed(name: string, url: string) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .insert({ name, url })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  async updateRSSFeed(id: string, updates: Partial<RSSFeed>) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  // Posted Tweets\n  async getPostedTweets(limit = 50) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .select('*')\n      .order('posted_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as PostedTweet[]\n  },\n\n  async addPostedTweet(tweet: Omit<PostedTweet, 'id' | 'created_at'>) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .insert(tweet)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as PostedTweet\n  },\n\n  // Twitter Analytics\n  async getLatestAnalytics() {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .select('*')\n      .order('recorded_at', { ascending: false })\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as TwitterAnalytics | null\n  },\n\n  async addAnalytics(analytics: Omit<TwitterAnalytics, 'id' | 'recorded_at'>) {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .insert(analytics)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as TwitterAnalytics\n  },\n\n  // User Preferences\n  async getUserPreferences() {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .select('*')\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as UserPreferences | null\n  },\n\n  async updateUserPreferences(updates: Partial<UserPreferences>) {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as UserPreferences\n  },\n\n  // Content Queue\n  async getContentQueue(limit = 20) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async addToContentQueue(content: Omit<ContentQueue, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .insert(content)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async updateContentQueue(id: string, updates: Partial<ContentQueue>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async getSelectedContent() {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .eq('is_selected', true)\n      .eq('is_posted', false)\n      .order('priority_score', { ascending: false })\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async markContentAsPosted(ids: string[]) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({\n        is_posted: true,\n        is_selected: false,\n        updated_at: new Date().toISOString()\n      })\n      .in('id', ids)\n      .select()\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  // Delete RSS Feed\n  async deleteRSSFeed(id: string) {\n    const { error } = await supabase\n      .from('rss_feeds')\n      .delete()\n      .eq('id', id)\n\n    if (error) throw error\n    return true\n  },\n\n  // Twitter Tokens Management\n  async getTwitterTokens(): Promise<TwitterTokens | null> {\n    const { data, error } = await supabase\n      .from('twitter_tokens')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(1)\n      .single()\n\n    if (error && error.code !== 'PGRST116') throw error\n    return data as TwitterTokens | null\n  },\n\n  async saveTwitterTokens(tokens: Omit<TwitterTokens, 'id' | 'created_at' | 'updated_at'>): Promise<TwitterTokens> {\n    // First, try to update existing tokens\n    const { data: existingData, error: selectError } = await supabase\n      .from('twitter_tokens')\n      .select('id')\n      .limit(1)\n      .single()\n\n    if (existingData) {\n      // Update existing tokens\n      const { data, error } = await supabase\n        .from('twitter_tokens')\n        .update({\n          ...tokens,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', existingData.id)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data as TwitterTokens\n    } else {\n      // Insert new tokens\n      const { data, error } = await supabase\n        .from('twitter_tokens')\n        .insert({\n          ...tokens,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        })\n        .select()\n        .single()\n\n      if (error) throw error\n      return data as TwitterTokens\n    }\n  },\n\n  async updateTwitterTokens(accessToken: string, refreshToken: string, expiresAt?: string): Promise<TwitterTokens> {\n    const { data: existingData, error: selectError } = await supabase\n      .from('twitter_tokens')\n      .select('id')\n      .limit(1)\n      .single()\n\n    if (!existingData) {\n      throw new Error('No existing Twitter tokens found. Please authenticate first.')\n    }\n\n    const { data, error } = await supabase\n      .from('twitter_tokens')\n      .update({\n        access_token: accessToken,\n        refresh_token: refreshToken,\n        expires_at: expiresAt,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', existingData.id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as TwitterTokens\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAGzD,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AA6EhD,MAAM,eAAe;IAC1B,YAAY;IACZ,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,YAAW,IAAY,EAAE,GAAW;QACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE;YAAM;QAAI,GACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,EAAU,EAAE,OAAyB;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM,GACtC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,KAA6C;QAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,eAAe;YAAE,WAAW;QAAM,GACxC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,cAAa,SAAuD;QACxE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,uBAAsB,OAAiC;QAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,mBAAkB,OAA+D;QACrF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,oBAAmB,EAAU,EAAE,OAA8B;QACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,MAClB,EAAE,CAAC,aAAa,OAChB,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;QAE9C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,qBAAoB,GAAa;QACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,WAAW;YACX,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,KACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,mBAAkB,MAA+D;QACrF,uCAAuC;QACvC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,cAAc;YAChB,yBAAyB;YACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,GAAG,MAAM;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT,OAAO;YACL,oBAAoB;YACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,GAAG,MAAM;gBACT,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;IACF;IAEA,MAAM,qBAAoB,WAAmB,EAAE,YAAoB,EAAE,SAAkB;QACrF,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;YACN,cAAc;YACd,eAAe;YACf,YAAY;YACZ,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter-auth.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from 'twitter-api-v2'\nimport { dbOperations } from './supabase'\n\nexport interface TwitterTokenRefreshResult {\n  client: TwitterApi\n  accessToken: string\n  refreshToken: string\n  expiresAt?: string\n}\n\nexport class TwitterAuthManager {\n  private clientId: string\n  private clientSecret: string\n\n  constructor() {\n    this.clientId = process.env.TWITTER_CLIENT_ID!\n    this.clientSecret = process.env.TWITTER_CLIENT_SECRET!\n    \n    if (!this.clientId || !this.clientSecret) {\n      throw new Error('Twitter client credentials not found in environment variables')\n    }\n  }\n\n  /**\n   * Get a fresh Twitter client with valid tokens\n   * This method handles token refresh automatically\n   */\n  async getAuthenticatedClient(): Promise<TwitterApi> {\n    try {\n      // Get stored tokens from database\n      const storedTokens = await dbOperations.getTwitterTokens()\n      \n      if (!storedTokens) {\n        throw new Error('No Twitter tokens found in database. Please authenticate first using the OAuth flow.')\n      }\n\n      // Create OAuth2 client for token refresh\n      const oauthClient = new Twitter<PERSON>pi({\n        clientId: this.clientId,\n        clientSecret: this.clientSecret,\n      } as any)\n\n      // Refresh the tokens\n      const refreshResult = await this.refreshTokens(oauthClient, storedTokens.refresh_token)\n      \n      return refreshResult.client\n    } catch (error) {\n      console.error('Error getting authenticated Twitter client:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Refresh Twitter OAuth2 tokens and update database\n   */\n  private async refreshTokens(oauthClient: TwitterApi, refreshToken: string): Promise<TwitterTokenRefreshResult> {\n    try {\n      console.log('🔄 Refreshing Twitter OAuth2 tokens...')\n      \n      // Refresh the token\n      const { client: rwClient, accessToken, refreshToken: newRefreshToken, expiresIn } = await oauthClient.refreshOAuth2Token(refreshToken)\n      \n      // Calculate expiration time if provided\n      let expiresAt: string | undefined\n      if (expiresIn) {\n        const expirationDate = new Date(Date.now() + (expiresIn * 1000))\n        expiresAt = expirationDate.toISOString()\n      }\n\n      // Update tokens in database\n      await dbOperations.updateTwitterTokens(accessToken, newRefreshToken, expiresAt)\n      \n      console.log('✅ Twitter tokens refreshed and updated in database')\n      console.log(`📅 New tokens expire at: ${expiresAt || 'Unknown'}`)\n      \n      return {\n        client: rwClient,\n        accessToken,\n        refreshToken: newRefreshToken,\n        expiresAt\n      }\n    } catch (error) {\n      console.error('❌ Failed to refresh Twitter tokens:', error)\n      \n      // Provide helpful error messages\n      if (error instanceof Error) {\n        if (error.message.includes('invalid_grant')) {\n          throw new Error('Twitter refresh token is invalid or expired. Please re-authenticate using the OAuth flow.')\n        } else if (error.message.includes('invalid_client')) {\n          throw new Error('Twitter client credentials are invalid. Please check TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET.')\n        }\n      }\n      \n      throw error\n    }\n  }\n\n  /**\n   * Save initial tokens from OAuth flow\n   */\n  async saveInitialTokens(accessToken: string, refreshToken: string, scope?: string, expiresIn?: number): Promise<void> {\n    try {\n      let expiresAt: string | undefined\n      if (expiresIn) {\n        const expirationDate = new Date(Date.now() + (expiresIn * 1000))\n        expiresAt = expirationDate.toISOString()\n      }\n\n      await dbOperations.saveTwitterTokens({\n        access_token: accessToken,\n        refresh_token: refreshToken,\n        expires_at: expiresAt,\n        token_type: 'bearer',\n        scope: scope\n      })\n\n      console.log('✅ Initial Twitter tokens saved to database')\n    } catch (error) {\n      console.error('❌ Failed to save initial Twitter tokens:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Check if we have valid tokens stored\n   */\n  async hasValidTokens(): Promise<boolean> {\n    try {\n      const tokens = await dbOperations.getTwitterTokens()\n      return !!tokens && !!tokens.access_token && !!tokens.refresh_token\n    } catch (error) {\n      console.error('Error checking token validity:', error)\n      return false\n    }\n  }\n\n  /**\n   * Get current token status for debugging\n   */\n  async getTokenStatus(): Promise<{\n    hasTokens: boolean\n    expiresAt?: string\n    scope?: string\n    lastUpdated?: string\n  }> {\n    try {\n      const tokens = await dbOperations.getTwitterTokens()\n      \n      if (!tokens) {\n        return { hasTokens: false }\n      }\n\n      return {\n        hasTokens: true,\n        expiresAt: tokens.expires_at,\n        scope: tokens.scope,\n        lastUpdated: tokens.updated_at\n      }\n    } catch (error) {\n      console.error('Error getting token status:', error)\n      return { hasTokens: false }\n    }\n  }\n}\n\n// Export singleton instance\nexport const twitterAuth = new TwitterAuthManager()\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AASO,MAAM;IACH,SAAgB;IAChB,aAAoB;IAE5B,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,iBAAiB;QAC7C,IAAI,CAAC,YAAY,GAAG,QAAQ,GAAG,CAAC,qBAAqB;QAErD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;GAGC,GACD,MAAM,yBAA8C;QAClD,IAAI;YACF,kCAAkC;YAClC,MAAM,eAAe,MAAM,wHAAA,CAAA,eAAY,CAAC,gBAAgB;YAExD,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAEA,yCAAyC;YACzC,MAAM,cAAc,IAAI,wLAAA,CAAA,aAAU,CAAC;gBACjC,UAAU,IAAI,CAAC,QAAQ;gBACvB,cAAc,IAAI,CAAC,YAAY;YACjC;YAEA,qBAAqB;YACrB,MAAM,gBAAgB,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,aAAa,aAAa;YAEtF,OAAO,cAAc,MAAM;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAc,cAAc,WAAuB,EAAE,YAAoB,EAAsC;QAC7G,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,oBAAoB;YACpB,MAAM,EAAE,QAAQ,QAAQ,EAAE,WAAW,EAAE,cAAc,eAAe,EAAE,SAAS,EAAE,GAAG,MAAM,YAAY,kBAAkB,CAAC;YAEzH,wCAAwC;YACxC,IAAI;YACJ,IAAI,WAAW;gBACb,MAAM,iBAAiB,IAAI,KAAK,KAAK,GAAG,KAAM,YAAY;gBAC1D,YAAY,eAAe,WAAW;YACxC;YAEA,4BAA4B;YAC5B,MAAM,wHAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,aAAa,iBAAiB;YAErE,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,aAAa,WAAW;YAEhE,OAAO;gBACL,QAAQ;gBACR;gBACA,cAAc;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YAErD,iCAAiC;YACjC,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBAC3C,MAAM,IAAI,MAAM;gBAClB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,mBAAmB;oBACnD,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,WAAmB,EAAE,YAAoB,EAAE,KAAc,EAAE,SAAkB,EAAiB;QACpH,IAAI;YACF,IAAI;YACJ,IAAI,WAAW;gBACb,MAAM,iBAAiB,IAAI,KAAK,KAAK,GAAG,KAAM,YAAY;gBAC1D,YAAY,eAAe,WAAW;YACxC;YAEA,MAAM,wHAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC;gBACnC,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,YAAY;gBACZ,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,SAAS,MAAM,wHAAA,CAAA,eAAY,CAAC,gBAAgB;YAClD,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,YAAY,IAAI,CAAC,CAAC,OAAO,aAAa;QACpE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,iBAKH;QACD,IAAI;YACF,MAAM,SAAS,MAAM,wHAAA,CAAA,eAAY,CAAC,gBAAgB;YAElD,IAAI,CAAC,QAAQ;gBACX,OAAO;oBAAE,WAAW;gBAAM;YAC5B;YAEA,OAAO;gBACL,WAAW;gBACX,WAAW,OAAO,UAAU;gBAC5B,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,UAAU;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBAAE,WAAW;YAAM;QAC5B;IACF;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from 'twitter-api-v2';\nimport { twitterAuth } from './twitter-auth';\n// Cached Twitter client instance\nlet twitterClientInstance: any = null\n\n// Twitter client configuration - only initialize if keys are available\nfunction getTwitterClient() {\n\n    // Create new instance and cache it\n    twitterClientInstance = new Twitter<PERSON><PERSON>({\n      clientId: process.env.TWITTER_CLIENT_ID!,\n      clientSecret: process.env.TWITTER_CLIENT_SECRET!,\n      accessToken: process.env.TWITTER_ACCESS_TOKEN!,\n      refreshToken: process.env.TWITTER_REFRESH_TOKEN!,\n    } as any)\n\n    return twitterClientInstance\n\n}\n\n// Cached Twitter V1 client instance\nlet twitterV1ClientInstance: any = null\n\n// Twitter API v1.1 client for additional features\nfunction getTwitterV1Client() {\n\n\n  // Return cached instance if available\n  if (twitterV1ClientInstance) {\n    return twitterV1ClientInstance\n  }\n\n  twitterV1ClientInstance = getTwitterClient();\n\n  return twitterV1ClientInstance;\n\n}\n\nexport interface TwitterUserData {\n  id: string\n  username: string\n  name: string\n  followers_count: number\n  following_count: number\n  tweet_count: number\n  verified: boolean\n  profile_image_url?: string\n}\n\nexport interface TweetData {\n  id: string\n  text: string\n  created_at: string\n  public_metrics?: {\n    retweet_count: number\n    like_count: number\n    reply_count: number\n    quote_count: number\n    impression_count?: number\n  }\n}\n\nexport const twitterOperations = {\n  // Get current user information\n  async getCurrentUser(): Promise<TwitterUserData> {\n    try {\n      const twitterClient = getTwitterClient()\n      const user = await twitterClient.v2.me({\n        'user.fields': ['public_metrics', 'verified', 'profile_image_url']\n      })\n\n      return {\n        id: user.data.id,\n        username: user.data.username,\n        name: user.data.name,\n        followers_count: user.data.public_metrics?.followers_count || 0,\n        following_count: user.data.public_metrics?.following_count || 0,\n        tweet_count: user.data.public_metrics?.tweet_count || 0,\n        verified: user.data.verified || false,\n        profile_image_url: user.data.profile_image_url\n      }\n    } catch (error) {\n      console.error('Error fetching current user:', error)\n      // Return mock data when Twitter API fails\n      return {\n        id: 'mock_user_id',\n        username: 'twitter_bot',\n        name: 'Twitter Bot',\n        followers_count: 150,\n        following_count: 50,\n        tweet_count: 25,\n        verified: false,\n        profile_image_url: undefined\n      }\n    }\n  },\n\n  // Post a tweet\n  async postTweet(content: string): Promise<TweetData> {\n    try {\n      // Get authenticated Twitter client (handles token refresh automatically)\n      const twitterClient = await twitterAuth.getAuthenticatedClient()\n\n      // Post the tweet using the authenticated client\n      const tweet = await twitterClient.v2.tweet(content)\n\n      return {\n        id: tweet.data.id,\n        text: content,\n        created_at: new Date().toISOString()\n      }\n    } catch (error) {\n      console.error('Error posting tweet:', error)\n      // Return mock tweet data when Twitter API fails\n      return {\n        id: `mock_tweet_${Date.now()}`,\n        text: content,\n        created_at: new Date().toISOString()\n      }\n    }\n  },\n\n  // Get recent tweets with metrics\n  async getRecentTweets(): Promise<TweetData[]> {\n    try {\n      // Return mock data for now\n      return [\n        {\n          id: 'mock_tweet_1',\n          text: 'Sample tweet about technology trends',\n          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n          public_metrics: {\n            retweet_count: 5,\n            like_count: 12,\n            reply_count: 3,\n            quote_count: 1,\n            impression_count: 150\n          }\n        },\n        {\n          id: 'mock_tweet_2',\n          text: 'Another interesting tweet about AI developments',\n          created_at: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),\n          public_metrics: {\n            retweet_count: 8,\n            like_count: 20,\n            reply_count: 5,\n            quote_count: 2,\n            impression_count: 280\n          }\n        }\n      ]\n    } catch (error) {\n      console.error('Error fetching recent tweets:', error)\n      return []\n    }\n  },\n\n  // Get tweet analytics\n  async getTweetAnalytics(tweetId: string): Promise<TweetData | null> {\n    try {\n      const twitterClient = getTwitterClient()\n      const tweet = await twitterClient.v2.singleTweet(tweetId, {\n        'tweet.fields': ['created_at', 'public_metrics']\n      })\n\n      if (!tweet.data) return null\n\n      return {\n        id: tweet.data.id,\n        text: tweet.data.text,\n        created_at: tweet.data.created_at || new Date().toISOString(),\n        public_metrics: tweet.data.public_metrics\n      }\n    } catch (error) {\n      console.error('Error fetching tweet analytics:', error)\n      return null\n    }\n  },\n\n  // Calculate total impressions from recent tweets\n  async getTotalImpressions(): Promise<number> {\n    try {\n      const tweets = await this.getRecentTweets() // Get more tweets for better calculation\n      \n      // Filter tweets from the last 30 days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - 30)\n      \n      const recentTweets = tweets.filter(tweet => \n        new Date(tweet.created_at) >= cutoffDate\n      )\n      \n      // Sum up impressions (if available) or estimate based on engagement\n      let totalImpressions = 0\n      for (const tweet of recentTweets) {\n        if (tweet.public_metrics?.impression_count) {\n          totalImpressions += tweet.public_metrics.impression_count\n        } else if (tweet.public_metrics) {\n          // Estimate impressions based on engagement (rough calculation)\n          const engagement = (tweet.public_metrics.like_count || 0) + \n                           (tweet.public_metrics.retweet_count || 0) + \n                           (tweet.public_metrics.reply_count || 0)\n          totalImpressions += Math.max(engagement * 10, 100) // Rough estimate\n        }\n      }\n      \n      return totalImpressions\n    } catch (error) {\n      console.error('Error calculating total impressions:', error)\n      return 0\n    }\n  },\n\n  // Get authentication status\n  async getAuthStatus(): Promise<{ authenticated: boolean }> {\n    try {\n      await this.getCurrentUser()\n      return { authenticated: true }\n    } catch (error) {\n      console.error('Error checking auth status:', error)\n      return { authenticated: false }\n    }\n  }\n}\n\nexport { getTwitterClient, getTwitterV1Client }\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;AACA,iCAAiC;AACjC,IAAI,wBAA6B;AAEjC,uEAAuE;AACvE,SAAS;IAEL,mCAAmC;IACnC,wBAAwB,IAAI,wLAAA,CAAA,aAAU,CAAC;QACrC,UAAU,QAAQ,GAAG,CAAC,iBAAiB;QACvC,cAAc,QAAQ,GAAG,CAAC,qBAAqB;QAC/C,aAAa,QAAQ,GAAG,CAAC,oBAAoB;QAC7C,cAAc,QAAQ,GAAG,CAAC,qBAAqB;IACjD;IAEA,OAAO;AAEX;AAEA,oCAAoC;AACpC,IAAI,0BAA+B;AAEnC,kDAAkD;AAClD,SAAS;IAGP,sCAAsC;IACtC,IAAI,yBAAyB;QAC3B,OAAO;IACT;IAEA,0BAA0B;IAE1B,OAAO;AAET;AA0BO,MAAM,oBAAoB;IAC/B,+BAA+B;IAC/B,MAAM;QACJ,IAAI;YACF,MAAM,gBAAgB;YACtB,MAAM,OAAO,MAAM,cAAc,EAAE,CAAC,EAAE,CAAC;gBACrC,eAAe;oBAAC;oBAAkB;oBAAY;iBAAoB;YACpE;YAEA,OAAO;gBACL,IAAI,KAAK,IAAI,CAAC,EAAE;gBAChB,UAAU,KAAK,IAAI,CAAC,QAAQ;gBAC5B,MAAM,KAAK,IAAI,CAAC,IAAI;gBACpB,iBAAiB,KAAK,IAAI,CAAC,cAAc,EAAE,mBAAmB;gBAC9D,iBAAiB,KAAK,IAAI,CAAC,cAAc,EAAE,mBAAmB;gBAC9D,aAAa,KAAK,IAAI,CAAC,cAAc,EAAE,eAAe;gBACtD,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI;gBAChC,mBAAmB,KAAK,IAAI,CAAC,iBAAiB;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0CAA0C;YAC1C,OAAO;gBACL,IAAI;gBACJ,UAAU;gBACV,MAAM;gBACN,iBAAiB;gBACjB,iBAAiB;gBACjB,aAAa;gBACb,UAAU;gBACV,mBAAmB;YACrB;QACF;IACF;IAEA,eAAe;IACf,MAAM,WAAU,OAAe;QAC7B,IAAI;YACF,yEAAyE;YACzE,MAAM,gBAAgB,MAAM,+HAAA,CAAA,cAAW,CAAC,sBAAsB;YAE9D,gDAAgD;YAChD,MAAM,QAAQ,MAAM,cAAc,EAAE,CAAC,KAAK,CAAC;YAE3C,OAAO;gBACL,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,gDAAgD;YAChD,OAAO;gBACL,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;gBAC9B,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;IACF;IAEA,iCAAiC;IACjC,MAAM;QACJ,IAAI;YACF,2BAA2B;YAC3B,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;oBAClE,gBAAgB;wBACd,eAAe;wBACf,YAAY;wBACZ,aAAa;wBACb,aAAa;wBACb,kBAAkB;oBACpB;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;oBAClE,gBAAgB;wBACd,eAAe;wBACf,YAAY;wBACZ,aAAa;wBACb,aAAa;wBACb,kBAAkB;oBACpB;gBACF;aACD;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,OAAe;QACrC,IAAI;YACF,MAAM,gBAAgB;YACtB,MAAM,QAAQ,MAAM,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS;gBACxD,gBAAgB;oBAAC;oBAAc;iBAAiB;YAClD;YAEA,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;YAExB,OAAO;gBACL,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,MAAM,MAAM,IAAI,CAAC,IAAI;gBACrB,YAAY,MAAM,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;gBAC3D,gBAAgB,MAAM,IAAI,CAAC,cAAc;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,iDAAiD;IACjD,MAAM;QACJ,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,GAAG,yCAAyC;;YAErF,sCAAsC;YACtC,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QACjC,IAAI,KAAK,MAAM,UAAU,KAAK;YAGhC,oEAAoE;YACpE,IAAI,mBAAmB;YACvB,KAAK,MAAM,SAAS,aAAc;gBAChC,IAAI,MAAM,cAAc,EAAE,kBAAkB;oBAC1C,oBAAoB,MAAM,cAAc,CAAC,gBAAgB;gBAC3D,OAAO,IAAI,MAAM,cAAc,EAAE;oBAC/B,+DAA+D;oBAC/D,MAAM,aAAa,CAAC,MAAM,cAAc,CAAC,UAAU,IAAI,CAAC,IACvC,CAAC,MAAM,cAAc,CAAC,aAAa,IAAI,CAAC,IACxC,CAAC,MAAM,cAAc,CAAC,WAAW,IAAI,CAAC;oBACvD,oBAAoB,KAAK,GAAG,CAAC,aAAa,IAAI,KAAK,iBAAiB;;gBACtE;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM;QACJ,IAAI;YACF,MAAM,IAAI,CAAC,cAAc;YACzB,OAAO;gBAAE,eAAe;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBAAE,eAAe;YAAM;QAChC;IACF;AACF", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/post/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { dbOperations } from '@/lib/supabase'\nimport { twitterOperations } from '@/lib/twitter'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { contentIds } = await request.json()\n    \n    if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {\n      return NextResponse.json(\n        { error: 'Content IDs array is required' },\n        { status: 400 }\n      )\n    }\n    \n    // Get selected content items\n    const contentQueue = await dbOperations.getContentQueue(100)\n    const selectedItems = contentQueue.filter(item => \n      contentIds.includes(item.id) && !item.is_posted\n    )\n    \n    if (selectedItems.length === 0) {\n      return NextResponse.json(\n        { error: 'No valid content items found' },\n        { status: 400 }\n      )\n    }\n    \n    let posted = 0\n    const results = []\n    \n    for (const item of selectedItems) {\n      try {\n        // Use AI-generated content if available, otherwise create basic tweet\n        const tweetContent = item.ai_generated_content || \n          `${item.original_title}\\n\\n${item.original_url}`\n        \n        // Post to Twitter\n        const tweetData = await twitterOperations.postTweet(tweetContent)\n        \n        // Save to posted tweets database\n        await dbOperations.addPostedTweet({\n          tweet_id: tweetData.id,\n          content: tweetContent,\n          original_url: item.original_url,\n          original_title: item.original_title,\n          impressions: 0,\n          retweets: 0,\n          likes: 0,\n          replies: 0,\n          posted_at: tweetData.created_at\n        })\n        \n        posted++\n        results.push({\n          id: item.id,\n          success: true,\n          tweetId: tweetData.id\n        })\n        \n        // Add delay between posts to avoid rate limiting\n        if (posted < selectedItems.length) {\n          await new Promise(resolve => setTimeout(resolve, 2000))\n        }\n      } catch (error) {\n        console.error(`Error posting content ${item.id}:`, error)\n        results.push({\n          id: item.id,\n          success: false,\n          error: error instanceof Error ? error.message : 'Unknown error'\n        })\n      }\n    }\n    \n    // Mark successfully posted items as posted\n    const successfulIds = results\n      .filter(r => r.success)\n      .map(r => r.id)\n    \n    if (successfulIds.length > 0) {\n      await dbOperations.markContentAsPosted(successfulIds)\n    }\n    \n    return NextResponse.json({\n      posted,\n      total: selectedItems.length,\n      results\n    })\n  } catch (error) {\n    console.error('Error posting content:', error)\n    return NextResponse.json(\n      { error: 'Failed to post content' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzC,IAAI,CAAC,cAAc,CAAC,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,KAAK,GAAG;YACxE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,MAAM,eAAe,MAAM,wHAAA,CAAA,eAAY,CAAC,eAAe,CAAC;QACxD,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,OACxC,WAAW,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,SAAS;QAGjD,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,SAAS;QACb,MAAM,UAAU,EAAE;QAElB,KAAK,MAAM,QAAQ,cAAe;YAChC,IAAI;gBACF,sEAAsE;gBACtE,MAAM,eAAe,KAAK,oBAAoB,IAC5C,GAAG,KAAK,cAAc,CAAC,IAAI,EAAE,KAAK,YAAY,EAAE;gBAElD,kBAAkB;gBAClB,MAAM,YAAY,MAAM,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC;gBAEpD,iCAAiC;gBACjC,MAAM,wHAAA,CAAA,eAAY,CAAC,cAAc,CAAC;oBAChC,UAAU,UAAU,EAAE;oBACtB,SAAS;oBACT,cAAc,KAAK,YAAY;oBAC/B,gBAAgB,KAAK,cAAc;oBACnC,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,SAAS;oBACT,WAAW,UAAU,UAAU;gBACjC;gBAEA;gBACA,QAAQ,IAAI,CAAC;oBACX,IAAI,KAAK,EAAE;oBACX,SAAS;oBACT,SAAS,UAAU,EAAE;gBACvB;gBAEA,iDAAiD;gBACjD,IAAI,SAAS,cAAc,MAAM,EAAE;oBACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;gBACnD,QAAQ,IAAI,CAAC;oBACX,IAAI,KAAK,EAAE;oBACX,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF;QACF;QAEA,2CAA2C;QAC3C,MAAM,gBAAgB,QACnB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EACrB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAEhB,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,wHAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC;QACzC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,OAAO,cAAc,MAAM;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}