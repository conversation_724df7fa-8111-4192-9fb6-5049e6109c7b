{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Client for browser/client-side operations\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Admin client for server-side operations\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)\n\n// Database types\nexport interface RSSFeed {\n  id: string\n  name: string\n  url: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface PostedTweet {\n  id: string\n  tweet_id?: string\n  content: string\n  original_url?: string\n  original_title?: string\n  rss_feed_id?: string\n  impressions: number\n  retweets: number\n  likes: number\n  replies: number\n  posted_at: string\n  created_at: string\n}\n\nexport interface TwitterAnalytics {\n  id: string\n  followers_count: number\n  following_count: number\n  total_tweets: number\n  total_impressions: number\n  total_engagements: number\n  recorded_at: string\n}\n\nexport interface UserPreferences {\n  id: string\n  max_topics_to_select: number\n  posting_interval_minutes: number\n  ai_tone: string\n  include_personal_touch: boolean\n  auto_post_enabled: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface TwitterTokens {\n  id: string\n  access_token: string\n  refresh_token: string\n  expires_at?: string\n  token_type: string\n  scope?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ContentQueue {\n  id: string\n  original_url: string\n  original_title?: string\n  original_content?: string\n  ai_generated_content?: string\n  short_hook?: string\n  long_hook?: string\n  personal_touch?: string\n  rss_feed_id?: string\n  is_selected: boolean\n  is_posted: boolean\n  priority_score: number\n  created_at: string\n  updated_at: string\n}\n\n// Database operations\nexport const dbOperations = {\n  // RSS Feeds\n  async getRSSFeeds() {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .select('*')\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data as RSSFeed[]\n  },\n\n  async addRSSFeed(name: string, url: string) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .insert({ name, url })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  async updateRSSFeed(id: string, updates: Partial<RSSFeed>) {\n    const { data, error } = await supabase\n      .from('rss_feeds')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as RSSFeed\n  },\n\n  // Posted Tweets\n  async getPostedTweets(limit = 50) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .select('*')\n      .order('posted_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as PostedTweet[]\n  },\n\n  async addPostedTweet(tweet: Omit<PostedTweet, 'id' | 'created_at'>) {\n    const { data, error } = await supabase\n      .from('posted_tweets')\n      .insert(tweet)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as PostedTweet\n  },\n\n  // Twitter Analytics\n  async getLatestAnalytics() {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .select('*')\n      .order('recorded_at', { ascending: false })\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as TwitterAnalytics | null\n  },\n\n  async addAnalytics(analytics: Omit<TwitterAnalytics, 'id' | 'recorded_at'>) {\n    const { data, error } = await supabase\n      .from('twitter_analytics')\n      .insert(analytics)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as TwitterAnalytics\n  },\n\n  // User Preferences\n  async getUserPreferences() {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .select('*')\n      .limit(1)\n      .single()\n    \n    if (error && error.code !== 'PGRST116') throw error\n    return data as UserPreferences | null\n  },\n\n  async updateUserPreferences(updates: Partial<UserPreferences>) {\n    const { data, error } = await supabase\n      .from('user_preferences')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as UserPreferences\n  },\n\n  // Content Queue\n  async getContentQueue(limit = 20) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async addToContentQueue(content: Omit<ContentQueue, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .insert(content)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async updateContentQueue(id: string, updates: Partial<ContentQueue>) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as ContentQueue\n  },\n\n  async getSelectedContent() {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .select('*')\n      .eq('is_selected', true)\n      .eq('is_posted', false)\n      .order('priority_score', { ascending: false })\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  async markContentAsPosted(ids: string[]) {\n    const { data, error } = await supabase\n      .from('content_queue')\n      .update({\n        is_posted: true,\n        is_selected: false,\n        updated_at: new Date().toISOString()\n      })\n      .in('id', ids)\n      .select()\n\n    if (error) throw error\n    return data as ContentQueue[]\n  },\n\n  // Delete RSS Feed\n  async deleteRSSFeed(id: string) {\n    const { error } = await supabase\n      .from('rss_feeds')\n      .delete()\n      .eq('id', id)\n\n    if (error) throw error\n    return true\n  },\n\n  // Twitter Tokens Management\n  async getTwitterTokens(): Promise<TwitterTokens | null> {\n    const { data, error } = await supabase\n      .from('twitter_tokens')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(1)\n      .single()\n\n    if (error && error.code !== 'PGRST116') throw error\n    return data as TwitterTokens | null\n  },\n\n  async saveTwitterTokens(tokens: Omit<TwitterTokens, 'id' | 'created_at' | 'updated_at'>): Promise<TwitterTokens> {\n    // First, try to update existing tokens\n    const { data: existingData, error: selectError } = await supabase\n      .from('twitter_tokens')\n      .select('id')\n      .limit(1)\n      .single()\n\n    if (existingData) {\n      // Update existing tokens\n      const { data, error } = await supabase\n        .from('twitter_tokens')\n        .update({\n          ...tokens,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', existingData.id)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data as TwitterTokens\n    } else {\n      // Insert new tokens\n      const { data, error } = await supabase\n        .from('twitter_tokens')\n        .insert({\n          ...tokens,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        })\n        .select()\n        .single()\n\n      if (error) throw error\n      return data as TwitterTokens\n    }\n  },\n\n  async updateTwitterTokens(accessToken: string, refreshToken: string, expiresAt?: string): Promise<TwitterTokens> {\n    const { data: existingData, error: selectError } = await supabase\n      .from('twitter_tokens')\n      .select('id')\n      .limit(1)\n      .single()\n\n    if (!existingData) {\n      throw new Error('No existing Twitter tokens found. Please authenticate first.')\n    }\n\n    const { data, error } = await supabase\n      .from('twitter_tokens')\n      .update({\n        access_token: accessToken,\n        refresh_token: refreshToken,\n        expires_at: expiresAt,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', existingData.id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as TwitterTokens\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAGzD,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AA6EhD,MAAM,eAAe;IAC1B,YAAY;IACZ,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,YAAW,IAAY,EAAE,GAAW;QACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE;YAAM;QAAI,GACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,EAAU,EAAE,OAAyB;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM,GACtC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,KAA6C;QAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,eAAe;YAAE,WAAW;QAAM,GACxC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,cAAa,SAAuD;QACxE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,uBAAsB,OAAiC;QAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,iBAAgB,QAAQ,EAAE;QAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,mBAAkB,OAA+D;QACrF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,oBAAmB,EAAU,EAAE,OAA8B;QACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,MAClB,EAAE,CAAC,aAAa,OAChB,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;QAE9C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,qBAAoB,GAAa;QACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,WAAW;YACX,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,KACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY,MAAM;QAC9C,OAAO;IACT;IAEA,MAAM,mBAAkB,MAA+D;QACrF,uCAAuC;QACvC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,cAAc;YAChB,yBAAyB;YACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,GAAG,MAAM;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT,OAAO;YACL,oBAAoB;YACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,GAAG,MAAM;gBACT,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;IACF;IAEA,MAAM,qBAAoB,WAAmB,EAAE,YAAoB,EAAE,SAAkB;QACrF,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,KAAK,CAAC,GACN,MAAM;QAET,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;YACN,cAAc;YACd,eAAe;YACf,YAAY;YACZ,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { dbOperations } from '@/lib/supabase'\n\nexport async function GET() {\n  try {\n    const feeds = await dbOperations.getRSSFeeds()\n    return NextResponse.json(feeds)\n  } catch (error) {\n    console.error('Error fetching RSS feeds:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch RSS feeds' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { name, url } = await request.json()\n    \n    if (!name || !url) {\n      return NextResponse.json(\n        { error: 'Name and URL are required' },\n        { status: 400 }\n      )\n    }\n    \n    const feed = await dbOperations.addRSSFeed(name, url)\n    return NextResponse.json(feed, { status: 201 })\n  } catch (error) {\n    console.error('Error adding RSS feed:', error)\n    return NextResponse.json(\n      { error: 'Failed to add RSS feed' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,wHAAA,CAAA,eAAY,CAAC,WAAW;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAExC,IAAI,CAAC,QAAQ,CAAC,KAAK;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,wHAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}