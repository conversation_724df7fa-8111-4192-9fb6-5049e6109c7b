module.exports = {

"[project]/node_modules/twitter-api-v2/dist/esm/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_twitter-api-v2_dist_esm_d171e321._.js",
  "server/chunks/[externals]_fs_6d6439d0._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/twitter-api-v2/dist/esm/index.js [app-route] (ecmascript)");
    });
});
}}),

};