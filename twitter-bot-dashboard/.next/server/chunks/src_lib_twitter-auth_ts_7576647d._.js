module.exports = {

"[project]/src/lib/twitter-auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TwitterAuthManager": (()=>TwitterAuthManager),
    "twitterAuth": (()=>twitterAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
;
class TwitterAuthManager {
    clientId;
    clientSecret;
    constructor(){
        this.clientId = process.env.TWITTER_CLIENT_ID;
        this.clientSecret = process.env.TWITTER_CLIENT_SECRET;
        if (!this.clientId || !this.clientSecret) {
            throw new Error('Twitter client credentials not found in environment variables');
        }
    }
    /**
   * Get a fresh Twitter client with valid tokens
   * This method handles token refresh automatically
   */ async getAuthenticatedClient() {
        try {
            // Get stored tokens from database
            const storedTokens = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getTwitterTokens();
            if (!storedTokens) {
                throw new Error('No Twitter tokens found in database. Please authenticate first using the OAuth flow.');
            }
            // Import TwitterApi dynamically to avoid circular dependency
            const { TwitterApi } = await __turbopack_context__.r("[project]/node_modules/twitter-api-v2/dist/esm/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            // Create OAuth2 client for token refresh
            const oauthClient = new TwitterApi({
                clientId: this.clientId,
                clientSecret: this.clientSecret
            });
            // Refresh the tokens
            const refreshResult = await this.refreshTokens(oauthClient, storedTokens.refresh_token);
            return refreshResult.client;
        } catch (error) {
            console.error('Error getting authenticated Twitter client:', error);
            throw error;
        }
    }
    /**
   * Refresh Twitter OAuth2 tokens and update database
   */ async refreshTokens(oauthClient, refreshToken) {
        try {
            console.log('🔄 Refreshing Twitter OAuth2 tokens...');
            // Refresh the token
            const { client: rwClient, accessToken, refreshToken: newRefreshToken, expiresIn } = await oauthClient.refreshOAuth2Token(refreshToken);
            // Validate that we got a new refresh token
            if (!newRefreshToken) {
                throw new Error('No refresh token returned from Twitter OAuth2 refresh');
            }
            // Calculate expiration time if provided
            let expiresAt;
            if (expiresIn) {
                const expirationDate = new Date(Date.now() + expiresIn * 1000);
                expiresAt = expirationDate.toISOString();
            }
            // Update tokens in database
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].updateTwitterTokens(accessToken, newRefreshToken, expiresAt);
            console.log('✅ Twitter tokens refreshed and updated in database');
            console.log(`📅 New tokens expire at: ${expiresAt || 'Unknown'}`);
            return {
                client: rwClient,
                accessToken,
                refreshToken: newRefreshToken,
                expiresAt
            };
        } catch (error) {
            console.error('❌ Failed to refresh Twitter tokens:', error);
            // Provide helpful error messages
            if (error instanceof Error) {
                if (error.message.includes('invalid_grant')) {
                    throw new Error('Twitter refresh token is invalid or expired. Please re-authenticate using the OAuth flow.');
                } else if (error.message.includes('invalid_client')) {
                    throw new Error('Twitter client credentials are invalid. Please check TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET.');
                }
            }
            throw error;
        }
    }
    /**
   * Save initial tokens from OAuth flow
   */ async saveInitialTokens(accessToken, refreshToken, scope, expiresIn) {
        try {
            let expiresAt;
            if (expiresIn) {
                const expirationDate = new Date(Date.now() + expiresIn * 1000);
                expiresAt = expirationDate.toISOString();
            }
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].saveTwitterTokens({
                access_token: accessToken,
                refresh_token: refreshToken,
                expires_at: expiresAt,
                token_type: 'bearer',
                scope: scope
            });
            console.log('✅ Initial Twitter tokens saved to database');
        } catch (error) {
            console.error('❌ Failed to save initial Twitter tokens:', error);
            throw error;
        }
    }
    /**
   * Check if we have valid tokens stored
   */ async hasValidTokens() {
        try {
            const tokens = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getTwitterTokens();
            return !!tokens && !!tokens.access_token && !!tokens.refresh_token;
        } catch (error) {
            console.error('Error checking token validity:', error);
            return false;
        }
    }
    /**
   * Get current token status for debugging
   */ async getTokenStatus() {
        try {
            const tokens = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dbOperations"].getTwitterTokens();
            if (!tokens) {
                return {
                    hasTokens: false
                };
            }
            return {
                hasTokens: true,
                expiresAt: tokens.expires_at,
                scope: tokens.scope,
                lastUpdated: tokens.updated_at
            };
        } catch (error) {
            console.error('Error getting token status:', error);
            return {
                hasTokens: false
            };
        }
    }
}
const twitterAuth = new TwitterAuthManager();
}}),

};

//# sourceMappingURL=src_lib_twitter-auth_ts_7576647d._.js.map