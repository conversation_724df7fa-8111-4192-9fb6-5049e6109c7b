'use client'

import { useEffect, useState } from 'react'
import { Users, MessageCircle, Eye, TrendingUp, RefreshCw } from 'lucide-react'

interface DashboardStats {
  followers: number
  totalTweets: number
  totalImpressions: number
  totalEngagements: number
  recentTweets: number
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    followers: 0,
    totalTweets: 0,
    totalImpressions: 0,
    totalEngagements: 0,
    recentTweets: 0
  })
  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchStats = async () => {
    try {
      setLoading(true)
      const { apiCall, API_CONFIG } = await import('../lib/api-config')
      const data = await apiCall(API_CONFIG.ENDPOINTS.TWITTER_STATS)

      // Map the API response to our local interface
      setStats({
        followers: data.followers_count,
        totalTweets: data.total_tweets,
        totalImpressions: data.total_impressions,
        totalEngagements: data.total_engagements,
        recentTweets: data.total_tweets // Using total tweets as recent tweets for now
      })
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
    // Refresh stats every 5 minutes
    const interval = setInterval(fetchStats, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  const StatCard = ({
    title,
    value,
    icon: Icon,
    change,
    color = 'blue'
  }: {
    title: string
    value: number | string
    icon: React.ComponentType<{ className?: string }>
    change?: string
    color?: 'blue' | 'green' | 'purple' | 'orange'
  }) => {
    const colorClasses = {
      blue: 'bg-blue-50 border-blue-200 text-blue-700',
      green: 'bg-green-50 border-green-200 text-green-700',
      purple: 'bg-purple-50 border-purple-200 text-purple-700',
      orange: 'bg-orange-50 border-orange-200 text-orange-700'
    }

    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mt-1">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {change && (
              <p className="text-sm text-green-600 mt-1">{change}</p>
            )}
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Twitter Bot Dashboard</h1>
              <p className="text-sm text-gray-600 mt-1">
                Monitor your automated Twitter bot performance
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {lastUpdated && (
                <p className="text-sm text-gray-500">
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </p>
              )}
              <button
                type="button"
                onClick={fetchStats}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Followers"
            value={stats.followers}
            icon={Users}
            color="blue"
          />
          <StatCard
            title="Total Tweets"
            value={stats.totalTweets}
            icon={MessageCircle}
            color="green"
          />
          <StatCard
            title="Total Impressions"
            value={stats.totalImpressions}
            icon={Eye}
            color="purple"
          />
          <StatCard
            title="Total Engagements"
            value={stats.totalEngagements}
            icon={TrendingUp}
            color="orange"
          />
        </div>

        {/* Navigation */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a
              href="/feeds"
              className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <h3 className="font-medium text-gray-900">RSS Feeds</h3>
              <p className="text-sm text-gray-600 mt-1">
                Manage RSS feeds and view aggregated content
              </p>
            </a>
            <a
              href="/content"
              className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <h3 className="font-medium text-gray-900">Content Queue</h3>
              <p className="text-sm text-gray-600 mt-1">
                Review and select content for posting
              </p>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
