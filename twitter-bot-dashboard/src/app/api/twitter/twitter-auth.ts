import { dbOperations } from '../../../lib/supabase';
import { <PERSON><PERSON><PERSON> } from 'twitter-api-v2';

export interface TwitterTokenRefreshResult {
  client: any // TwitterApi
  accessToken: string
  refreshToken: string
  expiresAt?: string
}

export class TwitterAuth<PERSON>anager {
  private clientId: string
  private clientSecret: string
  private oauthClient: any


  constructor() {
    this.clientId = process.env.TWITTER_CLIENT_ID!
    this.clientSecret = process.env.TWITTER_CLIENT_SECRET!
    this.oauthClient = new TwitterApi({
        clientId: this.clientId,
        clientSecret: this.clientSecret,
      } as any)
 
    if (!this.clientId || !this.clientSecret) {
      throw new Error('Twitter client credentials not found in environment variables')
    }
  }

  /**
   * Get a fresh Twitter client with valid tokens
   * This method handles token refresh automatically
   */
  async getAuthenticatedClient(): Promise<any> {
    try {
      // Get stored tokens from database
      const storedTokens = await dbOperations.getTwitterTokens()
      
      if (!storedTokens) {
        throw new Error('No Twitter tokens found in database. Please authenticate first using the OAuth flow.')
      }


      // Refresh the tokens
      const refreshResult = await this.refreshTokens(this.oauthClient, storedTokens.refresh_token)
      
      return refreshResult.client
    } catch (error) {
      console.error('Error getting authenticated Twitter client:', error)
      throw error
    }
  }

  /**
   * Refresh Twitter OAuth2 tokens and update database
   */
  private async refreshTokens(oauthClient: any, refreshToken: string): Promise<TwitterTokenRefreshResult> {
    try {
      console.log('🔄 Refreshing Twitter OAuth2 tokens...')
      
      // Refresh the token
      const { client: rwClient, accessToken, refreshToken: newRefreshToken, expiresIn } = await oauthClient.refreshOAuth2Token(refreshToken)

      // Validate that we got a new refresh token
      if (!newRefreshToken) {
        throw new Error('No refresh token returned from Twitter OAuth2 refresh')
      }

      // Calculate expiration time if provided
      let expiresAt: string | undefined
      if (expiresIn) {
        const expirationDate = new Date(Date.now() + (expiresIn * 1000))
        expiresAt = expirationDate.toISOString()
      }

      // Update tokens in database
      await dbOperations.updateTwitterTokens(accessToken, newRefreshToken, expiresAt)

      console.log('✅ Twitter tokens refreshed and updated in database')
      console.log(`📅 New tokens expire at: ${expiresAt || 'Unknown'}`)

      return {
        client: rwClient,
        accessToken,
        refreshToken: newRefreshToken,
        expiresAt
      }
    } catch (error) {
      console.error('❌ Failed to refresh Twitter tokens:', error)
      
      // Provide helpful error messages
      if (error instanceof Error) {
        if (error.message.includes('invalid_grant')) {
          throw new Error('Twitter refresh token is invalid or expired. Please re-authenticate using the OAuth flow.')
        } else if (error.message.includes('invalid_client')) {
          throw new Error('Twitter client credentials are invalid. Please check TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET.')
        }
      }
      
      throw error
    }
  }

  /**
   * Save initial tokens from OAuth flow
   */
  async saveInitialTokens(accessToken: string, refreshToken: string, scope?: string, expiresIn?: number): Promise<void> {
    try {
      let expiresAt: string | undefined
      if (expiresIn) {
        const expirationDate = new Date(Date.now() + (expiresIn * 1000))
        expiresAt = expirationDate.toISOString()
      }

      await dbOperations.saveTwitterTokens({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_at: expiresAt,
        token_type: 'bearer',
        scope: scope
      })

      console.log('✅ Initial Twitter tokens saved to database')
    } catch (error) {
      console.error('❌ Failed to save initial Twitter tokens:', error)
      throw error
    }
  }

  /**
   * Check if we have valid tokens stored
   */
  async hasValidTokens(): Promise<boolean> {
    try {
      const tokens = await dbOperations.getTwitterTokens()
      return !!tokens && !!tokens.access_token && !!tokens.refresh_token
    } catch (error) {
      console.error('Error checking token validity:', error)
      return false
    }
  }

  /**
   * Get current token status for debugging
   */
  async getTokenStatus(): Promise<{
    hasTokens: boolean
    expiresAt?: string
    scope?: string
    lastUpdated?: string
  }> {
    try {
      const tokens = await dbOperations.getTwitterTokens()
      
      if (!tokens) {
        return { hasTokens: false }
      }

      return {
        hasTokens: true,
        expiresAt: tokens.expires_at,
        scope: tokens.scope,
        lastUpdated: tokens.updated_at
      }
    } catch (error) {
      console.error('Error getting token status:', error)
      return { hasTokens: false }
    }
  }
}

