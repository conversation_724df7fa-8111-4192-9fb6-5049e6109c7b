import TweetComposer from '@/components/TweetComposer'
import TwitterStats from '@/components/TwitterStats'

export default function ComposePage() {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Tweet Composer
          </h1>
          <p className="text-gray-600">
            Post tweets directly to your Twitter account
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <TweetComposer />
          </div>
          <div>
            <TwitterStats />
          </div>
        </div>
      </div>
    </div>
  )
}
