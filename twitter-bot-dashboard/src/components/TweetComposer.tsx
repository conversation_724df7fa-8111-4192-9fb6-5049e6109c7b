'use client'

import { useState } from 'react'

interface TweetResponse {
  success: boolean
  tweet?: {
    id: string
    text: string
    created_at: string
    url: string
  }
  error?: string
}

export default function TweetComposer() {
  const [content, setContent] = useState('')
  const [isPosting, setIsPosting] = useState(false)
  const [lastResponse, setLastResponse] = useState<TweetResponse | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!content.trim()) {
      alert('Please enter some content for your tweet')
      return
    }

    if (content.length > 280) {
      alert('Tweet content exceeds 280 character limit')
      return
    }

    setIsPosting(true)
    setLastResponse(null)

    try {
      const { apiCall, API_CONFIG } = await import('../lib/api-config')

      const data = await apiCall(API_CONFIG.ENDPOINTS.TWITTER_POST, {
        method: 'POST',
        body: JSON.stringify({ content }),
      })

      setLastResponse(data)

      if (data.success) {
        setContent('') // Clear the form on success
      }
    } catch (error) {
      console.error('Error posting tweet:', error)
      setLastResponse({
        success: false,
        error: 'Network error. Please try again.'
      })
    } finally {
      setIsPosting(false)
    }
  }

  const characterCount = content.length
  const isOverLimit = characterCount > 280
  const characterCountColor = isOverLimit ? 'text-red-500' : 
                             characterCount > 260 ? 'text-yellow-500' : 
                             'text-gray-500'

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">Compose Tweet</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="What's happening?"
            className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={4}
            disabled={isPosting}
          />
          <div className="flex justify-between items-center mt-2">
            <span className={`text-sm ${characterCountColor}`}>
              {characterCount}/280
            </span>
            {isOverLimit && (
              <span className="text-sm text-red-500">
                {characterCount - 280} characters over limit
              </span>
            )}
          </div>
        </div>

        <button
          type="submit"
          disabled={isPosting || !content.trim() || isOverLimit}
          className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isPosting ? 'Posting...' : 'Post Tweet'}
        </button>
      </form>

      {/* Response Display */}
      {lastResponse && (
        <div className="mt-6 p-4 rounded-lg">
          {lastResponse.success ? (
            <div className="bg-green-50 border border-green-200 text-green-800">
              <div className="flex items-center mb-2">
                <span className="text-green-500 mr-2">✅</span>
                <strong>Tweet posted successfully!</strong>
              </div>
              {lastResponse.tweet && (
                <div className="text-sm space-y-1">
                  <p><strong>Tweet ID:</strong> {lastResponse.tweet.id}</p>
                  <p><strong>Posted at:</strong> {new Date(lastResponse.tweet.created_at).toLocaleString()}</p>
                  {lastResponse.tweet.url && (
                    <p>
                      <strong>View on Twitter:</strong>{' '}
                      <a 
                        href={lastResponse.tweet.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        Open Tweet
                      </a>
                    </p>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="bg-red-50 border border-red-200 text-red-800">
              <div className="flex items-center mb-2">
                <span className="text-red-500 mr-2">❌</span>
                <strong>Failed to post tweet</strong>
              </div>
              {lastResponse.error && (
                <p className="text-sm">{lastResponse.error}</p>
              )}
            </div>
          )}
        </div>
      )}

      {/* Usage Instructions */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-800 mb-2">Usage Notes:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Maximum 280 characters per tweet</li>
          <li>• Requires valid Twitter OAuth2 authentication</li>
          <li>• Posted tweets are automatically saved to the database</li>
          <li>• Rate limits apply (avoid posting too frequently)</li>
        </ul>
      </div>
    </div>
  )
}
