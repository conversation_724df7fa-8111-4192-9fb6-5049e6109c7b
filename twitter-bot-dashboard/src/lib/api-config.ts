// API Configuration for external Twitter API server
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
  ENDPOINTS: {
    TWITTER_POST: '/twitter/post',
    TWITTER_STATS: '/twitter/stats',
    TWITTER_STATS_REFRESH: '/twitter/stats',
    TWITTER_AUTH_STATUS: '/twitter/auth/status',
    TWITTER_HEALTH: '/twitter/health'
  }
};

// Helper function to make API calls
export async function apiCall(endpoint: string, options: RequestInit = {}) {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, defaultOptions);
  
  if (!response.ok) {
    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}
