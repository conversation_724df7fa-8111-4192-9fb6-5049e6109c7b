import cron, { ScheduledTask } from 'node-cron'
import { dbOperations } from './supabase'
import { twitterOperations } from '../app/api/twitter/stats/twitter'
import rssOperations from './rss'
import aiOperations from './openai'

class TwitterBotScheduler {
  private isRunning = false
  private cronJob: ScheduledTask | null = null

  async start() {
    if (this.isRunning) {
      console.log('Scheduler is already running')
      return
    }

    try {
      // Get user preferences for scheduling
      const preferences = await dbOperations.getUserPreferences()
      const intervalMinutes = preferences?.posting_interval_minutes || 30
      
      // Create cron expression for the interval
      const cronExpression = `*/${intervalMinutes} * * * *`
      
      console.log(`Starting Twitter bot scheduler with ${intervalMinutes} minute intervals`)
      
      this.cronJob = cron.schedule(cronExpression, async () => {
        await this.processAutomatedPosting()
      })

      this.isRunning = true
      
      console.log('Twitter bot scheduler started successfully')
    } catch (error) {
      console.error('Error starting scheduler:', error)
    }
  }

  stop() {
    if (this.cronJob) {
      this.cronJob.stop()
      this.cronJob = null
    }
    this.isRunning = false
    console.log('Twitter bot scheduler stopped')
  }

  async processAutomatedPosting() {
    try {
      console.log('Processing automated posting...')
      
      // Check if auto-posting is enabled
      const preferences = await dbOperations.getUserPreferences()
      if (!preferences?.auto_post_enabled) {
        console.log('Auto-posting is disabled, skipping...')
        return
      }

      // Refresh content queue first
      await this.refreshContentQueue()
      
      // Get content queue and find items ready for posting
      const contentQueue = await dbOperations.getContentQueue(50)
      const unpostedItems = contentQueue.filter(item => 
        !item.is_posted && 
        item.ai_generated_content && 
        item.priority_score > 50 // Only post high-quality content
      )
      
      if (unpostedItems.length === 0) {
        console.log('No suitable content found for automated posting')
        return
      }
      
      // Select the highest scoring item
      const itemToPost = unpostedItems.sort((a, b) => b.priority_score - a.priority_score)[0]
      
      // Post to Twitter
      const tweetData = await twitterOperations.postTweet(itemToPost.ai_generated_content!)
      
      // Save to posted tweets database
      await dbOperations.addPostedTweet({
        tweet_id: tweetData.id,
        content: itemToPost.ai_generated_content!,
        original_url: itemToPost.original_url,
        original_title: itemToPost.original_title,
        impressions: 0,
        retweets: 0,
        likes: 0,
        replies: 0,
        posted_at: tweetData.created_at
      })
      
      // Mark as posted
      await dbOperations.markContentAsPosted([itemToPost.id])
      
      console.log(`Successfully posted automated tweet: ${itemToPost.original_title}`)
    } catch (error) {
      console.error('Error in automated posting:', error)
    }
  }

  async refreshContentQueue() {
    try {
      // Get active RSS feeds
      const feeds = await dbOperations.getRSSFeeds()
      const activeFeeds = feeds.filter(feed => feed.is_active)
      
      if (activeFeeds.length === 0) {
        console.log('No active RSS feeds found')
        return
      }
      
      // Get aggregated content from all active feeds
      const feedUrls = activeFeeds.map(feed => feed.url)
      const content = await rssOperations.getAggregatedContent(feedUrls)
      
      // Score and filter content
      const scoredContent = rssOperations.scoreContent(content)
      
      // Get existing content URLs to avoid duplicates
      const existingQueue = await dbOperations.getContentQueue(1000)
      const existingUrls = new Set(existingQueue.map(item => item.original_url))
      
      // Add new content to queue and generate AI content for top items
      let added = 0
      for (const item of scoredContent.slice(0, 10)) { // Limit to top 10 items
        if (!existingUrls.has(item.url)) {
          try {
            // Find the RSS feed ID
            const feedUrl = activeFeeds.find(feed => 
              item.url.includes(new URL(feed.url).hostname)
            )?.id
            
            // Add to content queue
            const queueItem = await dbOperations.addToContentQueue({
              original_url: item.url,
              original_title: item.title,
              original_content: item.content,
              rss_feed_id: feedUrl,
              is_selected: false,
              is_posted: false,
              priority_score: item.score
            })
            
            // Generate AI content for high-scoring items
            if (item.score > 70) {
              try {
                const generatedContent = await aiOperations.generateTweetContent(
                  item.title,
                  item.content,
                  item.url,
                  { tone: 'analytical', includePersonalTouch: true }
                )
                
                // Update the queue item with generated content
                await dbOperations.updateContentQueue(queueItem.id, {
                  ai_generated_content: generatedContent.tweetContent,
                  short_hook: generatedContent.shortHook,
                  long_hook: generatedContent.longHook,
                  personal_touch: generatedContent.personalTouch
                })
              } catch (aiError) {
                console.error(`Error generating AI content for ${item.title}:`, aiError)
              }
            }
            
            added++
          } catch (error) {
            console.error(`Error adding content item: ${item.title}`, error)
          }
        }
      }
      
      console.log(`Added ${added} new items to content queue`)
    } catch (error) {
      console.error('Error refreshing content queue:', error)
    }
  }

  async updateAnalytics() {
    try {
      // Get current user data from Twitter
      const userData = await twitterOperations.getCurrentUser()
      
      // Get recent tweets for engagement calculation
      const recentTweets = await twitterOperations.getRecentTweets()
      
      // Calculate total engagements
      let totalEngagements = 0
      for (const tweet of recentTweets) {
        if (tweet.public_metrics) {
          totalEngagements += (tweet.public_metrics.like_count || 0) +
                             (tweet.public_metrics.retweet_count || 0) +
                             (tweet.public_metrics.reply_count || 0) +
                             (tweet.public_metrics.quote_count || 0)
        }
      }
      
      // Get total impressions
      const totalImpressions = await twitterOperations.getTotalImpressions()
      
      // Store analytics in database
      await dbOperations.addAnalytics({
        followers_count: userData.followers_count,
        following_count: userData.following_count,
        total_tweets: userData.tweet_count,
        total_impressions: totalImpressions,
        total_engagements: totalEngagements
      })
      
      console.log('Analytics updated successfully')
    } catch (error) {
      console.error('Error updating analytics:', error)
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      cronJob: this.cronJob ? 'Active' : 'Inactive'
    }
  }
}

// Export singleton instance
export const twitterBotScheduler = new TwitterBotScheduler()
export default twitterBotScheduler
