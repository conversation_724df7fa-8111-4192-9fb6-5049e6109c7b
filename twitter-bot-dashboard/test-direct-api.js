#!/usr/bin/env node

/**
 * Direct test of the Twitter API functionality without Next.js server
 */

require('dotenv').config();

async function testDirectAPI() {
  console.log('🧪 Testing Twitter API Directly...\n');

  try {
    // Import the twitter auth manager
    const { twitterAuth } = require('./src/lib/twitter-auth');
    
    console.log('1️⃣ Getting authenticated Twitter client...');
    const client = await twitterAuth.getAuthenticatedClient();
    console.log('   ✅ Client obtained successfully');

    console.log('\n2️⃣ Testing user authentication...');
    const user = await client.v2.me();
    console.log(`   ✅ Authenticated as: @${user.data.username} (${user.data.name})`);

    console.log('\n3️⃣ Testing tweet posting...');
    const testContent = `🧪 Direct API test - ${new Date().toISOString().slice(0, 19)}`;
    console.log(`   📝 Content: "${testContent}"`);
    console.log(`   📏 Length: ${testContent.length} characters`);

    // Ask user if they want to actually post
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const shouldPost = await new Promise((resolve) => {
      rl.question('\n❓ Do you want to actually post this test tweet? (y/N): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });

    if (shouldPost) {
      console.log('\n🚀 Posting tweet...');
      const tweet = await client.v2.tweet(testContent);
      
      console.log('   ✅ Tweet posted successfully!');
      console.log(`   🆔 Tweet ID: ${tweet.data.id}`);
      console.log(`   🔗 Tweet URL: https://twitter.com/i/web/status/${tweet.data.id}`);
      console.log(`   📝 Content: "${tweet.data.text}"`);

      // Test saving to database
      console.log('\n4️⃣ Testing database save...');
      const { dbOperations } = require('./src/lib/supabase');
      
      await dbOperations.addPostedTweet({
        tweet_id: tweet.data.id,
        content: testContent,
        original_url: undefined,
        original_title: undefined,
        impressions: 0,
        retweets: 0,
        likes: 0,
        replies: 0,
        posted_at: new Date().toISOString()
      });
      
      console.log('   ✅ Tweet saved to database');

    } else {
      console.log('\n⏭️  Tweet posting skipped');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Token management working');
    console.log('   ✅ Authentication successful');
    console.log('   ✅ Twitter API client functional');
    if (shouldPost) {
      console.log('   ✅ Tweet posting working');
      console.log('   ✅ Database integration working');
    }
    console.log('\n🚀 Your Twitter API implementation is ready!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('\n🔍 Error details:', error);
    
    if (error.message.includes('Cannot access')) {
      console.log('\n💡 This looks like a circular dependency issue.');
      console.log('   The fix should resolve this, but there might be a remaining issue.');
    } else if (error.message.includes('invalid_grant')) {
      console.log('\n💡 The refresh token is invalid or expired.');
      console.log('   Please run the OAuth flow again.');
    } else if (error.message.includes('No Twitter tokens found')) {
      console.log('\n💡 No tokens found in database.');
      console.log('   Please complete the OAuth setup first.');
    }
  }
}

testDirectAPI().catch(console.error);
