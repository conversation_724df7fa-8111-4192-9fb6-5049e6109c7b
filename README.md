# Twitter Bot Application

A modern Twitter bot application with a separated architecture:
- **NestJS API Server**: Handles all Twitter API operations, authentication, and database management
- **Next.js Frontend**: Provides a user-friendly dashboard for managing the bot

## Architecture Overview

```
┌─────────────────┐    HTTP/REST API    ┌─────────────────┐
│   Next.js       │ ◄─────────────────► │   NestJS        │
│   Frontend      │                     │   API Server    │
│   (Port 3000)   │                     │   (Port 3001)   │
└─────────────────┘                     └─────────────────┘
                                                │
                                                ▼
                                        ┌─────────────────┐
                                        │   Supabase      │
                                        │   Database      │
                                        └─────────────────┘
```

## Features

### NestJS API Server
- **Twitter Authentication**: OAuth2 token management with automatic refresh
- **Tweet Operations**: Post tweets, fetch user data, get analytics
- **Database Operations**: Supabase integration for token storage and analytics
- **RESTful API**: Clean endpoints for frontend consumption
- **Error Handling**: Comprehensive error handling and logging
- **Health Checks**: Monitor system status

### Next.js Frontend
- **Dashboard**: Real-time Twitter analytics and statistics
- **Tweet Composer**: Direct tweet posting interface
- **RSS Feed Management**: Content aggregation and curation
- **Content Queue**: Review and schedule content
- **Responsive Design**: Mobile-friendly interface

## Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account
- Twitter Developer Account with OAuth2 app

### 1. Clone and Setup

```bash
git clone <repository-url>
cd twitterbot
```

### 2. Setup NestJS API Server

```bash
cd twitter-api-server
npm install
cp .env.example .env
```

Edit `.env` with your credentials:
```env
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
PORT=3001
```

### 3. Setup Next.js Frontend

```bash
cd ../twitter-bot-dashboard
npm install
```

Add to `.env.local`:
```env
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

### 4. Run Both Applications

**Terminal 1 - API Server:**
```bash
cd twitter-api-server
npm run start:dev
```

**Terminal 2 - Frontend:**
```bash
cd twitter-bot-dashboard
npm run dev
```

### 5. Access Applications
- Frontend: http://localhost:3000
- API Server: http://localhost:3001/api
- Health Check: http://localhost:3001/api/twitter/health

## API Endpoints

### Twitter Operations
- `POST /api/twitter/post` - Post a tweet
- `GET /api/twitter/stats` - Get Twitter statistics
- `POST /api/twitter/stats` - Refresh Twitter statistics
- `GET /api/twitter/auth/status` - Check authentication status
- `GET /api/twitter/health` - Health check

### Request/Response Examples

**Post Tweet:**
```bash
curl -X POST http://localhost:3001/api/twitter/post \
  -H "Content-Type: application/json" \
  -d '{"content": "Hello from my Twitter bot!"}'
```

**Get Stats:**
```bash
curl http://localhost:3001/api/twitter/stats
```

## Development

### Project Structure
```
twitterbot/
├── twitter-api-server/          # NestJS API Server
│   ├── src/
│   │   ├── twitter/            # Twitter module
│   │   ├── database/           # Database module
│   │   └── main.ts            # Application entry
│   └── package.json
├── twitter-bot-dashboard/       # Next.js Frontend
│   ├── src/
│   │   ├── app/               # App router pages
│   │   ├── components/        # React components
│   │   └── lib/              # Utilities
│   └── package.json
└── README.md
```

### Adding New Features

1. **API Changes**: Modify NestJS controllers and services
2. **Frontend Changes**: Update React components and API calls
3. **Database Changes**: Update Supabase schema and service methods

## Deployment

### Production Environment Variables

**API Server (.env):**
```env
NODE_ENV=production
PORT=3001
TWITTER_CLIENT_ID=prod_client_id
TWITTER_CLIENT_SECRET=prod_client_secret
SUPABASE_URL=prod_supabase_url
SUPABASE_ANON_KEY=prod_supabase_key
```

**Frontend (.env.local):**
```env
NEXT_PUBLIC_API_URL=https://your-api-domain.com/api
```

### Docker Deployment (Optional)

Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  api:
    build: ./twitter-api-server
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
  
  frontend:
    build: ./twitter-bot-dashboard
    ports:
      - "3000:3000"
    depends_on:
      - api
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure API server allows frontend origin
2. **Token Refresh Failures**: Check Twitter app permissions
3. **Database Connection**: Verify Supabase credentials
4. **Port Conflicts**: Change ports in environment files

### Logs and Debugging

- API Server logs: Check console output for detailed error messages
- Frontend logs: Open browser developer tools
- Database logs: Check Supabase dashboard

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes following the existing patterns
4. Test both API and frontend
5. Submit a pull request

## License

MIT License - see LICENSE file for details
