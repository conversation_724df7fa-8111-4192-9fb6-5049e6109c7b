#!/bin/bash

# Twitter Bot Application Setup Script
echo "🚀 Setting up Twitter Bot Application..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

print_success "Node.js $(node -v) detected"

# Setup NestJS API Server
print_status "Setting up NestJS API Server..."
cd twitter-api-server

if [ ! -f "package.json" ]; then
    print_error "package.json not found in twitter-api-server directory"
    exit 1
fi

print_status "Installing API server dependencies..."
npm install

if [ $? -ne 0 ]; then
    print_error "Failed to install API server dependencies"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating .env file from template..."
    cp .env.example .env
    print_warning "Please edit twitter-api-server/.env with your actual credentials"
else
    print_success ".env file already exists"
fi

# Build the API server
print_status "Building API server..."
npm run build

if [ $? -ne 0 ]; then
    print_error "Failed to build API server"
    exit 1
fi

print_success "API server setup complete"

# Setup Next.js Frontend
print_status "Setting up Next.js Frontend..."
cd ../twitter-bot-dashboard

if [ ! -f "package.json" ]; then
    print_error "package.json not found in twitter-bot-dashboard directory"
    exit 1
fi

print_status "Installing frontend dependencies..."
npm install

if [ $? -ne 0 ]; then
    print_error "Failed to install frontend dependencies"
    exit 1
fi

# Check if .env.local exists and has API URL
if [ ! -f ".env.local" ]; then
    print_status "Creating .env.local file..."
    echo "NEXT_PUBLIC_API_URL=http://localhost:3001/api" > .env.local
    print_warning "Please add your other environment variables to twitter-bot-dashboard/.env.local"
elif ! grep -q "NEXT_PUBLIC_API_URL" .env.local; then
    print_status "Adding API URL to .env.local..."
    echo "NEXT_PUBLIC_API_URL=http://localhost:3001/api" >> .env.local
fi

print_success "Frontend setup complete"

# Return to root directory
cd ..

# Create start scripts
print_status "Creating start scripts..."

# Create start-api.sh
cat > start-api.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting NestJS API Server..."
cd twitter-api-server
npm run start:dev
EOF

# Create start-frontend.sh
cat > start-frontend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Next.js Frontend..."
cd twitter-bot-dashboard
npm run dev
EOF

# Create start-all.sh
cat > start-all.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Twitter Bot Application..."

# Function to cleanup background processes
cleanup() {
    echo "Stopping all processes..."
    kill $(jobs -p) 2>/dev/null
    exit
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start API server in background
echo "Starting API Server..."
cd twitter-api-server
npm run start:dev &
API_PID=$!

# Wait a moment for API to start
sleep 3

# Start frontend in background
echo "Starting Frontend..."
cd ../twitter-bot-dashboard
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ Both applications are starting..."
echo "📊 Frontend: http://localhost:3000"
echo "🔧 API Server: http://localhost:3001/api"
echo "🏥 Health Check: http://localhost:3001/api/twitter/health"
echo ""
echo "Press Ctrl+C to stop both applications"

# Wait for background processes
wait
EOF

# Make scripts executable
chmod +x start-api.sh start-frontend.sh start-all.sh

print_success "Start scripts created"

# Final instructions
echo ""
print_success "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Edit twitter-api-server/.env with your Twitter and Supabase credentials"
echo "2. Edit twitter-bot-dashboard/.env.local with your environment variables"
echo "3. Run the application:"
echo ""
echo "   Option 1 - Start both applications:"
echo "   ./start-all.sh"
echo ""
echo "   Option 2 - Start individually:"
echo "   ./start-api.sh     (in one terminal)"
echo "   ./start-frontend.sh (in another terminal)"
echo ""
echo "📊 Access points:"
echo "   Frontend: http://localhost:3000"
echo "   API: http://localhost:3001/api"
echo "   Health Check: http://localhost:3001/api/twitter/health"
echo ""
print_warning "Make sure to configure your environment variables before starting!"
