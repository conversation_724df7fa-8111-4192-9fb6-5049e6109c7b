// auth.mjs
import express from 'express';
import { TwitterApi } from 'twitter-api-v2';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const app = express();
const PORT = 3000;

// Create Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

const client = new TwitterApi({
  clientId: process.env.TWITTER_CLIENT_ID,
  clientSecret: process.env.TWITTER_CLIENT_SECRET,
});

const callbackUrl = `http://localhost:${PORT}/callback`;

const { url, codeVerifier, state } = client.generateOAuth2AuthLink(callbackUrl, {
  scope: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
});

console.log('\u2728 Visit this URL to authorize the app:');
console.log(url);

app.get('/callback', async (req, res) => {
  const { state: returnedState, code } = req.query;

  if (state !== returnedState) {
    return res.status(400).send('Invalid state');
  }

  try {
    const { client: userClient, accessToken, refreshToken } = await client.loginWithOAuth2({
      code,
      codeVerifier,
      redirectUri: callbackUrl,
    });

    console.log('\n🎉 Access Token:', accessToken);
    console.log('🔁 Refresh Token:', refreshToken);

    // Test authentication
    console.log('\n🔍 Testing authentication...');
    const user = await userClient.v2.me();
    console.log(`👤 Authenticated as: @${user.data.username} (${user.data.name})`);

    // Save tokens to database
    console.log('\n💾 Saving tokens to database...');
    try {
      const { error } = await supabase
        .from('twitter_tokens')
        .insert({
          access_token: accessToken,
          refresh_token: refreshToken,
          token_type: 'bearer',
          scope: 'tweet.read tweet.write users.read offline.access',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('❌ Failed to save tokens to database:', error.message);
        res.send('✅ Authorization successful! Tokens received but not saved to database. Check console for details.');
      } else {
        console.log('✅ Tokens saved to database successfully!');
        res.send('✅ Authorization successful! Tokens saved to database. You can close this tab.');
      }
    } catch (dbError) {
      console.error('❌ Database error:', dbError.message);
      res.send('✅ Authorization successful! Tokens received but database save failed. Check console for details.');
    }

  } catch (err) {
    console.error('Error getting tokens:', err);
    res.status(500).send('Failed to get tokens');
  }
});

app.listen(PORT, () => {
  console.log(`\nWaiting for OAuth redirect at http://localhost:${PORT}/callback ...`);
});
